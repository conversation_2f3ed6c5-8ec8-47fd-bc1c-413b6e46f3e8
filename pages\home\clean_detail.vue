<template>
	<view>
		<view class="weui-cells weui-cells_after-title">
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">消毒开始时间</view>
				<view class="weui-cell__ft weui-flex" style="align-items: center;">{{objclean.starttime || ''}}</view>
			</view>
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">消毒时间（分钟）</view>
				<view class="weui-cell__ft">{{objclean.duration}}</view>
			</view>
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">物品名称</view>
				<view class="weui-cell__ft rtview">{{objclean.wp_name}}</view>
			</view>
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">物品数量</view>
				<view class="weui-cell__ft">{{objclean.num}}</view>
			</view>
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">单位</view>
				<view class="weui-cell__ft">{{objclean.unit}}</view>
			</view>
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">消毒方式</view>
				<view class="weui-cell__ft rtview">{{objclean.method}}</view>
			</view>
			<!-- <view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">操作员</view>
				<view class="weui-cell__ft">胡玉春</view>
			</view> -->
		</view>
		<view class="remarks-list">
			<view class="font14 martop">图片/视频附件</view>
			<view class="weui-uploader__bd" style="margin: 10px 0 0 0;">
				<view class="weui-uploader__files" style="padding-top: 5px;">
					<block v-if="objclean.arrurl.length > 0">
						<view v-for="(item, i) in objclean.arrurl" :key="i" class="weui-uploader__file">
							<video v-if="item.url.endsWith('.mp4')" :src="ossPrefix + item.url" class="img-upload"></video>
							<image v-else :src="ossPrefix + item.url" class="img-upload"></image>
						</view>
					</block>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	var dateUtil = require("@/common/util.js");
	export default {
		data() {
			return {
				ossPrefix: app.globalData.ossPrefix,
				today: dateUtil.getStrByDate(new Date()),
				id: 0,
				objclean: {
					starttime: '',
					duration: '',
					wp_name: '',
					num: 0,
					unit: '',
					method: '',
					attach: '',
					arrurl: []
				}
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			var that = this;
			this.id = options.id;
			this.initData();
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			initData() {
				var _this = this;
				if(!this.id){
					return;
				}
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg("系统错误");
					}
					re.arrurl = JSON.parse(re.attach);
					_this.objclean = re;
				}, ["home.clean.detailbyid", this.id], { route: uni.svs.zxx })
			}
		}
	};
</script>


<style>
	page {
		background: #F2F4F5;
	}

	.yllist {
		background: #fff;
	}
	
	.yltit {
		background: #F8F8F8;
		border-radius: 0px 42px 42px 0px;
		display: inline-block;
		font-size: 24rpx;
		color: #303030;
		padding: 5px 10px;
		font-weight: bold;
	}
	
	.ylmain {
		background: #fff;
		padding: 10px 10px;
		overflow: hidden;
	}
	
	.yltit text {
		color: #666666;
	}
	
	.ylleft {
		width: 100%;
		float: left;
		font-size: 14px;
		color: #999999;
		margin: 5px 0;
	}
	.staple {
		display: inline-flex;
		border: 1px solid #eee;
	}
	
	
	page {
		background: #F2F4F5;
	}
	
	.weui-cell {
		padding: 15px;
	}
	
	.weui-cell .weui-cell__bd {
		font-size: 14px;
		color: #303030;
	}
	
	.weui-cell .weui-cell__ft {
		font-size: 14px;
		color: #999999;
	}
	
	.weui-cell:before {
		right: 15px;
	}
	
	.weui-cells:after {
		right: 15px;
		left: 15px;
	}
	
	.inputbg {
		text-align: left;
		background: #F8F8F8;
		border-radius: 3px;
		padding: 5px 10px;
		font-size: 24rpx;
		color: #303030;
		line-height: 60rpx;
		height: 60rpx;
		margin-top: 10px;
	
	}
	
	.uni-input {
		text-align: left;
		font-size: 24rpx;
		color: #999999;
		line-height: 60rpx;
		height: 60rpx;
	}
	
	.remarks-list {
		background: #fff;
		margin-top: 10px;
		padding: 15px;
	}
	
	.weui-uploader__input-box {
		width: 216rpx;
		background: #F7F9FC !important;
		height: 216rpx;
	}
	
	.weui-uploader__file {
		position: relative;
		width: 216rpx;
		height: 216rpx;
	}
	
	.weui-uploader__input-box {
		box-sizing: border-box;
		border: 1px solid #eef1f2;
		background: #f9f9f9;
	
	}
	
	.img-upload {
		width: 216rpx;
		height: 216rpx;
	
		display: block;
	
	}
	
	.img-close {
		width: 22rpx;
		height: 22rpx;
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		z-index: 111;
	}
	
	.uni-combox {
		border: none;
		line-height: 60rpx;
		height: 60rpx;
		padding: 0 8px 0 0;
	}
	
	.weui-uploader__input-box:after,
	.weui-uploader__input-box:before {
		background-color: transparent;
	}
</style>
