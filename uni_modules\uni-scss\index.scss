@import './styles/index.scss';
@import '../../style/iconfont.scss';
@import '../../style/weui.scss';
/*按钮*/
button.noafter::after{content: none;}
.fullbtn{font-size: 30rpx;border-radius: 40px;color:#ffffff;background: #C23C43;margin: 0;padding: 6rpx 0;}  
.fullbtn.graybg{background: #CCCCCC;}
.borderbtn{border: 1px solid #eeeeee;font-size: 26rpx;color: #cccccc;background: #ffffff;border-radius: 30px;margin: 0;}
.borderbtn.borblue{color: #6f9eff;border: 1px solid #6f9eff;}
.borderbtn.borgray{color: #281517;border: 1px solid #CFCFCF;}
.borderbtn.borgreen{background: #d9f7ef;border: 1px solid #04c8ad;color: #04c8ad;}
.fullbtn.borcoffce{color: #CF9155 !important;border: 1px solid #CF9155; background: #fff !important;}
.fullbtn.borcoffce.current{background:#CF9155 !important;color: #fff !important;}
/*flex布局*/
.weui-flex{display: -webkit-box;display: -webkit-flex;display: flex;}
.weui-flex_center{display: -webkit-box;display: -webkit-flex;display: flex;justify-content: center;align-items: center;}
.weui-flexcen{display: -webkit-box;display: -webkit-flex;display: flex; align-items: center}
.weui-flex__item{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;}
/*搜索框*/
.weui-search-bar{
  border: none;
  background: #fff;
  margin-bottom: 0px;
  align-items: center;
  padding: 24rpx 30rpx;
}
.weui-search-bar__box{
  border-radius: 15px;
  align-items: center;
}
.weui-search-bar__box .icon_search,.weui-search-bar__box .icon_close{
  position: absolute;
  width: 17px;
  height: 17px;
}
.weui-search-bar__box .icon_close:after{
  color: #666666;
  font-size: 17px;
}
.weui-search-bar__form{
 background: #F7F7F7;
  border: none;
  border-radius: 20px;
}
.weui-search-bar_btn{
  color: #04c8ad;
  font-size: 14px;
  margin-left: 10px;
}
.weui-search-bar__input{
  padding: 1px 0;
}
/*文本*/
.font12gray{font-size: 24rpx;color: #a7a8a9;}
.font12block{color: #281517;font-size: 24rpx; font-weight: bold;}
.font14block{color: #281517;font-size: 14px; font-weight: bold;}
.font16{color: #303030; font-size:16px;}
.font16fw{font-size:16px;font-weight: bold;color: #303030;}
.font12{font-size: 24rpx;}
.font15{font-size: 30rpx;}
.font14{font-size: 28rpx;}
.redtxt{color: #CD5642 !important;}
.fwbold{font-weight: bold;}
.coffeecolor{color: #CF9155;}
.garytxt{color: #827E7B;}
.margin10{ margin-left: 10px;}
.marginrt10{ margin-right: 10px;}
.boldtxt{font-weight: bold;}
.blocktxt{font-weight:normal;color: #303030;}
.graytit{color: #999999;}
.redtit{font-size: 28rpx;color: #C23C43;font-weight: bold;}
/*内容背景*/
.panel-view{background: #ffffff;margin: 30rpx 30rpx;border-radius: 10rpx;position: relative;box-shadow:2px 2px 10px 0px #e8e8ea;}
.panel-view.greenshadow{box-shadow: 0px 0px 14px 3px #ecf9f7;}
/*渐变*/
.gradient-red{
	background: -webkit-linear-gradient(left, #ff9e74 , #ff7b7b); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #ff9e74, #ff7b7b); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #ff9e74, #ff7b7b); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #ff9e74 , #ff7b7b); /* 标准的语法 */
}
.gradient-red2{
	background: -webkit-linear-gradient(left, #ff7c60 , #ffb17d); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #ff7c60, #ffb17d); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #ff7c60, #ffb17d); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #ff7c60 , #ffb17d); /* 标准的语法 */
}
.gradient-green{
	background: -webkit-linear-gradient(left, #e6fff2 , #ccf4d9); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #e6fff2, #ccf4d9); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #e6fff2, #ccf4d9); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #e6fff2 , #ccf4d9); /* 标准的语法 */
}
.gradient-orange{
	background: -webkit-linear-gradient(left, #fef4e0 , #fdd3c4); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #fef4e0, #fdd3c4); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #fef4e0, #fdd3c4); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #fef4e0 , #fdd3c4); /* 标准的语法 */
}
.gradient-blue{
	background: -webkit-linear-gradient(left, #dfedfc , #ccf2fb); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #dfedfc, #ccf2fb); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #dfedfc, #ccf2fb); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #dfedfc , #ccf2fb); /* 标准的语法 */
}
.gradient-purple{
	background: -webkit-linear-gradient(left, #ffffff , #e9eeff); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #ffffff, #e9eeff); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #ffffff, #e9eeff); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #ffffff , #e9eeff); /* 标准的语法 */
}
.gradient-white{
	background: -webkit-linear-gradient(rgba(255,255,255,0) , #ffffff); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(rgba(255,255,255,0) , #ffffff); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(rgba(255,255,255,0) , #ffffff); /* Firefox 3.6 - 15 */
    background: linear-gradient(rgba(255,255,255,0) , #ffffff); /* 标准的语法 */
}
/*分割线*/
.border-rgt{position: relative;}
.border-rgt:after{content: "";border-right: 1px solid #eeeeee;height: 40%;position: absolute;right: 0;top:35%;}
.border-rgt:last-child::after{content: none;}
/*切换*/
.tab-common{font-size: 30rpx;color: #787878;padding: 20rpx 30rpx;align-items: center;overflow-x: auto;white-space: nowrap;}
.tab-common { overflow: -moz-scrollbars-none; }
.tab-common { -ms-overflow-style: none; }
.tab-common::-webkit-scrollbar { width: 0 !important }
.tab-common>view{position: relative;margin: 0 25rpx;height: 80rpx;line-height: 76rpx;}
.tab-common>view.current{color: #2188db;font-size: 30rpx;margin-top: -6rpx;}
.tab-common>view.current:after{content: "";width: 48rpx;height: 4rpx;background: #6f9eff;position: absolute;left: 50%;margin-left: -24rpx;bottom: 8rpx;border-radius: 10px;}
.tab-common.orange-tab{font-size: 30rpx;color: #333333;border-bottom: 1px solid #eeeeee;padding: 26rpx;}
.tab-common.orange-tab>view.current{font-size: 34rpx;color: #ff9c2c;margin-top: 0;}
.tab-common.orange-tab>view.current:after{background: #ff9c2c;bottom: -26rpx;}
.tab-common.blueline-tab>view.current{background: url(static/image/tab_line.png) no-repeat center 65rpx;background-size: 42rpx 14rpx;font-size: 36rpx;color: #323334;}
.tab-common.blueline-tab>view.current:after{content: none;}

.tab-common2{background: #ffffff;color: #d5d5d5;font-size: 9px;height: 30rpx;line-height: 30rpx;font-weight: normal;}
.tab-common2>view{width: 50rpx;text-align: center;}
.tab-common2>view.current{background: #2188db;color: #ffffff;border-radius: 2rpx;}
.tab-common3{font-size: 10px;color: #cfcfcf;}
.tab-common3>view{background: #ffffff;margin: 0 10rpx;padding: 2rpx 16rpx;min-width: 68rpx;border-radius: 2rpx;}
.tab-common3>view.current{background: #2188db;color: #ffffff;}
/*底部操作*/
.common-btn::after, .btn-gray-def::after {
  border: none;
}
.common-btn {
  background: #C23C43;
  border-radius: 10rpx;
  color: #fff;
  border: none;
  /* box-shadow: 0 0 25px rgba(21, 221, 182, 0.6); */
  line-height: 2.55555556;
  -webkit-tap-highlight-color: transparent;
  /* font-family: '宋体'; */
  margin: 30rpx 50rpx;
  font-size: 30rpx;
  padding: 6rpx 0;
}
 .common-btn.oprbor-btntk{background: rgba(205,86,66,0.08); color: #CD5642;}
  
  
  /*底部操作*/
  .button-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    text-align: center;
    font-size: 15px;
    box-shadow: 0 0px 8px 3px #f8f8f8;
    height: 100rpx;
	z-index: 999;
    }
  .button-bottom .oprbor-btn{width: 240rpx;font-size: 30rpx;color: #60cabb;height: 80rpx;margin: 0 10rpx;}
  .button-bottom .default-btn{height: 80rpx;margin: 0 10rpx;background: #60cabb;color: #ffffff;font-size: 30rpx;border-radius: 50px;}  
  .step-btn{width: 320rpx;height: 100%;border-radius: 0;background: #60cabb;color: #ffffff;font-size: 30rpx;margin: 0;}
    .button-bottom .redbtn{  background: #FFECEF;color: #C23C43;
border-radius: 10px 10px 10px 10px;
border: 1px solid #C23C43;}

  
/*阴影*/
.shadow-view{background:rgba(0,0,0,0.5);position:fixed;top:0;bottom:0;width:100%;display:flex;align-items:center;justify-content:center;z-index:9999;flex-direction: column;}
.shadow-content{position: relative;background: #ffffff;width: 88%;padding: 20rpx 0 15rpx 0;border-radius: 6px;}
.bodyshape-title{font-size: 17px;color: #1b1b1b;text-align: center;height: 80rpx;line-height: 70rpx;border-bottom: 1px solid #EDEDEF;height: 100rpx;line-height: 100rpx;border-radius: 6px 6px 0 0;}
.box-btn{height: 90rpx;line-height: 90rpx;color: #666666;font-size: 16px;border-top: 1px solid #dfdfdf;text-align: center;border-right: 1px solid #dddddd;}
.box-view .box-btn:last-child{border-right: none;}
/*弹框-底部*/
.shadow-view.box-btm{justify-content: flex-end;}
.shadow-view.box-btm .shadow-content{width: 100%;border-radius: 0;}
.shadow-view.box-btm .box-btn{height: 100rpx;line-height: 100rpx;background: #f5f6f7;border-top: 0;font-size: 16px;color: #323334;}
  
  
/*遮罩层/弹框*/
.shadow-con{position: fixed;top: 0;bottom: 0;left: 0;right: 0;background: rgba(0,0,0,0.5);z-index: 9;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;}
.drop-down .shadow-box {
  background: #fff;
  width: 90%;
  right: 0;
  top: 43px;
  height: 100%;
  border-radius: 0;
  margin: 0;
  position: absolute;
  padding: 0px 0 50px 0px;
  overflow-y: scroll;
}	
.shadow-box {
  font-size: 13px;
  color: #333333;
}

.screen-sellist{
  padding-bottom: 3px;
  overflow: hidden;
}
.screen-sellist view {
  width: 33.3%;
  line-height: 30px;
  height: 30px;
  margin-top: 15px;
  text-align: center;
  float: left;
  font-size: 14px;
}
.screen-selbot {
  clear: both;
  padding: 0 30rpx;
  overflow: hidden;
  margin-top: 30rpx;
}
.filter-lt{font-weight: bold;}

.screen-sellist view text {
	display: block;
	background: #F8F8F8;
	border-radius: 28px;
	margin-right: 10px;
	color: #A7A8A9;
}
.screen-sellist view text.current {
	background: #FFECEF;
    border: 1px solid #C23C43;
	color: #C23C43;
}
.screen-sellist view.current text {
    background: #FFECEF;
    border: 1px solid #C23C43;
	color: #C23C43;
	
}

.uni-input-input, .uni-input-placeholder{line-height: 30px;
	font-size: 12px;
	margin-left: 5px;}
.custom-list {
	background: #F8F8F8;
	border-radius: 20px;
	text-align: center;
	flex: 1;
    height: 30px;
    padding-left: 10px;
}
.custom-time .uni-input-input {
	line-height: 30px;
	font-size: 12px;
	margin-left: 5px;
}



.custom-time text {
	color: #A7A8A9;
	color: #A7A8A9;
}

.weui-btn {
  position:relative;
  display:block;
  margin-left:auto;
  margin-right:auto;
  padding-left:14px;
  padding-right:14px;
  box-sizing:border-box;
  font-size:18px;
  text-align:center;
  text-decoration:none;
  color:#fff;
  line-height:2.55555556;
  border-radius:5px;
  -webkit-tap-highlight-color:rgba(0,0,0,0);
  overflow:hidden
}
.weui-btn:after {
  content:" ";
  width:200%;
  height:200%;
  position:absolute;
  top:0;
  left:0;
  -webkit-transform:scale(.5);
  transform:scale(.5);
  -webkit-transform-origin:0 0;
  transform-origin:0 0;
  box-sizing:border-box;
  border-radius:10px
}
/*checkbox默认样式重置*/
 .checkbox_style {
  margin: 4rpx 15rpx 0 0;
  position: relative;
  width: 14px;
  height: 14px;
}
 .checkbox_style .checkbox_label {
  display: block;
  padding: 0 20rpx;
  height: 34rpx;
  line-height: 34rpx;
}
.checkbox_style checkbox {
  box-sizing: border-box;
  background: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 14px;
  height: 14px;
}
.checkbox_style checkbox .wx-checkbox-input {
  margin-right: 15rpx;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: none;
  box-sizing: border-box;
  border-radius: 50%;
}
.checkbox_style checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border-color: #C23C43;
  overflow: hidden;
  box-sizing: border-box;
  border-radius: 50%;
}
 .checkbox_style checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  padding: 0 22rpx;
  line-height: 34rpx;
  text-align: center;
  font-size: 8px;
  color: #fff;
  background: #C23C43;
  transform: translate(-50%, -50%) scale(1);
  -webkit-transform: translate(-50%, -50%) scale(1);
  border-radius: 50%;
}
.checkbox_style.checkbox_orange checkbox .wx-checkbox-input{
  border: 1px solid #ffffff;
}
.checkbox_style.checkbox_orange checkbox{
  width: 30rpx;
  height: 30rpx;
}
.checkbox_style.checkbox_orange checkbox .wx-checkbox-input{
  border-radius: 50%;
}
.checkbox_style.checkbox_orange checkbox .wx-checkbox-input.wx-checkbox-input-checked{
  background-color:#ffffff;
  border-color:#ffffff;
}
.checkbox_style.checkbox_orange checkbox .wx-checkbox-input.wx-checkbox-input-checked::before{
  color: #CF9155;
  background: #ffffff;
}



/*radio默认样式重置*/
.radio_style .uni-radio-input{
	width: 28rpx!important;
	height: 28rpx!important;
	margin: -8rpx 10rpx 0 0;
}
uni-radio .uni-radio-input.uni-radio-input-checked:before{
	font-size: 20rpx;
}
uni-radio:not([disabled]) .uni-radio-input:hover{
	border-color: #C23C43;
}
uni-radio .uni-radio-input{
	width: 28rpx;
	height: 28rpx;
}
.uni-radio-input.uni-radio-input-checked{
	width: 28rpx;
	height: 28rpx;
	background-color: #C23C43!important;
	border-color: #C23C43!important;
}

/*上传*/
.weui-cell__bd{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;min-width: 0;}
.weui-uploader{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;}
.weui-uploader__hd{   
	display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    padding-bottom: 16px;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;}
.weui-uploader__title{
	-webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
	}
.weui-uploader__bd{
	margin-bottom: -8px;
	margin-right: -8px;
	overflow: hidden;
}	
.weui-uploader__files{
	list-style: none;
}
.weui-uploader__file{
	float: left;
	margin-right: 8px;
	margin-bottom: 8px;
	width: 96px;
	height: 96px;
	background: no-repeat center center;
	background-size: cover;
}	
.weui-uploader__file_status{
	position: relative;
}
.weui-uploader__file-content{
	display: none;
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%,-50%);
	transform: translate(-50%,-50%);
	color: #fff;
	color: var(--weui-WHITE);
}	
.weui-uploader__file_status .weui-uploader__file-content{
	display: block;
}	
.weui-uploader__file-content .weui-icon-warn{
	display: inline-block;
}	
.weui-uploader__input-box{
	float: left;
	position: relative;
	margin-right: 8px;
	margin-bottom: 8px;
	width: 96px;
	height: 96px;
	box-sizing: border-box;
	background-color: #ededed;
}	
.weui-uploader__input{
	position: absolute;
	z-index: 1;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
	-webkit-tap-highlight-color: rgba(0,0,0,0);
}	
/*省略号*/	
.text-elli{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}	

.tab-common10{justify-content: space-around;font-size: 30rpx;color: #281517;padding:30rpx 0rpx 20rpx 0rpx;align-items: center;margin: 0 -20rpx;}
.tab-common10>view{position: relative;margin: 0 20rpx;min-width: 80rpx;text-align: center;}
.tab-common10>view.current{color: #C23C43;font-size: 30rpx;}
.tab-common10>view.current:after{content: "";width: 48rpx;height: 4rpx;background: #C23C43;position: absolute;left: 50%;bottom: -20rpx;transform: translate(-50%,0);border-radius: 20rpx;}
	
	
.ordedetails {
		font-size: 26rpx;
		margin: 0rpx 30rpx;
		color: #827E7B;
	}
	.pad160{padding-bottom:160rpx}
	
	/* 底部左右2个按钮 */
	.common-btn.opr-btnleft {    height: 44px;
	    line-height: 44px;
	    font-size: 16px;
	    border-radius: 30px 0 0 30px; padding: 0 20px;background: rgba(205,86,66,0.08); color: #CD5642;}
	.common-btn.opr-btnright { height: 44px;
		    line-height: 44px;
		    font-size: 16px;
		   border-radius: 0 30px 30px 0; padding: 0 20px; background: #CD5642;}
		   
	.bodyshape-title{font-size: 32rpx;color: #1b1b1b;text-align: center;height: 80rpx;line-height: 70rpx;border-bottom: 1px solid #EDEDEF;height: 100rpx;line-height: 100rpx;border-radius: 6px 6px 0 0;}
/* 弹框关闭按钮*/
.closeimg {
    position: absolute;
    top: 0;
    right: 30rpx;
}
/* 列表*/
.reginstra-list {
  padding: 10px 0rpx;
  font-size: 28rpx;
  color: #281517; 
 
}
.bnum {
  width: 150rpx;
 color: #827E7B;
}
.borbottom{border-bottom: 1px solid #EBEBEB; margin: 0 30rpx;}
/*暂无内容*/
	.noinfo-view {
		flex-direction: column;
		padding: 100rpx 0;
		font-size: 14px;
	  color: #AFAFAF;
	}
	
.nomesssage{    
		position: absolute;            
		top:50%;            
		left:50%;            
		width:100%;            
		transform:translate(-50%,-50%);  
		text-align: center;     
	}	
.nomesssage-txt{font-size: 24rpx;color: #999999; display:block;}	
.greentxt{color: #33B596 !important;}
.redtxt{color: #FF6F6F;}
.graytxt{color:#666666; margin-left: 2px;}
.graytxt{color:#999;}
.yellowtxt{color:#EB7D1C;}

/*背景底部带颜色*/
.toplinebg{box-shadow: 0px 0px 16px 0px  rgba(194, 60, 67, 0.12);   }


.sel-person-list{position:relative;font-size: 14px;height: 40px;display: flex;display: -webkit-flex;align-items: center;justify-content: space-between;color: #303030;}
.sel-person-list:before{content: "";position: absolute;height: 18px;width: 3px;background: #C23C43; left:-15px}
/*距离*/
.marbot10{margin-bottom: 10px;}


/*下划线列表*/
.yllist {
		background: #fff;
	}

	.yltit {
		background: #F8F8F8;
		border-radius: 0px 42px 42px 0px;
		display: inline-block;
		font-size: 24rpx;
		color: #303030;
		padding: 5px 10px;
		font-weight: bold;
	}

	.ylmain {
		background: #fff;
		padding: 10px 10px;
		overflow: hidden;
	}

	.yltit text {
		color: #666666;
	}

	.ylleft {
		width: 50%;
		float: left;
		font-size: 14px;
		color: #999999;
		margin: 5px 0;
	}

	.ritime {
		font-size: 14px;
		color: #999999;
		padding: 10px 15px;
		
	}
	/* 公用颜色 */
.greentxt{color: #33B596;}


/* 默认按钮的颜色 */
	.wx-checkbox-input-checked,
	.wx-radio-input-checked,
	.wx-switch-input-checked {
		background-color: #C23C43 !important;
		border-color: #C23C43 !important;
		color: #fff !important;}
		
	radio,checkbox{transform:scale(.7) }


.weui-cell .weui-cell__ft.rtview,.weui-cell .rtview{width: 78%; text-align: right;}


/*搜索样式*/
	.filter-results {
		background: #fff;
		padding: 0 15px 10px 15px;
		font-size: 24rpx;
	}
	
	.filter-results view {
		display: inline-block;
		background: #FFEDEF;
		border-radius: 10rpx;
		margin-right: 10px;
		color: #C23C43;
		padding: 5rpx 0rpx 5rpx 10rpx;
		font-size: 22rpx;
		font-weight: normal;
		cursor: pointer;
		
	}
	
	
	.icon_close2:after {
		color: #C23C43;
		font-size: 16rpx;
		vertical-align: middle;
		margin-left: 10px;
	}
	.history-txt{ padding: 10px 15px; background: #fff; margin-top: 8px;font-size: 28rpx;color: #303030;}
	
	/* 筛选颜色 */
	
	.droptxt {
			padding: 0 8rpx;
			height: 30rpx;
			line-height: 30rpx;
			background: #C23C43;
			border-radius: 12px;
			font-size: 20rpx;
			color: #FFFFFF;
			text-aviewgn: center;
			margin-left: 3px;
			font-weight: normal;
		}