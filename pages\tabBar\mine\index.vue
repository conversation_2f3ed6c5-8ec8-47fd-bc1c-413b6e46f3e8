<template>
	<view>
		<view class="top-bgview">
			<image src="/static/image/home/<USER>" style="width: 100%;height: 393rpx;"></image>
		</view>
		<view style="position: absolute; top: 160rpx;z-index: 1; left:30rpx; right: 30rpx;">
			<image src="/static/image/home/<USER>" style="width: 100%;height: 431rpx;"></image>
			<view style="position: absolute; top: 50rpx;z-index: 1; left:30rpx; right: 30rpx; text-align: center;">
				<view @click="toMyinfo">
					<image src="/static/image/home/<USER>" style="width: 116rpx;height: 116rpx;"></image>
					<view>
						{{myinfo.truename || myinfo.nickname || myinfo.mobile || ""}}
					</view>
				</view>
				<view class="identitylist weui-flex_center">
					<view @click="toMybabys" class="identitytxt">
						<image src="/static/image/home/<USER>" style="width: 73rpx;height:72rpx;"></image>
						<text>我的孩子</text>
					</view>
					<view class="identitytxt">
						<image src="/static/image/home/<USER>" style="width: 73rpx;height:72rpx;"></image>
						<text>我的身份 - {{pname || '无'}}</text>
					</view>
				</view>
			</view>

			<view class="helplist">
				<view class="weui-cell" @click="toHelpCenter">
					<view class="weui-cell__bd weui-flex" style="align-items: center;">
						<view class="weui-flex_center" style="width: 50rpx;margin-right: 20rpx;">
							<image src="/static/image/home/<USER>" style="width: 48rpx;height:48rpx;"></image>
						</view>
						<view>帮助中心</view>
					</view>
					<view class="weui-cell__ft weui-cell__ft_in-access">

					</view>
				</view>
				<view class="weui-cell" @click="Subscribe()">
					<view class="weui-cell__bd weui-flex" style="align-items: center;">
						<view class="weui-flex_center" style="width: 50rpx;margin-right: 20rpx;">
							<image src="/static/image/home/<USER>" style="width: 48rpx;height:48rpx;"></image>
						</view>
						<view>订阅消息{{subscribecount?"+"+subscribecount:""}}</view>
					</view>
					<view class="weui-cell__ft weui-cell__ft_in-access">

					</view>
				</view>
				<!-- <view class="weui-cell">
					<view class="weui-cell__bd weui-flex" style="align-items: center;">
						<view class="weui-flex_center" style="width: 50rpx;margin-right: 20rpx;">
							<image src="/static/image/home/<USER>" style="width: 48rpx;height:48rpx;"></image>
						</view>
						<view>意见反馈</view>
					</view>
					<view class="weui-cell__ft weui-cell__ft_in-access">

					</view>
				</view>
				<view class="weui-cell">
					<view class="weui-cell__bd weui-flex" style="align-items: center;">
						<view class="weui-flex_center" style="width: 50rpx;margin-right: 20rpx;">
							<image src="/static/image/home/<USER>" style="width: 48rpx;height:48rpx;"></image>
						</view>
						<view>设置</view>
					</view>
					<view class="weui-cell__ft weui-cell__ft_in-access">

					</view>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>
    import MessageUtil from '../../../util/sendMsg.js';
	var moment = require("../../../common/moment.min.js");
	var util = require("../../../common/util.js")
	var app = getApp();
	
	export default {
		data() {
			return {
				myinfo: app.globalData.objuserinfo||{},
				arrptype: [
					{ptype: 1, pname: '爸爸'},
					{ptype: 2, pname: '妈妈'},
					{ptype: 3, pname: '爷爷'},
					{ptype: 4, pname: '奶奶'},
					{ptype: 5, pname: '姥爷'},
					{ptype: 6, pname: '姥姥'}
				],
				pname: '',
				subscribecount: 0
			}
		},
		onLoad(params) {
			this.initData();
		},
		onShow() {
		
		},
		methods: {
			initData(){
				var ptype = this.myinfo.ptype;
				var arrptype = this.arrptype;
				for (var i = 0; i < arrptype.length; i++) {
					if(arrptype[i].ptype == ptype){
						this.pname = arrptype[i].pname;
						break;
					}
				}

				this.getSubscribecount();
			},
			//获取订阅数量
			getSubscribecount(){
				var _this = this;
				var templateid = MessageUtil.getTemplateId('xcxhome', 1);
				uni.smaction(function (re, err) {
					if (err) {
						return uni.msg("获取订阅数量失败" + err);
					}
					_this.subscribecount = re.subcount;
				}, { openid: app.globalData.objuserinfo.openid_xcx, templateid: templateid }, { route: uni.svs.zxx_home_biz, action: "message/subscribe/get" });
			},
			//个人信息
			toMyinfo(){
				if(!this.myinfo || !this.myinfo.id){//未登录，跳转到登录页
					uni.navigateTo({
						url: "/pages/login/login" 
					})
					return;
				}
				uni.navigateTo({
					url: "/pages/mine/myinfo" 
				})
			},
			//我的孩子
			toMybabys(){
				if(!this.myinfo || !this.myinfo.id){//未登录，跳转到登录页
					uni.navigateTo({
						url: "/pages/login/login" 
					})
					return;
				}
				uni.navigateTo({
					url: "/pages/mine/mybabys" 
				})
			},
			//帮助中心
			toHelpCenter(){
				if(!this.myinfo || !this.myinfo.id){//未登录，跳转到登录页
					uni.navigateTo({
						url: "/pages/login/login" 
					})
					return;
				}
				uni.navigateTo({
					url: "/pages/mine/help_center" 
				})
			},
			//意见反馈
			toFeeDback(){
				if(!this.myinfo || !this.myinfo.id){//未登录，跳转到登录页
					uni.navigateTo({
						url: "/pages/login/login" 
					})
					return;
				}
				uni.navigateTo({
					url: "/pages/mine/feedback" 
				})
			},
			//订阅消息
			Subscribe(cb) {
				var _this = this;
				if(!this.myinfo || !this.myinfo.id){//未登录，跳转到登录页
					uni.navigateTo({
						url: "/pages/login/login" 
					})
					return;
				}
				//先获取下订阅数量
				this.getSubscribecount();

				//获取模板id
				var templateid = MessageUtil.getTemplateId('xcxhome', 1);
				console.log(templateid)
				debugger
				uni.requestSubscribeMessage({
					//此处填写刚才申请模板的模板ID
					tmplIds: [templateid],
					success(res) { 
						console.log(res)
						if(res.errMsg=='requestSubscribeMessage:ok'){
							//设置用户订阅数量
							uni.smaction(function (re, err) {
								if (err) {
									return uni.msg("设置用户订阅量 失败" + err);
								}
								uni.msg('订阅成功+1')
								_this.subscribecount=_this.subscribecount+1;
							}, { openid: app.globalData.objuserinfo.openid_xcx, templateid: templateid }, { route: uni.svs.zxx_home_biz, action: "message/subscribe/incr" });
						}
						cb&&cb(res)
					}
				})
			},
			logincallback(){
				this.myinfo = app.globalData.objuserinfo;
				this.initData();
			}
		}
	}
</script>

<style>
	page {
		background: #F2F4F7;
	}

	.weui-cell__ft {
		color: #8C8C8C;
		font-size: 20rpx;
	}

	.weui-cell__ft_in-access:after {
		border-color: #C7C7CC;
	}

	.weui-cell {
		padding: 30rpx 30rpx;
	}

	.weui-cell::before {
		right: 30rpx;
		left: 100rpx;

	}


	.top-bgview {
		height: 393rpx;


	}

	.statustxt {
		height: 128rpx;
		background: #FFFFFF;
		border-radius: 5px;
		background: #fff;
		width: 50%;
		align-items: center;
	}

	.identitylist {
		margin-top: 40rpx;
	}

	.identitytxt {
		flex: 1;
	}

	.identitytxt text {
		text-align: center;
		display: block;
		font-size: 28rpx;
		color: #303030;
	}

	.helplist {
		border-radius: 10rpx;
		background: #fff;
		font-size: 28rpx;
		color: #333333;
	}
</style>