<template>
	<view>
		<view v-if="myinfo.id && !curstuid">
			<view style="background: linear-gradient( 90deg, #F45357 0%, #F79676 100%); color: #fff; font-size: 24rpx; padding:10px 15px;" @click="addStu">关联学生</view>
		</view>
		<view style="height: 300rpx;">
			<view class="top-bgview">
				<image src="/static/image/home/<USER>" style="width: 100%;height: 359rpx;"></image>
				<block v-if="1==2">
					<view style="text-align: center;position: absolute;top:0px;left: 0;right: 0;">
						<view class="topbg">
							<view class="weui-cell">
								<view class="weui-cell__bd weui-flexcen" style="text-align: left;">
									<image src="/static/image/home/<USER>"
										style="width: 31rpx;height: 31rpx; margin-right: 5px;"></image>您有1条新消息待处理
								</view>
								<view class="weui-cell__ft" style="color: #fff;">去查看 >
								</view>
							</view>
						</view>
					</view>
				</block>
			</view>
			<view v-if="arrmenu.length > 0" class="cwxzdiv">
				<block v-for="(menu,i) in arrmenu" :key="i">
					<view v-if="menu.menu_type == 'C'" @click="toMenu(menu.path)" class="weui-grid">
						<view class="weui-grid__icon">
							<image :src="menu.icon ? menu.icon : '/static/image/home/<USER>'"
								style="width:94rpx;height: 94rpx;"></image>
						</view>
						<text class="weui-grid__label">{{menu.menu_name}}</text>
					</view>
				</block>
			</view>
			<view v-else class="nomesssage" style="text-align: center;">
				<image src="/static/image/home/<USER>" style="width: 240rpx;height:241rpx;"></image>
				<text style="font-size: 30rpx;
				color: #999999; text-align: center;display: block;">暂无数据</text>
			</view>
		</view>
	</view>
</template>

<script>
	var moment = require("../../../common/moment.min.js");
	var util = require("../../../common/util.js")
	var app = getApp();
	var pagesize = 10;
	export default {
		data() {
			return {
				curstuid: 0,
				islogin: app.checkLogin(),
				myinfo: app.globalData.objuserinfo||{}, 
				arrmenu: []
			} 
		},
		onLoad: function(params) {
			if (app.globalData.waitlogin) {
				// return uni.navigateTo({
				// 	url: "/pages/login/login?qrcodeuserid=" + (params.qrcodeuserid || '') + "&qrcodegiftcode=" + (params.qrcodegiftcode || '')
				// })
				app.globalData.userInfoReadyCallback = this.loadData;
			} else {
				console.log("onLoad:" + params);
				this.loadData();
			}
		},
		onShow() {
		
		},
		methods: {
			loadData(){
				this.curstuid = app.globalData.objuserinfo && app.globalData.objuserinfo.stu_id || 0;
				if(!this.curstuid){//没有关联学生
					uni.msg("没有关联学生，请添加学生");
					return;
				}
				var _this = this;
				uni.smaction(function(re, err){
					if(err){
						return uni.msg(err);
					}
					console.log('re:', re);
					_this.arrmenu = re;
				}, {}, {route:uni.svs.zxx_home_biz, action: 'menu/usermenu'})
			},
			addStu(){
				uni.navigateTo({
					url: '/pages/home/<USER>'
				})
			},
			toMenu(page){
				uni.navigateTo({
					url: page
				})
			},
			logincallback(){
				this.myinfo = app.globalData.objuserinfo;
				this.initData();
			}
		},
		
	}
</script>


<style>
	page {
		background: #fff;

	}
	.nomesssage{    
		position: absolute;            
		top:50%;            
		left:50%;            
		width:100%;            
		transform:translate(-50%,-50%);            
		text-align: center;     
	}
	.top-bgview {
		height: 350rpx;
		overflow: hidden;
	}


	.cwxzdiv {

		width: 100%;
		background: #fff;
		z-index: 999;
		border-radius: 30px 30px 0px 0px;
		position: absolute;
		margin-top: -50rpx;

	}


	.topbg {
		background: linear-gradient(90deg, #F45357 0%, #F79676 100%);
		color: #FFFFFF;
		font-size: 24rpx;
		height: 80rpx;
	}

	.weui-grid {
		border-right: none;
		border-bottom: none;
		padding: 40rpx 10px 0 10rpx;
	}

	.weui-grid__icon {
		width: 94rpx;
		height: 94rpx;
	}

	.weui-grid__label {
		font-size: 28rpx;
		color: #303030;
	}

	.weui-grid {
		width: 25%;
	}
</style>
