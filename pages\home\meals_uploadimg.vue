<template>
	<view>
		<view class="remarks-list">
			<view class="font14 martop" style="color: #999999;">请上传缴费回执照片</view>
			<view class="weui-uploader__bd" style="margin: 10px 0 0 0;">
				<view class="weui-uploader__files" style="padding-top: 5px;">
					<uni-file-picker v-model="imageValue" fileMediatype="image" mode="grid" :limit="9"
						@select="select"></uni-file-picker>
				</view>
			</view>
		</view>
		  <!-- 提交按钮 -->
		 <view class="button-bottom " style="height: 160rpx;box-shadow: none;border-top:none;">
	         <button class="common-btn noafter"  @click="btnConfirm">确定</button>
		</view>
	</view>
</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js");
	var uploadUtil = require("../../common/uploadUtil.js");
	var app = getApp();

	export default {
		data() {
			return {
				myinfo: app.globalData.objuserinfo,
				recordid: 0,
				imageValue: [],
				arrImg: []
			}
		},
		onLoad: function(params) {
			this.recordid = params.recordid;
		},
		onShow() {},
		methods: {
			gettext(e) {
				this.text = e.detail.value;
			},
			select(e) {
				var _this = this;
				uni.showLoading({
					title: '上传中...'
				})
				// 调用上传接口
				uploadUtil.uploadServer({
					arrfile: e.tempFiles,
					// #ifdef H5
					serverurl: "/" + uni.svs.zxx + '/ueditorController?Authorization=Bearer ' + uni.getStorageSync(
						'token') + '&action=uploadfile&upRePath=unihome/meals/', // 文件接收服务端
			
					// #endif
					// #ifndef H5
					serverurl: app.globalData.geturl(uni.svs.zxx, "ueditorController") + '?Authorization=Bearer ' +
						uni.getStorageSync('token') + '&action=uploadfile&upRePath=unihome/meals/', // 文件接收服务端
					// #endif
					complete: function(arrpath) {
						console.log('arrpath', arrpath);
						for (var i = 0; i < arrpath.length; i++) {
							var imgurl = arrpath[i].key;
							_this.arrImg.push(imgurl);
						}
						uni.msg("上传成功");
					}
				})
			},
			btnConfirm() { //无疑问，确认
				if (this.arrImg.length == 0) {
					return uni.msg("请上传缴费凭证");
				}
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg(err);
					}
					var prepage = uni.getPrePage(-2);
					prepage.refresh(4);
					uni.navigateBack({
						delta: 2
					});
				}, ["meals.confirmfee", this.arrImg.join(","), this.recordid], {
					route: uni.svs.zxx
				})
			}

		}
	}
</script>

<style>
	page {
		background: #fff;
	}


	.weui-cell .weui-cell__bd {
		font-size: 14px;
		color: #999999;
	}



	.remarks-list {
		background: #fff;
		margin: 10px 15px;
		/* border-bottom: 1px solid #F1F1F1; */
		padding-bottom: 5px;
	}

	.weui-uploader__input-box {
		width: 165rpx;
		background: #F7F9FC !important;
		height: 165rpx;
	}

	.weui-uploader__file {
		position: relative;
		width: 165rpx;
		height: 165rpx;
	}

	.weui-uploader__input-box {
		box-sizing: border-box;
		border: 1px solid #eef1f2;
		background: #f9f9f9;

	}

	.img-upload {
		width: 165rpx;
		height: 165rpx;

		display: block;

	}

	.img-close {
		width: 22rpx;
		height: 22rpx;
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		z-index: 111;
	}

	.uni-combox {
		border: none;
		line-height: 60rpx;
		height: 60rpx;
		padding: 0 8px 0 0;
	}

	.weui-uploader__input-box:after,
	.weui-uploader__input-box:before {
		background-color: transparent;
	}
</style>
