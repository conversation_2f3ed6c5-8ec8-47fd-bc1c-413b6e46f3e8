<template>
    <view>
        <view id="scale-wrapper" class="wrapper" :style="'background:' + bgoutside + ';height:' + elemHeight + 'px;width:' + w + 'px;display:inline-block;'">
            <view class="zhongjianxian" :style="'width:' + w / 2 + 'px;border:1px ' + lineSelect + ' solid;top:' + rul.elemHeight / 2 + 'px'"></view>
            <scroll-view style="height: 100%" :scroll-y="true" :scroll-top="centerNum" :scroll-with-animation="true" @scroll.stop.prevent="bindscroll">
                <view id="canvas" class="scroll-wrapper">
                    <view class="seat" :style="'width:' + w + 'px;height:' + (rul.elemHeight / 2 - fiexNum / 2 + 1) + 'px;'"></view>
                    <view class="scale-image" :style="'width:' + w + 'px; height:' + ruleheight + 'px'">
                        <image :src="ruleimgsrc" mode=""></image>
                    </view>
                    <view class="seat" :style="'width:' + w + 'px;height:' + (rul.elemHeight / 2 - fiexNum / 2 - 5) + 'px;'"></view>

                    <view class="oldvalxian" :style="'width:' + (w / 2 - 5) + 'px;border:1px red solid;top:' + oldvalpx + 'px'"></view>
                </view>
            </scroll-view>
        </view>
        <view class="canvas" :style="'height:' + ruleheight + 'px;width：' + w + 'px;'" v-if="!ruleimgsrc">
            <canvas class="canvas" :style="'height:' + ruleheight + 'px;width：' + w + 'px;'" canvas-id="canvas"></canvas>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            imageWidth: '', 
            bgoutside: '#dbdbdb',
            lineSelect: '#52b8f5',
            scaleId: '',

            rul: {
                elemHeight: 0
            },

            assingOldVal: -1,
            ruleheight: '',
            oldvalpx: '',
            centerNum: '',
            ruleimgsrc: '',
            round: '',
            canvashide: false
        };
    },
    /**
     * 组件的属性列表
     */
    props: {
        // 最小值
        min: {
            type: Number,
            default: 0
        },
        //最大值
        max: {
            type: Number,
            default: 100
        },
        // 是否开启整数模式
        int: {
            type: Boolean,
            default: true
        },
        // 每个格子的长度（只能是 1 ，2 ，5 一个能被10整除的数字 ）
        step: {
            type: Number,
            default: 1
        },
        // 每个格子的实际行度 （单位px ，相对默认值）
        single: {
            type: Number,
            default: 10
        },
        // 卡尺左右的余量 ，最为60
        fiexNum: {
            type: Number,
            default: 60
        },
        // 高度
        w: {
            type: Number,
            default: 80
        },
        elemHeight: {
            type: Number,
            default: 300
        },
        // 当前选中
        activeV: {
            type: null,
            default: '0'
        },
        styles: {
            type: Object,
            default: () => ({
                line: '#dbdbdb',
                bginner: '#fbfbfb',
                bgoutside: '#dbdbdb',
                lineSelect: '#52b8f5',
                font: '#404040'
            })
        },
        rulesrc: {
            type: String,
            default: ''
        }
    },
    mounted() {
        this.init_v();
    },
    /**
     * 组件的方法列表
     */
    methods: {
        /**
         * 初始化卡尺
         */
        init_v() {
            let self = this;
            let rul = {
                spa: '',
                // 单个格子的距离
                unitNum: '',
                // 格子总数
                minNum: this.min,
                maxNum: this.max,
                num: this.max - this.min,
                // 仿数据总数
                FIXED_NUM: this.fiexNum,
                // 标尺上下空余部分
                single: this.single,
                // 单个格子的实际长度（单位px）
                step: this.step,
                // 步长，相对传入的值，每个格子代表几个值（值只能是能被10整除的 整数 ，1，2，5）, 默认值 1
                w: this.w,
                //宽度
                elemHeight: this.elemHeight,
                //高度
                activeV: '',
                //初始值
                styles: this.styles //样式
            };

            this._getErro(rul); // 判断是否使用整数类型

            if (self.int) {
                rul.unitNum = rul.num / rul.step;
            } else {
                rul.unitNum = rul.num * (rul.single / rul.step);
            } // 设置单个格子的长度

            rul.spa = rul.single * rul.step;
            rul.total = rul.spa * rul.unitNum + rul.FIXED_NUM;

            if (this.rulesrc) {
                let centerNum = this.assignValue(this, rul);
                var rulesrc = this.rulesrc;
                var oldvalpx = centerNum + rul.elemHeight / 2;
                self.w= this.w;
                self.elemHeight= this.elemHeight;
                self.ruleheight= rul.total;
                self.fiexNum= rul.FIXED_NUM;
                self.rul=rul;
                self.oldvalpx=oldvalpx;
                self.centerNum= centerNum;
                self.ruleimgsrc= "/static/image/" + rulesrc + ".png";
                self.round= self.int ? rul.minNum : rul.minNum.toFixed(1);
                self.bgoutside= rul.styles.bgoutside;
                self.lineSelect= rul.styles.lineSelect;
                self.$emit('value', {
                    detail: {
                        value: self.round
                    }
                });
            } else {
                return; 
            }
        },

        /**
         * 绘制
         * 生成卡尺
         */
        draw(num, total, { self, rul }) {
            let canvasWeight = 80;
            let ctx = uni.createCanvasContext('canvas', self); //  绘制背景

            ctx.save();
            ctx.setFillStyle(rul.styles.bginner);
            ctx.fillRect(0, 0, canvasWeight, total);
            ctx.restore();
            ctx.beginPath();
            ctx.setLineWidth(1);
            ctx.setStrokeStyle(rul.styles.line);
            ctx.moveTo(rul.FIXED_NUM / 2, 0);
            ctx.lineTo(total - rul.FIXED_NUM / 2, 0);
            ctx.stroke();

            for (let i = 0; i < rul.unitNum + 1; i++) {
                // 绘制文字
                if (i % (rul.single / rul.step) === 0) {
                    ctx.setFontSize(18);
                    ctx.setFillStyle(rul.styles.font);
                    ctx.setTextAlign('center');

                    if (self.int) {
                        ctx.fillText(rul.maxNum - i * rul.step, canvasWeight - 15, rul.FIXED_NUM / 2 + i * rul.spa);
                    } else {
                        ctx.fillText(rul.maxNum - i / (rul.single / rul.step), canvasWeight - 15, rul.FIXED_NUM / 2 + i * rul.spa);
                    }
                } // 绘制刻度

                if (i % 5 === 0) {
                    ctx.beginPath();
                    ctx.setLineWidth(2);
                    ctx.setStrokeStyle(rul.styles.line);
                    ctx.moveTo(0, rul.FIXED_NUM / 2 + i * rul.spa);
                    ctx.lineTo(canvasWeight / 2, rul.FIXED_NUM / 2 + i * rul.spa);
                    ctx.stroke();
                } else {
                    ctx.beginPath();
                    ctx.setLineWidth(1);
                    ctx.setStrokeStyle(rul.styles.line);
                    ctx.moveTo(0, rul.FIXED_NUM / 2 + i * rul.spa);
                    ctx.lineTo(canvasWeight - 50, rul.FIXED_NUM / 2 + i * rul.spa);
                    ctx.stroke();
                }
            }

            ctx.draw(true,setTimeout(() => {
                    uni.canvasToTempFilePath({
                            x: 0,
                            y: 0,
                            width: canvasWeight,
                            height: total,
                            // destWidth: total * 4,
                            // destHeight: canvasWeight * 4,
                            canvasId: 'canvas',
                            success: (res) => {
                                // 改变高度重新计算
                                rul.total = (rul.total / 80) * rul.w;
                                rul.FIXED_NUM = (rul.FIXED_NUM / 80) * rul.w; // let centerNum = self.int ?
                                //   ((rul.activeV - rul.minNum) / rul.step) *
                                //   parseInt(rul.total - rul.FIXED_NUM) / rul.num * rul.step :
                                //   ((rul.activeV - rul.minNum) * 10 / rul.step) *
                                //   parseFloat((rul.total - rul.FIXED_NUM)) / rul.num / (rul.single / rul.step)

                                let centerNum = self.assignValue(self, rul);
                                self.canvashide= true;
                                self.ruleimgsrc= res.tempFilePath;
                                self.centerNum;
                                self.ruleheight= rul.total;
                                self.w= rul.w;
                                self.elemHeight= rul.elemHeight;
                                self.fiexNum= rul.FIXED_NUM;
                                self.round= self.int ? rul.minNum : rul.minNum.toFixed(1);
                                self.bgoutside= rul.styles.bgoutside;
                                self.lineSelect= rul.styles.lineSelect;
                                self.$emit('value', {
                                    detail: {
                                        value: self.round
                                    }
                                });
                            },

                            fail(e) {
                                console.log(e);
                            }
                        },this);
                }, 5000)
            );
        },

        /**
         * 获取滑动的值
         */
        bindscroll: function (e) {
            let rul = this.rul; // 移动距离

            let top = e.detail.scrollTop; // 单格的实际距离

            let spa; // 判断是否是整数

            if (this.int) {
                spa = (parseInt(rul.total - rul.FIXED_NUM) / rul.num) * rul.step;
            } else {
                spa = parseFloat(rul.total - rul.FIXED_NUM) / rul.num / (rul.single / rul.step);
            } // 当前显示值

            let resultNum = Math.round(top / spa); // 还原为实际数值

            let redNum = Math.round(resultNum * spa); // 小数位处理

            if (this.int) {
                resultNum = rul.maxNum - resultNum * rul.step;
            } else {
                resultNum = (rul.maxNum - (resultNum * rul.step) / 10).toFixed(1);
            }

            if (this.assingOldVal === resultNum) {
                return;
            }

            this.round=resultNum;
            this.assingOldVal=resultNum;
            this.$emit('value', {
                detail: {
                    value: resultNum
                }
            });
            clearTimeout(rul.Timer);
            rul.Timer = setTimeout(() => {
                // console.log("执行了定时器")
                this.centerNum= redNum;
                this.round= resultNum;
                this.activeV= resultNum;
                this.assingOldVal= resultNum;
                this.$emit('value', {
                    detail: {
                        value: resultNum
                    }
                });
            }, 1000);
        },

        /**
         * 输出错误信息
         */
        _getErro(rul) {
            // 判断 最大值 最小值 是否 正确
            if (rul.minNum >= rul.maxNum) {
                console.error('您输入的最大值 小于最小值，请检查 minNum ， maxNum');
                rul.minNum = 0;
                rul.maxNum = 100;
                rul.num = rul.maxNum - rul.minNum;
            } // 判断 是否开启整数类型

            if (rul.step !== 1 && rul.step !== 2 && rul.step !== 5) {
                console.error('步长只能是 1 ，2  ，5  ,请检查 step');
                rul.step = 1;
            }

            if (rul.FIXED_NUM < 60) {
                console.warn('左右余量 输入小于 60 ，可能影响显示效果，请检查 fiexNum');

                if (!rul.FIXED_NUM) {
                    rul.FIXED_NUM = 60;
                }

                if (rul.FIXED_NUM < 0) {
                    console.error('左右余量最小为0  ，请检查 fiexNum');
                    rul.FIXED_NUM = 0;
                }
            }

            if (rul.single < 10) {
                console.warn('格子单位小于10 ，可能影响显示效果，请检查 single');

                if (!rul.single) {
                    rul.single = 10;
                }
            }

            if (rul.w < 50) {
                console.warn('格子单位小于50 ，可能影响显示效果，请检查 w');

                if (!rul.w) {
                    rul.w = 80;
                }

                if (rul.w < 20) {
                    console.error('高度最小为20  ，请检查 w');
                    rul.w = 20;
                }
            } // 当前选中位置设置

            if (this.activeV === 'min') {
                rul.activeV = rul.minNum;
            } else if (this.activeV === 'max') {
                rul.activeV = rul.maxNum;
            } else if (this.activeV === 'center') {
                rul.activeV = (rul.maxNum + rul.minNum) / 2;
            } else {
                rul.activeV = this.activeV;
            }

            if (this.activeV !== 'min' && this.activeV !== 'max' && this.activeV !== 'center') {
                // console.log("任意数值")
                if (rul.activeV < rul.minNum || rul.activeV > rul.maxNum) {
                    console.error('您输入的数值（activeV）超入范围，请检查 activeV');
                    this.$emit('error', {
                        detail: {
                            error: 'notinfanwei',
                            activeV: rul.activeV,
                            minNum: rul.minNum,
                            maxNum: rul.maxNum
                        }
                    });
                }

                if (rul.activeV % rul.step !== 0 && rul.int) {
                    console.warn('您输入的数值（activeV）不是合法数值，请检查，所以导致结果可能有错误');
                }

                if ((rul.activeV * 10) % rul.step !== 0 && !rul.int) {
                    console.warn('您输入的数值（activeV）不是合法数值，请检查，所以导致结果可能有错误');
                }
            }

            if (!rul.styles) {
                rul.styles = {};

                if (!rul.styles.line) {
                    rul.styles.line = '#dbdbdb';
                }

                if (!rul.styles.lineSelect) {
                    rul.styles.lineSelect = '#52b8f5';
                }

                if (!rul.styles.bginner) {
                    rul.styles.bginner = '#fbfbfb';
                }

                if (!rul.styles.bgoutside) {
                    rul.styles.bgoutside = '#dbdbdb';
                }

                if (!rul.styles.font) {
                    rul.styles.font = '#404040';
                }
            } else {
                if (!rul.styles.line) {
                    rul.styles.line = '#dbdbdb';
                }

                if (!rul.styles.lineSelect) {
                    rul.styles.lineSelect = '#52b8f5';
                }

                if (!rul.styles.bginner) {
                    rul.styles.bginner = '#fbfbfb';
                }

                if (!rul.styles.bgoutside) {
                    rul.styles.bgoutside = '#dbdbdb';
                }

                if (!rul.styles.font) {
                    rul.styles.font = '#404040';
                }
            }
        },

        assignValue(self, rul) {
            return self.int
                ? ((((rul.maxNum - rul.activeV) / rul.step) * parseInt(rul.total - rul.FIXED_NUM)) / rul.num) * rul.step
                : ((((rul.maxNum - rul.activeV) * 10) / rul.step) * parseFloat(rul.total - rul.FIXED_NUM)) / rul.num / (rul.single / rul.step);
        }
    },
    watch: {
        activeV: {
            handler: function (newVal, oldVal) {
                console.log("old:"+newVal+ ',new:'+newVal);

                if (this.rul.maxNum) {
                    let centerNum = this.assignValue(this, this.rul);
                    this.centerNum=centerNum; 
                }
				this.$emit('value', {
					detail: {
						value: newVal
					}
				});
            },

            immediate: true
        }
    }
};
</script>
<style>
/* pages/test/test.wxss */

.wrapper {
    position: relative;
    box-sizing: border-box;
    background: #dbdbdb;
}

.zhongjianxian {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 0px;
    /* height: 40px; */
    border: 1px #52b8f5 solid;
    z-index: 999;
}
.oldvalxian {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 0px;
    /* height: 40px; */
    border: 1px red solid;
    z-index: 999;
}

.scroll-wrapper {
}

.scale-image {
    flex-shrink: 0;
    height: 70px;
    display: inline-block;
    /* border: 1px red solid; */
}

.scale-image image {
    width: 100%;
    height: 100%;
}

.seat {
    float: left;
}

.canvas {
    position: absolute;
    overflow: hidden;
    box-sizing: border-box;
    top: 0;
    right: -100px;
    z-index: -1;
}
</style>
