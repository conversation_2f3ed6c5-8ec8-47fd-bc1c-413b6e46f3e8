<template>


	<view style="height: 100%; position: relative; width: 100%; ">
		<image src="../static/image/home/<USER>" style="width: 100%; height: 100%;"></image>

		

	</view>


</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {

			}
		},
		onLoad: function(params) {
			this.params = params;
			console.log("-----------", params);
			if (app.globalData.waitlogin) {
				app.globalData.userInfoReadyCallback = this.init;
			} else {
				this.init();
			}
		},
		onReady() {
		
		},
		methods: {
			logincallback: function() {
				this.init();
			},
			init() {
				var scene = 0;
				// #ifdef MP-WEIXIN
				//扫描小程序码进行签到       
				//       1.注册
				//          1.1 先登录流程 1.2 跳转签到页面 1.3进行签到 签退
				//       2.未注册
				//          2.1 跳转登录页
				var options = wx.getLaunchOptionsSync();
				console.log(options)
				scene = options.scene;
				// #endif
				var qrcodeuserid = "";
				var qrcodegiftcode = "";
				if (scene && this.params.scene) { //扫描二维码进入 && 有id
					var arrscene = this.params.scene.split('-');
					qrcodeuserid = arrscene[0];
					if (arrscene.length > 1) {
						qrcodegiftcode = arrscene[1];
					}
					uni.reLaunch({
						url: "/pages/tabBar/home/<USER>" + qrcodeuserid + "&qrcodegiftcode=" + qrcodegiftcode
					});
				} else { //非扫描码进入
					setTimeout(function() {
						uni.reLaunch({
							url: "/pages/tabBar/home/<USER>"
						})
					}, 1000)
				}
			}
		}
	}
</script>

<style>
	page {
		background: #ffffff;
	}

	page,
	template {
		height: 100%;
	}
</style>