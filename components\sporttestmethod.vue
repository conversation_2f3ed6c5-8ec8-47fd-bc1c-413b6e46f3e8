<template>
    <!-- pages/measurement/waymeasure.wxml -->
    <view :class="'shadow-view ' + wrapAnimate" v-if="flag" style="background: none; z-index: 999" @touchmove.stop.prevent="true">
        <view :class="'shadow-view ' + wrapAnimate" @tap="tapshadow"></view>
        <view class="shadow-content" style="z-index: 10; padding: 0">
            <cover-view style="padding: 20rpx 0 15rpx 0; border-radius: 6px; background: #ffffff">
                <cover-view class="measure-title">{{ testname }}的测量方法</cover-view>
                <cover-view class="measure-way">{{ testname }}（单位：{{ danwei }}）</cover-view>
                <cover-view class="measure-img">
                    <cover-image :src="'../pages/zhishiku/static/image/measure_' + key1 + '.png'" style="width: 587rpx; height: 186rpx; margin: 0 auto" />
                </cover-view>
                <cover-view class="measrue-con" style="overflow-y: scroll">
                    <cover-view class="measure-list">
                        <cover-view>动作要领：</cover-view>
                        <cover-view class="weui-flex__item">
                            <cover-view>{{ yaoling }}</cover-view>
                        </cover-view>
                    </cover-view>
                    <cover-view class="measure-list">
                        <cover-view>注意事项：</cover-view>
                        <cover-view class="weui-flex__item">
                            <cover-view>{{ zhuyi }}</cover-view>
                        </cover-view>
                    </cover-view>
                    <cover-view class="measure-list">
                        <cover-view>物品准备：</cover-view>
                        <cover-view class="weui-flex__item">
                            <cover-view>{{ wupin }}</cover-view>
                        </cover-view>
                    </cover-view>
                    <cover-view class="measure-list">
                        <cover-view>正确填写：</cover-view>
                        <cover-view class="weui-flex__item">
                            <cover-view>{{ tianxie }}</cover-view>
                        </cover-view>
                    </cover-view>
                </cover-view>
            </cover-view>
            <cover-view style="width: 74rpx; height: 74rpx; position: absolute; left: 50%; top: -37rpx; margin-left: -37rpx; z-index: 9999">
                <cover-image :src="'../pages/zhishiku/static/image/boy_' + itemkey + '_HL.png'" class="userAvatar" />
            </cover-view>
            <cover-image src="../pages/zhishiku/static/image/close_icon.png" style="width: 29px; height: 29px; position: absolute; left: 50%; margin-left: -15px; bottom: -40px" @tap="hidePopup" />
        </view>
    </view>
</template>

<script>
// components/sporttestmethod.js
var config = require('@/common/config.js');

export default {
    data() {
        return {
            wrapAnimate: '',
            flag: false,
            testname: '',
            danwei: '',
            yaoling: '',
            zhuyi: '',
            wupin: '',
			itemkey:'sensitive',
            tianxie: ''
        };
    },
    options: {
        addGlobalClass: true
    },
    /**
     * 组件的属性列表
     */
    props: {
        key1: {
            type: String,
            default: 'run'
        }
    },
    /**
     * 组件的方法列表
     */
    methods: {
        showPopup: function (k) {
            var key1 = k||this.key1;
            var itemkey = config.objsport[key1].itemkey;
            this.wrapAnimate='wrapAnimate';
            this.flag=true;
            this.key1=key1;
            this.itemkey=itemkey;
            this.testname=config.objsport[key1].name;
            this.danwei=config.objsport[key1].danwei;
            this.yaoling=config.objsporttest[itemkey].yaoling;
            this.zhuyi=config.objsporttest[itemkey].zhuyi;
            this.wupin=config.objsporttest[itemkey].wupin;
            this.tianxie=config.objsporttest[itemkey].tianxie;
        },

        //隐藏弹框
        hidePopup: function () {
            const that = this;
            this.wrapAnimate= 'wrapAnimateOut';
            setTimeout(function () {
                that.flag=false;
            }, 500);
        },

        tapshadow: function () {
            this.hidePopup();
        },

        true() {
            console.log('占位：函数 true 未声明');
        }
    }
};
</script>
<style>
/* components/sporttestmethod.wxss */
cover-view,
cover-image {
    overflow: inherit;
    white-space: inherit;
}
.measure-title {
    color: #333333;
    font-size: 15px;
    font-weight: bold;
    padding: 25rpx 0;
    text-align: center;
    border-bottom: 1px solid #eeeeee;
}
.userAvatar {
    background: #f2f2f2;
    border: 7rpx solid #ffffff;
    width: 60rpx;
    height: 60rpx;
}
.measure-way {
    color: #666666;
    font-size: 12px;
    padding: 15rpx 25rpx;
    border-bottom: 1px solid #eeeeee;
}
.measure-img {
    padding: 30rpx 0 15rpx 0;
    text-align: center;
    border-bottom: 1px solid #eeeeee;
}
.measrue-con {
    height: 500rpx;
    overflow: auto;
}
.measrue-con .measure-list {
    font-size: 12px;
    color: #999999;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    border-bottom: 1px solid #eeeeee;
    padding: 15rpx 25rpx;
    line-height: 38rpx;
}
.measrue-con .measure-list:last-child {
    border-bottom: none;
}
.measrue-con .measure-list text {
    font-size: 13px;
    color: #666666;
    font-weight: bold;
}
.icon_close {
    position: absolute;
    bottom: -80rpx;
    left: 50%;
    margin-left: -26rpx;
}

.wrapAnimate {
    animation: wrapAnimate 0.5s linear forwards;
}
@keyframes wrapAnimate {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
.wrapAnimateOut {
    animation: wrapAnimateOut 0.3s 0.2s linear forwards;
}
@keyframes wrapAnimateOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
</style>
