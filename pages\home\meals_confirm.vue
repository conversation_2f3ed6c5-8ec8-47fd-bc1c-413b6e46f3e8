<template>


	<view class="marbot10">
		<view style="background: #C23C43;font-size: 24rpx;line-height: 80rpx; text-align:center; color: #fff;">
			若对就餐情况有疑问请留言！</view>
		<view class="weui-cell" style="border-bottom: 1px solid #EBEBEB;">
			<view class="weui-cell__bd">
				<view class="sel-person-list green-left">
					<view class="weui-flex " style="align-items: center;">就餐天数</view>
				</view>
			</view>
		</view>
		<view class="dining-list">
			<view class="weui-cell">
				<view class="weui-cell__bd" style="color: #999999;">
					就餐确认月份
				</view>
				<view class="weui-cell__ft">{{eatmonth}}
				</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd" style="color: #999999;">
					缺勤未就餐天数
				</view>
				<view class="weui-cell__ft">{{objrecord.leavedays}}天
				</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd" style="color: #999999;">
					合计就餐天数
				</view>
				<view class="weui-cell__ft">{{eatdays}}天
				</view>
			</view>
			<view class="dining-days" @click="toDetail">
				查看就餐天数明细
			</view>
		</view>
		<!-- 提交按钮 -->
		<view v-if="objrecord.parentstatus == 1" class="button-bottom " style="height: 320rpx;box-shadow: none;border-top:none;">
			<button class="common-btn noafter" @click="toFeedback" style="color: #303030;background: #F2F4F7;">对就餐情况有疑问，去留言</button>
			<button class="common-btn noafter" @click="btnConfirm" style="margin-top: 0;">确认就餐情况无误</button>
		</view>
	</view>

</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js");
	var app = getApp();
	var pagesize = 10;

	export default {
		data() {
			return {
				myinfo: app.globalData.objuserinfo,
				objrecord: {}
			}
		},
		onLoad: function(params) {
			var recordid = params.recordid;
			this.initData(recordid);
		},
		onShow() {

		},
		computed: {
			eatmonth() {
				var startdate = this.objrecord.startdate;
				var enddate = this.objrecord.enddate;
				if (startdate && enddate) {
					var startyearmonth = startdate.substring(0, 7);
					var endyearmonth = enddate.substring(0, 7);
					if (startyearmonth == endyearmonth) { //如果开始日期和结束日期在同一个月
						return startyearmonth.replace("-", "年") + "月";
					} else {
						return startyearmonth.replace("-", "年") + "月-" + endyearmonth.replace("-", "年") + "月";
					}
				} else {
					return '';
				}
			},
			eatdays() {
				var arrdate = this.objrecord.arrdate;
				if (arrdate) {
					var leavedays = this.objrecord.leavedays;
					var totalday = arrdate.split(",").length;
					return totalday - leavedays;
				} else {
					return 0;
				}
			}
		},
		watch: {},
		methods: {
			initData(recordid) {
				var _this = this;
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg(err.msg | '查询出错');
					}
					console.log('re:', re);
					//m.id,m.name,m.arrdate,m.startdate,m.enddate,r.id as recordid,m.arrdate,m.startdate,m.enddate,r.leavedays,parentstatus,charge_no,pay_img
					_this.objrecord = re;
				}, ["meals.selectbyrecordid", recordid], {
					route: uni.svs.zxx
				})
			},
			toDetail() {
				uni.navigateTo({
					url: "/pages/home/<USER>" + this.objrecord.recordid
				})
			},
			toFeedback() { //有疑问，反馈
				uni.navigateTo({
					url: "/pages/home/<USER>" + this.objrecord.recordid
				})
			},
			btnConfirm() { //无疑问，确认
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg(err);
					}
					var prepage = uni.getPrePage();
					prepage.refresh(3);
					uni.navigateBack();
				}, ["meals.confirmmeal", this.objrecord.recordid], {
					route: uni.svs.zxx
				})
			}

		}
	}
</script>

<style>
	page {
		background: #fff;
	}

	.weui-cell {
		padding: 0px 15px;
	}


	.weui-cell__bd {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell__ft {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell:before {
		right: 0px;
		left: 0;
		border-top: none;
	}

	.dining-list {
		border-bottom: 1px solid rgb(235, 235, 235);
		padding: 10px 0;
		margin: 0 15px;
	}

	.dining-list .weui-cell {
		padding: 5px 0px;
	}

	.weui-uploader__input-box {
		width: 165rpx;
		background: #F7F9FC !important;
		height: 165rpx;
	}

	.weui-uploader__file {
		position: relative;
		width: 165rpx;
		height: 165rpx;
	}



	.img-upload {
		width: 165rpx;
		height: 165rpx;

		display: block;

	}

	.dining-days {
		font-size: 28rpx;
		color: #EB7D1C;
		text-align: right;
		text-decoration: underline;
		cursor: pointer;
	}
</style>
