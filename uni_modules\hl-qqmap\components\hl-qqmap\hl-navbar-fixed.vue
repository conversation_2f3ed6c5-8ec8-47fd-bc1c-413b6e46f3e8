<template>
	<view class="nuter">
		<view v-for="(item, index) in navArr" :key="index" class="nav-item" :class="target == index?'active':''"
			@click="tabChange(index)" :id="'item-' + index" key="index">
			<text class="title">{{item.title}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			navArr: {
				type: Array,
			},
			scrollChangeIndex: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				target: 0,
			}
		},
		methods: {
			tabChange(index) {
				this.$emit('navbarTap', index);
				this.target = index
			},
		},
		watch: {
			scrollChangeIndex(val) {
				this.tabChange(val)
			},
		}
	}
</script>

<style lang="scss">
	
	$uni-color-theme : #007aff !default
	
	.nuter {
		width: 100%;
		height: 100%;
		line-height: 46px;
		display: flex;
		font-size: 35rpx;
		padding: 0 20px;
		background-color: #FFFFFF;
	}

	.nuter view {
		font-size: 30rpx;
		text-align: center;
		background-color: #FFFFFF;
		margin-right: 80rpx;
	}

	.active {
		box-sizing: border-box;
		color: $uni-color-theme;
		border-bottom: 5rpx solid $uni-color-theme;
		background-color: #f3ffff;
	}

</style>
