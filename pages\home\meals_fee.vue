<template>
	<view class="marbot10">
		<view style="background: #C23C43;font-size: 24rpx;color: #FFFFFF;line-height: 80rpx; text-align:center;">若对费用有疑问请联系本班老师！</view>
		<view class="weui-cell" style="border-bottom: 1px solid #EBEBEB;">
			<view class="weui-cell__bd">
				<view class="sel-person-list green-left">
					<view class="weui-flex " style="align-items: center;">应缴金额</view>
				</view>
			</view>
			<view class="weui-cell__ft" style="color: #C23C43;">¥ {{objrecord.totalcost}} </text>
			</view>
		</view>
		<view class="dining-list">
			<view class="weui-cell">
				<view class="weui-cell__bd" style="color: #999999;">
					缴费项目
				</view>
				<view class="weui-cell__ft">{{objrecord.name}}
				</view>
			</view>
			<view class="dining-days" @click="toDetail">
				查看缴费明细
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd" style="color: #999999;">
					收费单位
				</view>
				<view class="weui-cell__ft">{{myinfo.yeyname}}
				</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd" style="color: #999999;">
					收费单号
				</view>
				<view class="weui-cell__ft">{{objrecord.charge_no}}
				</view>
			</view>
		</view>
	
		<!-- 提交按钮 -->
		 <view v-if="objrecord.feestate == 0" class="button-bottom " style="height: 160rpx;box-shadow: none;border-top:none;">
		     <button v-if="feeway == 1" class="common-btn noafter"  @click="toUpload">我已缴费，去上传凭证</button>
			 <button v-else class="common-btn noafter"  @click="toPay">立即缴费</button>
		</view>
	</view>

</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js");
	var app = getApp();
	var pagesize = 10;

	export default {
		data() {
			return {
				myinfo: app.globalData.objuserinfo,
				feeway: 1,
				objrecord: {}
			}
		},
		onLoad: function(params) {
			var recordid = params.recordid;
			console.log(recordid);
			this.getFeeway();
			this.initData(recordid);
		},
		onShow() {

		},
		methods: {
			getFeeway(){
				var _this = this;
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg(err);
					}
					console.log('getFeeway:', re)
					if(re.num){
						_this.feeway = 2;
					}
				}, ["submch.selectbyyeyid", this.myinfo.yeyid], {
					route: uni.svs.zxx
				})
			},
			initData(recordid) {
				var _this = this;
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg(err.msg | '查询出错');
					}
					console.log('re:', re);
					//m.id,m.name,m.arrdate,m.startdate,m.enddate,r.id as recordid,m.arrdate,m.startdate,m.enddate,r.leavedays,parentstatus,charge_no,pay_img
					_this.objrecord = re;
				}, ["meals.selectbyrecordid", recordid], {
					route: uni.svs.zxx
				})
			},
			toDetail(){
				uni.navigateTo({
					url: "/pages/home/<USER>" + this.objrecord.recordid
				})
			},
			toUpload() {
				uni.navigateTo({
					url: "/pages/home/<USER>" + this.objrecord.recordid
				})
			},
			toPay(){
				var _this  = this;
				uni.smaction(function(re, err){
					uni.hideLoading();
					if(err){
						return uni.msg(err);
					}
					console.log(re);
					uni.requestPayment({
						provider: 'wxpay',
						appId: re.appId,     //公众号ID，由商户传入     
						timeStamp: re.timeStamp,     //时间戳，自1970年以来的秒数     
						nonceStr: re.nonceStr,      //随机串     
						package: re.package,
						signType: re.signType,     //微信签名方式：     
						paySign: re.paySign,
						success(res) {
							console.log('success:', res);
							uni.msg('支付成功');
							var prepage = uni.getPrePage();
							prepage.refresh(3);
						},
						fail(err) {
							console.log('fail:', err);
							uni.msg('支付失败');
						}
					})
				}, {recordid: this.objrecord.recordid}, {route:uni.svs.zxx_home_biz, action: 'homemeals/order'})
			}

		}
	}
</script>


<style>
	page {
		background: #fff;
	}

	.weui-cell {
		padding: 0px 15px;
	}


	.weui-cell__bd {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell__ft {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell:before {
		right: 0px;
		left: 0;
		border-top: none;
	}

	.dining-list {
		border-bottom: 1px solid rgb(235, 235, 235);
		padding: 10px 0;
		margin: 0 15px;
	}

	.dining-list .weui-cell {
		padding: 5px 0px;
	}

	.weui-uploader__input-box {
		width: 165rpx;
		background: #F7F9FC !important;
		height: 165rpx;
	}

	.weui-uploader__file {
		position: relative;
		width: 165rpx;
		height: 165rpx;
	}



	.img-upload {
		width: 165rpx;
		height: 165rpx;

		display: block;

	}

	.dining-days {
		font-size: 28rpx;
		color: #EB7D1C;
		text-align: right;
		text-decoration: underline;
		cursor: pointer;
	}
</style>
