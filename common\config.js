var arrsport = [
    {
        key: 'run',
        itemkey: 'sensitive',
        name: '10m往返跑',
        typenum: 1,
        danwei: '秒',
        danwei2: 's',
        iname: '灵敏',
        desc: '身体灵敏性'
    },
    {
        key: 'jump',
        itemkey: 'lowerlimb',
        name: '立定跳远',
        typenum: 2,
        danwei: '厘米',
        danwei2: 'cm',
        iname: '下肢',
        desc: '下肢力量'
    },
    {
        key: 'softball',
        itemkey: 'upperlimb',
        name: '网球掷远',
        typenum: 2,
        danwei: '米',
        danwei2: 'm',
        iname: '上肢',
        desc: '上肢力量'
    },
    {
        key: 'djump',
        itemkey: 'coordinative',
        name: '双脚持续跳跃',
        typenum: 1,
        danwei: '秒',
        danwei2: 's',
        iname: '协调',
        desc: '协调能力'
    },
    {
        key: 'ante',
        itemkey: 'supple',
        name: '坐位体前屈',
        typenum: 2,
        danwei: '厘米',
        danwei2: 'cm',
        iname: '柔韧',
        desc: '身体柔韧性'
    },
    {
        key: 'balancebeam',
        itemkey: 'balance',
        name: '走平衡木',
        typenum: 1,
        danwei: '秒',
        danwei2: 's',
        iname: '平衡',
        desc: '平衡能力'
    }
];
var objsporttest = {
    sensitive: {
        //"10m往返跑"
        yaoling:
            '测试时，受试者至少两人一组，以站立式起跑姿势在起跑线前，当听到“跑”的口令后，全力跑向折返线，测试员视受试者起动开表计时。受试者跑到折返处，用手触摸到物体后，转身跑向目标线，当胸部到达起点线的垂直面时，测试员停表。',
        zhuyi: '受试者应全速跑，途中不得串道，接近终点时不要减速；在起终点处和目标线处不得站人，以免妨碍测试。',
        wupin: '秒表\n在平坦的地面上画长10米、宽1.22米的直线跑道若干条，在每条跑道折返线处设一手触物体（如木箱），在跑道起终点线外3米处画一条目标线。',
        tianxie: '测试一次，记录以秒为单位，精确到小数点后一位。小数点后第二位数按非“非零进一”的原则进位，如10.11秒记录为10.2秒。'
    },
    lowerlimb: {
        //"立定跳远"
        yaoling: '测试时，受试者双脚自然分开，站立在起跳线后，然后摆动双臂，双脚蹬地尽力向前跳，测量起跳线距最近脚跟之间的直线距离。',
        zhuyi: '受试者起跳时，不能有垫跳动作。',
        wupin: '卷尺；三角尺;沙坑（距沙坑边缘20厘米处设立起跳线）或软地面',
        tianxie: '测试两次，取最大值，记录以厘米为单位，不计小数。'
    },
    upperlimb: {
        //"网球掷远"
        yaoling:
            '测试时，受试者身体面向投掷方向，两脚前后分开，站在投掷线后约一步距离，单手持球举过头顶，尽力向前掷出。球出手时，后脚可以向前迈出一步，但不能踩在或越过投掷线，有效成绩为投掷线至球着地点之间的直线距离。',
        zhuyi: '测试时，严禁学生进入投掷区，避免出现伤害事故。',
        wupin: '卷尺；网球\n在平坦的场地画一长20米，宽6米的长方形，在长方形内，每隔0.5米处画一条横线，以一侧端线为投掷线。',
        tianxie:
            '测试两次，取最大值，记录以米为单位。\n如果球的着地点在横线上，则记录该线所标示的数值；如果球的着地点在两条横线之间，则记录靠近投掷线的横线所标示的数值；如果球的着地点超过20米长的测试场地，可用卷尺丈量；如果球的着地点超出场地的宽度，则重新投掷。'
    },
    coordinative: {
        //"双脚持续跳跃"
        yaoling:
            '测试时，受试者两脚并拢，站在起跳线后，当听到“开始”口令后，双脚同时起跳，双脚一次或两次跳过一块软方包，连续跳过10块软方包。测试员视在受试者起动开表计时，当受试者跳过第十个软方包双脚落地时，测试员停表。',
        zhuyi: '测试时，如果受试者两次单脚起跳跨越软方包、踩在软方包上或将软方包踢乱则重新测试。',
        wupin: '卷尺；秒表；软方包（长10厘米，宽5厘米，高5厘米）10个\n在平坦地面上每隔0.5米画一条横线，共画10条，每条横线上横置一块软方包，在距离第一块软方包20厘米处设立起跳线。',
        tianxie: '测试两次，取最好成绩，记录以秒为单位，精确到小数点后一位。小数点后第二位数按非零进一的原则进位，如10.11秒记录为10.2秒。'
    },
    supple: {
        //"坐位体前屈"
        yaoling:
            '测试时，受试者坐在垫上，双腿伸直，脚跟并拢，脚尖自然分开，全脚掌蹬在测试仪平板上；然后掌心向下，双臂并拢平伸，上体前屈，用双手中指指尖推动游标平滑前进，直到不能移动为止。',
        zhuyi: '测试前，受试者应做好准备活动，以防肌肉拉伤；膝关节不得弯曲，不能有突然前振的动作；记录时，正确填写正负号。',
        wupin: '坐位体前屈测试仪。',
        tianxie: '测试两次，取最大值，记录以厘米为单位，保留小数点后一位。'
    },
    balance: {
        //"走平衡木"
        yaoling: '测试时，受试者站在平台上，面向平衡木，双臂侧平举，当听到“开始”口令后，前进。测试员视在受试者起动开表计时，当受试者任意一个脚尖超过终点线时，测试员停表。',
        zhuyi: '测试时，受试者如中途落地须重试；要安排人员对受试者进行保护。',
        wupin: '秒表\n平衡木（长3米，宽10厘米，高30厘米；平衡木的两端为起点线和终点线，两端外各加一块长20厘米、宽20厘米、高30厘米的平台）。',
        tianxie: '测试两次，取最好成绩，记录以秒为单位，精确到小数点后一位。小数点后第二位数按非零进一的原则进位，如10.11秒记录为10.2秒。'
    }
};
var objjieshao = {
    sensitive: '灵敏性反应儿童快速、准确、协调、的改变身体姿势、运动方向和随机应变的能力。灵敏素质一定程度上反映了大脑发育状况，若发展的好能促进大脑发育。',
    lowerlimb: '下肢力量反应儿童下肢肌肉克服阻力的能力，是影响儿童体育活动的重要因素。',
    upperlimb: '上肢力量反应儿童上肢肌肉克服阻力的能力，是影响儿童体育活动的重要因素。缺乏上肢力量常伴有提东西不稳、扔东西乏力等现象，会影响扔远、提拉等运动。',
    coordinative: '协调性反映儿童身体的不同系统、不同部位和不同器官协调配合完成某一动作的能力。',
    supple: '柔韧能力能保障儿童安全的参与活动，降低活动中不安全事故的风险。柔韧性的提高，对增强身体的协调能力，更好地发挥力量、速度等素质，提高技能和技术，防止运动创伤等都有积极的作用。',
    balance: '平衡能力反映儿童在活动中保持、获得和恢复稳定状态的能力。发展平衡能力有利于提高运动器官的功能和前庭器官的机能，提高适应复杂环境的能力和自我保护的能力。'
};
var objjianyi = {
    sensitive: '通过练习和游戏使学生掌握减速、降重心、转身迅速，而提高灵敏素质和速度。',
    lowerlimb: '提高立定跳远成绩，力量是基础，特别要提高膝、踝、髋三个关节的协调用力及爆发用力的能力。',
    upperlimb: '投掷训练能加强学生的上肢、腰部、腹部等肌肉力量，使上肢关节、韧带得到锻炼，并需要学生投掷出手角度与身体用力的协调性相配合。',
    coordinative:
        '建议孩子多参与韵律操等能锻炼手、眼、肌肉、神经等发展的活动。通过跳跃的练习以及与之相关的游戏使学生掌握跳跃正确方法和姿势，发展跳跃能力，提高学生身体的协调性、灵活性和持久性。',
    supple: '通过体育锻炼能够提高关节的灵活性，改善关节周围软组织的功能以及肌肉、韧带、肌腱的伸展性。',
    balance: '平衡能力可以通过静态的平衡活动和动态的平衡活动来提高。'
};
var objsport = {};

for (var i = 0; i < arrsport.length; i++) {
    objsport[arrsport[i].key] = arrsport[i];
}

var arritem = [
    {
        key: 'height',
        ikey: 'height',
        name: '身高',
        desc: '身高',
        color: '#a590e8',
        stylekey: 'purple',
        dotnum: 1
    },
    {
        key: 'weight',
        ikey: 'weight',
        name: '体重',
        desc: '体重',
        color: '#6db9ff',
        stylekey: 'blue',
        dotnum: 2
    },
    {
        key: 'sensitive',
        ikey: 'run',
        name: '灵敏',
        desc: '灵敏',
        color: '#7ee59a',
        stylekey: 'lightgreen4',
        dotnum: 1
    },
    {
        key: 'lowerlimb',
        ikey: 'jump',
        name: '下肢',
        desc: '下肢力量',
        color: '#37c8cf',
        stylekey: 'green',
        dotnum: 0
    },
    {
        key: 'upperlimb',
        ikey: 'softball',
        name: '上肢',
        desc: '上肢力量',
        color: '#f9a548',
        stylekey: 'orange',
        dotnum: 1
    },
    {
        key: 'coordinative',
        ikey: 'djump',
        name: '协调',
        desc: '协调能力',
        color: '#fb8db6',
        stylekey: 'pink',
        dotnum: 1
    },
    {
        key: 'supple',
        ikey: 'ante',
        name: '柔韧',
        desc: '身体柔韧性',
        color: '#a590e8',
        stylekey: 'purple',
        dotnum: 1
    },
    {
        key: 'balance',
        ikey: 'balancebeam',
        name: '平衡',
        desc: '平衡能力',
        color: '#6db9ff',
        stylekey: 'blue',
        dotnum: 1
    }
];
var objitem = {};

for (var i = 0; i < arritem.length; i++) {
    objitem[arritem[i].key] = arritem[i];
}

var objitem2 = {};

for (var i = 0; i < arritem.length; i++) {
    objitem2[arritem[i].ikey] = arritem[i];
}

module.exports = {
    szs: [
        '安徽',
        '北京',
        '重庆',
        '福建',
        '甘肃',
        '广东',
        '广西',
        '贵州',
        '河北',
        '河南',
        '黑龙江',
        '湖北',
        '湖南',
        '海南',
        '吉林',
        '江西',
        '江苏',
        '辽宁',
        '宁夏',
        '内蒙古',
        '青海',
        '上海',
        '陕西',
        '山东',
        '山西',
        '四川',
        '天津',
        '新疆',
        '西藏',
        '云南',
        '浙江',
        '台湾',
        '香港',
        '澳门'
    ],
    nation: [
        '汉族',
        '蒙古族',
        '回族',
        '藏族',
        '维吾尔族',
        '苗族',
        '彝族',
        '壮族',
        '布依族',
        '朝鲜族',
        '满族',
        '侗族',
        '瑶族',
        '白族',
        '土家族',
        '哈尼族',
        '哈萨克族',
        '傣族',
        '黎族',
        '僳僳族',
        '佤族',
        '畲族',
        '高山族',
        '拉祜族',
        '水族',
        '东乡族',
        '纳西族',
        '景颇族',
        '柯尔克孜族',
        '土族',
        '达斡尔族',
        '仫佬族',
        '羌族',
        '布朗族',
        '撒拉族',
        '毛南族',
        '仡佬族',
        '锡伯族',
        '阿昌族',
        '普米族',
        '塔吉克族',
        '怒族',
        '乌孜别克族',
        '俄罗斯族',
        '鄂温克族',
        '德昂族',
        '保安族',
        '裕固族',
        '京族',
        '塔塔尔族',
        '独龙族',
        '鄂伦春族',
        '赫哲族',
        '门巴族',
        '珞巴族',
        '基诺族',
        '外籍',
        '其他'
    ],
    nationality: [
        '中国',
        '阿富汗',
        '阿尔巴尼亚',
        '阿尔及利亚',
        '美属萨摩亚',
        '安道尔',
        '安哥拉',
        '安圭拉',
        '南极洲',
        '安提瓜和巴布达',
        '阿根廷',
        '亚美尼亚',
        '阿鲁巴',
        '澳大利亚',
        '奥地利',
        '阿塞拜疆',
        '巴哈马',
        '巴林',
        '孟加拉国',
        '巴巴多斯',
        '白俄罗斯',
        '比利时',
        '伯利兹',
        '贝宁',
        '百慕大',
        '不丹',
        '玻利维亚',
        '波黑',
        '博茨瓦纳',
        '布维岛',
        '巴西',
        '英属印度洋领土',
        '文莱',
        '保加利亚',
        '布基纳法索',
        '布隆迪',
        '柬埔寨',
        '喀麦隆',
        '加拿大',
        '佛得角',
        '开曼群岛',
        '中非',
        '乍得',
        '智利',
        '圣诞岛',
        '科科斯(基林)群岛',
        '哥伦比亚',
        '科摩罗',
        '刚果（布）',
        '刚果（金）',
        '库克群岛',
        '哥斯达黎加',
        '科特迪瓦',
        '克罗地亚',
        '古巴',
        '塞浦路斯',
        '捷克',
        '丹麦',
        '吉布提',
        '多米尼克',
        '多米尼加共和国',
        '东帝汶',
        '厄瓜多尔',
        '埃及',
        '萨尔瓦多',
        '赤道几内亚',
        '厄立特里亚',
        '爱沙尼亚',
        '埃塞俄比亚',
        '福克兰群岛(马尔维纳斯)',
        '法罗群岛',
        '斐济',
        '芬兰',
        '法国',
        '法属圭亚那',
        '法属波利尼西亚',
        '法属南部领土',
        '加蓬',
        '冈比亚',
        '格鲁吉亚',
        '德国',
        '加纳',
        '直布罗陀',
        '希腊',
        '格陵兰',
        '格林纳达',
        '瓜德罗普',
        '关岛',
        '危地马拉',
        '几内亚',
        '几内亚比绍',
        '圭亚那',
        '海地',
        '赫德岛和麦克唐纳岛',
        '洪都拉斯',
        '匈牙利',
        '冰岛',
        '印度',
        '印度尼西亚',
        '伊朗',
        '伊拉克',
        '爱尔兰',
        '以色列',
        '意大利',
        '牙买加',
        '日本',
        '约旦',
        '哈萨克斯坦',
        '肯尼亚',
        '基里巴斯',
        '朝鲜',
        '韩国',
        '科威特',
        '吉尔吉斯斯坦',
        '老挝',
        '拉脱维亚',
        '黎巴嫩',
        '莱索托',
        '利比里亚',
        '利比亚',
        '列支敦士登',
        '立陶宛',
        '卢森堡',
        '前南马其顿',
        '马达加斯加',
        '马拉维',
        '马来西亚',
        '马尔代夫',
        '马里',
        '马耳他',
        '马绍尔群岛',
        '马提尼克',
        '毛里塔尼亚',
        '毛里求斯',
        '马约特',
        '墨西哥',
        '密克罗尼西亚联邦',
        '摩尔多瓦',
        '摩纳哥',
        '蒙古',
        '蒙特塞拉特',
        '摩洛哥',
        '莫桑比克',
        '缅甸',
        '纳米比亚',
        '瑙鲁',
        '尼泊尔',
        '荷兰',
        '荷属安的列斯',
        '新喀里多尼亚',
        '新西兰',
        '尼加拉瓜',
        '尼日尔',
        '尼日利亚',
        '纽埃',
        '诺福克岛',
        '北马里亚纳',
        '挪威',
        '阿曼',
        '巴基斯坦',
        '帕劳',
        '巴勒斯坦',
        '巴拿马',
        '巴布亚新几内亚',
        '巴拉圭',
        '秘鲁',
        '菲律宾',
        '皮特凯恩群岛',
        '波兰',
        '葡萄牙',
        '波多黎各',
        '卡塔尔',
        '留尼汪',
        '罗马尼亚',
        '俄罗斯联邦',
        '卢旺达',
        '圣赫勒拿',
        '圣基茨和尼维斯',
        '圣卢西亚',
        '圣皮埃尔和密克隆',
        '圣文森特和格林纳丁斯',
        '萨摩亚',
        '圣马力诺',
        '圣多美和普林西比',
        '沙特阿拉伯',
        '塞内加尔',
        '塞舌尔',
        '塞拉利昂',
        '新加坡',
        '斯洛伐克',
        '斯洛文尼亚',
        '所罗门群岛',
        '索马里',
        '南非',
        '南乔治亚岛和南桑德韦奇岛',
        '西班牙',
        '斯里兰卡',
        '苏丹',
        '苏里南',
        '斯瓦尔巴群岛',
        '斯威士兰',
        '瑞典',
        '瑞士',
        '叙利亚',
        '塔吉克斯坦',
        '坦桑尼亚',
        '泰国',
        '多哥',
        '托克劳',
        '汤加',
        '特立尼达和多巴哥',
        '突尼斯',
        '土耳其',
        '土库曼斯坦',
        '特克斯科斯群岛',
        '图瓦卢',
        '乌干达',
        '乌克兰',
        '阿联酋',
        '英国',
        '美国',
        '美国本土外小岛屿',
        '乌拉圭',
        '乌兹别克斯坦',
        '瓦努阿图',
        '梵蒂冈',
        '委内瑞拉',
        '越南',
        '英属维尔京群岛',
        '美属维尔京群岛',
        '瓦利斯和富图纳',
        '西撒哈拉',
        '也门',
        '南斯拉夫',
        '赞比亚',
        '津巴布韦'
    ],
    locationtype: ['城镇户口', '农村户口'],
    location: ['本片', '本片外区', '本市外区', '外埠', '外籍', '港澳台'],
    credentialstype: ['居民身份证', '香港特区护照/身份证明', '澳门特区护照/身份证明', '台湾居民来往大陆通行证', '境外永久居住证', '其他'],
    status_xingtai: {
        //+1 肥胖 +3 偏上 5 标准 -3 偏下 -1 消瘦
        '+1': '肥胖',
        '+3': '偏上',
        5: '标准',
        '-3': '偏下',
        '-1': '消瘦'
    },
    objshaobuchongtype: {
        油脂类: '膨化食品、西式快餐、肥肉、黄油、油炸食品',
        糖类: '糖果、蜜饯、巧克力、冷饮、甜点心、含糖饮料'
    },
    objnut: {
        pro: {
            nutname: '蛋白质',
            danwei: 'g'
        },
        df: {
            nutname: '膳食纤维',
            danwei: 'g',
            fuhan: 6 //每100克含多少算富含
        },
        fat: {
            nutname: '脂肪',
            danwei: 'g'
        },
        ch: {
            nutname: '碳水化合物',
            danwei: 'g'
        },
        ca: {
            nutname: '钙',
            danwei: 'mg',
            cujin: '维生素D、乳糖、寡糖、氨基酸、适宜的钙磷比2:1',
            jiangdi: '草酸、植酸、膳食纤维中的糖醛酸残基、饱和脂肪酸',
            jieshao:
                '钙是体内最丰富的矿物质。主要以羟磷灰石的结晶形式存在于骨骼与牙齿中, 钙和主要用来形成骨骼，而且超过90％的钙和磷都存在于骨骼中。钙是神经传导、肌肉收缩以及糖原合成和分解所必需的。磷作为DNA、ATP、磷酸肌酸和2，3－二磷酸甘油酸（2，3－DPG）的组成分别存在于每个细胞中，2，3－DPG调节运动期间向肌肉释放的氧气。吸收：钙盐在酸性溶液中较易溶解，因此吸收作用主要在小肠的上部（小肠近端或十二指肠区），但吸收极不完全。在普通膳食中，摄入的钙通常仅20％～30％由小肠吸收并进入血液中。正在生长的儿童、孕妇和乳母对钙的利用最有效，他们吸收膳食中40％或更多的钙。钙和磷的膳食来源包括干酪、牛奶、奶制品、小麦、大豆粉、杏仁、鱼子酱、无花果、带可食软骨的鱼、绿叶蔬菜、花椰菜、甘蓝、萝ト叶、牡蛎、虾类、蛋类等。钙的优质来源>23mg/100g。',
            fuhan: 23 //每100克含多少算富含
        },
        fe: {
            nutname: '铁',
            danwei: 'mg',
            cujin: '维生素C、肉、鱼、海产品、有机酸',
            jiangdi: '植酸、多酚（茶、咖啡、可可、菠菜、柿子）、过多的钙',
            jieshao:
                '铁这种金属元素对于形成用于氧气输送和利用的化合物至关重要。血红蛋白是最主要的含铁化合物，能够将氧输送到细胞。细胞的铁化合物包括肌红蛋白、细胞色素和三羧酸循环中的一些酶。几乎30％的铁储存在组织中，70％的铁参与氧代谢。与非血红素铁相比，血红素铁更易于吸收和使用（10％～35％vs2％～10％）。铁含量丰富的食物有：菌藻类、红蘑、发菜、口蘑、黑木耳、动物肝脏、动物全血、动物瘦肉、鱼类、花生、核桃、麦糠、麦胚、绿叶蔬菜等，奶和乳制品都是铁的良好来源。铁的优质来源>2.5mg/100g。',
            fuhan: 2.5 //每100克含多少算富含
        },
        zn: {
            nutname: '锌',
            danwei: 'mg',
            cujin: '蛋白质、有机酸',
            jiangdi: '高钙、高铁、高膳食纤维',
            jieshao:
                '锌存在于身体的各个组织中，是100多种金属酶的组成成分。含有锌的酶参与能量代谢，包括氧气、二氧化碳传输（碳酸酐酶）和乳酸代谢（乳酸脱氢酶），还能控制大量营养元素的分解和合成、生长和发展、免疫功能和伤口愈合。动物性食物锌的生物利用率大于植物性食物、前者为35%～40%，后者为1%～20%。一般来说高蛋白食物锌含量＞海产品＞奶及蛋＞蔬菜、水果。海鲜、肉类、利马豆、黑眼豆、白豆、全谷物产品和强化谷物是锌的来源。锌的优质来源>4.05mg/100g。',
            fuhan: 4.05 //每100克含多少算富含
        },
        va: {
            nutname: '维生素A',
            danwei: 'ug',
            jieshao:
                '维生素A的生物活性形式是视黄醇其可以由β－胡萝ト素（维生素原）形成。维生素A保护上皮细胞免受损伤，在保护视力中起重要作用，并且有助于维持免疫功能。在运动中，维生素A主要被用作一种抗氧化剂。维生素A存在于动物性食物中，尤其在动物的肝脏、蛋黄、乳制品和鱼肝油中含量最高。胡萝ト素是视黄醇的前体，主要存在于黄色的蔬菜和水果中，以及深色绿叶蔬菜中，以胡萝卜、绿叶蔬菜和某些水果中含量较多。富含维生素A、胡萝卜素的食物>24ugRE/100g、890ug/100g。',
            fuhan: 24 //每100克含多少算富含
        },
        vb1: {
            nutname: '维生素B1',
            danwei: 'mg',
            jieshao:
                '维生素B1: 硫胺素。维生素B1的生物活性形式是焦磷酸硫胺素，其作为碳水化合物和蛋白质的代谢中的辅酶协助产生能量。在三羧酸循环中，它协助将丙酮酸转化为乙酰辅酶A，将a-酮戊二酸转化为琥珀酰辅酶A来产生能量，在运动中尤其如此。硫胺素也参与支链氨基酸的脱羧反应（去除一个CO2基团），从而有助于肌肉产生能量。很多食物中都含有硫胺素，动物的内脏（肝、心、肾等）及猪肉含量较丰富，豆类、谷类、硬果类也是良好的来源，但这类食物易在加工或烹调过程中丢失，应尽量避免。维生素B1的优质来源>0.16mg/100g。',
            fuhan: 0.16 //每100克含多少算富含
        },
        vb2: {
            nutname: '维生素B2',
            danwei: 'mg',
            jieshao:
                '维生素B2: 核黄素。核黄素在线粒体电子链传递系统中作为辅酶黄素单核苷酸（FMN）和黄素腺嘌呤二核苷酸（FAD）发挥作用。碳水化合物和脂肪分解后，这些酶会参与电子传递来生成三磷酸腺苷（ATP）。维生素B转化为活性形式时也需要核黄素。核黄素的食物来源包括动物的脏器（肝、肾、心）、蘑菇、鳝鱼是核黄素的丰富来源，蟹、牛奶和奶制品、蛋、茶叶、花椰菜、苜蓿、杏仁等也是良好来源。但核黄素可被光破坏和具有在碱性溶液中加热易破坏的性质，因此，在加工、烹任和贮藏过程中应严加注意。维生素B2的优质来源>0.3mg/100g。',
            fuhan: 0.3 //每100克含多少算富含
        },
        vc: {
            nutname: '维生素C',
            danwei: 'mg',
            jieshao:
                '维生素C:抗坏血酸。维生素C具有多种能够影响运动表现的生物功能。虽然維生素C不会直接影响作用，但是人体需要使用维生素C来合成茶酚胺和肉碱，肉碱可以将脂肪酸转运到线粒体来产生能量。维生素C可以减少无机铁在肠中的吸收，也可以作为一种强有力的抗氧化剂从其氧化的副产品中再生成维生素E（1OM，2000）。维生素C主要来自于植物性食物新鲜的水果和蔬菜中，（特别是柑橘类水果和绿叶蔬菜），例如花椰菜、土豆、西红柿和草莓,尤其是刺梨（我国西南部）、樱桃（西印度）、枣类含量最丰富，枣类被人体利用率也可高达86％。维生素C的优质来源>19mg/100g。',
            fuhan: 19 //每100克含多少算富含
        }
    },
    status_xingtai_color: ['greentxt', 'redtxt', 'yellowtxt', 'bluetxt', 'yellowtxt', 'redtxt'],
    status_suzhi: ['', '较弱', '及格', '良好', '优秀'],
    status_onesuzhi: ['', '较弱', '及格', '良好', '优秀'],
    objheight: {
        title: '身高/年龄',
        typenum: 3
    },
    objweight: {
        title: '身高/体重'
    },
    arrsport: arrsport,
    arritem: arritem,
    objitem: objitem,
    objitem2: objitem2,
    objsport: objsport,
    objsporttest: objsporttest,
    objjieshao: objjieshao,
    objjianyi: objjianyi
};
