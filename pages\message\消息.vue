<template>
	<view>
	
		<view class="messagelist">
			
			<view class="weui-cell" style="align-items: flex-start;" >
				
				<image src="../message/static/image/messae/jcnf.png" class="jcnf">
				<view class="weui-cell__bd">
					<view class="weui-flex blocktxt"  style="justify-content: space-between;"><view>就餐缴费</view><view  class="graytit">2025-01-03 09:00:02</view></view>
					<view class="weui-flexcen graytit"  style="justify-content: space-between; margin-top: 5rpx;"><view>缴费账单已送达，去查看。</view><text></text></view>
			
				</view>
			</view>
			
			
		</view>
		<view class="messagelist">
			
			<view class="weui-cell" style="align-items: flex-start;" >
				
				<image src="../message/static/image/messae/jcqr.png" class="jcnf">
				<view class="weui-cell__bd">
					<view class="weui-flexcen blocktxt"  style="justify-content: space-between;"><view>就餐确认</view><view  class="graytit">2025-01-03 09:00:02</view></view>
					<view class="weui-flexcen graytit"  style="justify-content: space-between; margin-top: 5rpx;"><view>缴费账单已送达，去查看。</view><text></text></view>
			
				</view>
			</view>
			
			
		</view>
		<view class="messagelist">
			
			<view class="weui-cell" style="align-items: flex-start;" >
				
				<image src="../message/static/image/messae/jcqr.png" class="jcnf">
				<view class="weui-cell__bd">
					<view class="weui-flexcen blocktxt"  style="justify-content: space-between;"><view>就餐确认</view><view  class="graytit">2025-01-03 09:00:02</view></view>
					<view class="weui-flexcen graytit"  style="justify-content: space-between; margin-top: 5rpx;"><view>缴费账单已送达，去查看。</view><text></text></view>
			
				</view>
			</view>
			
			
		</view>
		
	</view>
</template>

<script>
</script>

<style>
	page {
		background: #F2F4F5;
	}

	.weui-cell {
		padding: 15px;
	}

	.weui-cell .weui-cell__bd {
		font-size: 14px;
		color: #999999;
	}

	.messagetxt {
		width: 102rpx;
		height: 40rpx;
		background: #C7C7CC;
		border-radius: 50rpx;
		text-align: center;
		margin: 10px auto;
		font-size: 24rpx;
		color: #FFFFFF;
	}

	.messagelist {
		background: #FFFFFF;
		border-radius: 10rpx;
		font-size: 28rpx;
		margin: 15px 30rpx;
		padding: 0rpx;
	}

	.messagelist text {
		width: 15rpx;
		height: 15rpx;
		background: #EE464F;
		margin-left: 10rpx;
		display: inline-block;
		border-radius: 25px;
		
	}
	.jcnf{width: 90rpx; height:90rpx; margin-right: 10rpx;}
</style>