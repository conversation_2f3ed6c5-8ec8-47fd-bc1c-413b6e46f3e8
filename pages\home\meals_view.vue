<template>
	<view>
		<view></view>
		<view class="marbot10">
			<view class="yllist">
				<view class="weui-cell" style="border-bottom: 1px solid #EBEBEB;">
					<view class="weui-cell__bd">
						<view class="sel-person-list green-left">
							<view class="weui-flex " style="align-items: center;">缴费项目</view>
						</view>
					</view>
					<view class="weui-cell__ft">{{objrecord.name}}
					</view>
				</view>
				<view class="weui-cell" style="padding: 10px 15px;">
					<view class="weui-cell__bd" style="color: #999999;">
						收费单号
					</view>
					<view class="weui-cell__ft">{{objrecord.charge_no}}
					</view>
				</view>
				<view class="weui-cell" style="padding: 0px 15px;">
					<view class="weui-cell__bd" style="color: #999999;">
						缴费状态
					</view>
					<view class="weui-cell__ft">
						{{objrecord.parentstatus <= 2 ? '未确认' : objrecord.parentstatus == 3 ? '未缴费' : '已缴费'}}
					</view>
				</view>
				<view class="weui-cell" style="padding:10px 15px;">
					<view class="weui-cell__bd" style="color: #999999;">
						缴费凭证
					</view>
				</view>
				<view class="weui-uploader__bd"
					style="margin: 0px 30rpx;border-bottom: 1px solid #EBEBEB;padding-bottom:5px;">
					<view class="weui-uploader__files" style="padding-top: 5px;">
						<view v-for="(path,i) in arrimg" :key="i" class="weui-uploader__file">
							<image :src="prefix + path" class="img-upload"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js");
	var app = getApp();
	var pagesize = 10;

	export default {
		data() {
			return {
				myinfo: app.globalData.objuserinfo,
				prefix: app.globalData.ossPrefix,
				objrecord: {},
				arrimg: []
			}
		},
		onLoad: function(params) {
			var recordid = params.recordid;
			this.initData(recordid);
		},
		onShow() {

		},
		methods: {
			initData(recordid) {
				var _this = this;
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg(err.msg | '查询出错');
					}
					console.log('re:', re);
					//m.id,m.name,m.arrdate,m.startdate,m.enddate,r.id as recordid,m.arrdate,m.startdate,m.enddate,r.leavedays,parentstatus,charge_no,pay_img
					_this.objrecord = re;
					_this.arrimg = re.pay_img ? re.pay_img.split(",") : [];
				}, ["meals.selectbyrecordid", recordid], {
					route: uni.svs.zxx
				})
			},
			toConfirm(recordid) {
				uni.navigateTo({
					url: "/pages/home/<USER>" + recordid
				})
			}

		}
	}
</script>


<style>
	page {
		background: #fff;
	}

	.weui-cell {
		padding: 0px 15px;
	}


	.weui-cell__bd {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell__ft {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell:before {
		right: 0px;
		left: 0;
		border-top: none;
	}

	.remarks-list {
		background: #fff;
		margin-top: 10px;
		padding: 15px;
	}

	.weui-uploader__input-box {
		width: 165rpx;
		background: #F7F9FC !important;
		height: 165rpx;
	}

	.weui-uploader__file {
		position: relative;
		width: 165rpx;
		height: 165rpx;
	}



	.img-upload {
		width: 165rpx;
		height: 165rpx;

		display: block;

	}
</style>
