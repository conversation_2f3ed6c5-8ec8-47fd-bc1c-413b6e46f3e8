<template>
	<view class="content">
		<!-- 下拉刷新组件 -->
		<mix-pulldown-refresh ref="mixPulldownRefresh" :top="0" class="panel-content" @refresh="onPulldownRereshnews" @setEnableScroll="setnewsEnableScroll">
			<view class="whitebg  toplinebg" style="margin-bottom: 5px;">
				<view class="weui-flex" style="padding: 10px;">
					<view @click="markAllAsRead" class="border-rgt weui-flex_center ">
						<image src="/pages/home/<USER>/image/purgeimg.png" style="width: 25rpx;height: 25rpx; margin-right: 5px;" /><text v-if="unreadCount > 0" class="redtxt">{{unreadCount}}</text>
					</view>
					<view class="border-rgt weui-flex_center " style="margin-left: 15px;" @click="toggleSearchBox">
						<view>搜索</view>
						<image src="/pages/home/<USER>/image/seachimg.png" style="width: 25rpx;height: 25rpx; margin-left: 5px;" />
					</view>
					<view @click="showPop" class="border-rgt weui-flex_center" style="margin-left: 15px;">
						<view>筛选</view>
						<image src="/pages/home/<USER>/image/filteringimg.png" style="width: 24rpx;height: 25rpx; margin-left: 5px;" />
						<text v-if="arrapply.length > 0" class="droptxt">{{ arrapply.length }}</text>
					</view>
				</view>


				<view v-if="arrapply.length > 0" class="filter-results redtit">筛选结果：
					<view v-for="(item, i) in arrapply" :key="i" class="weui-flexcen"> {{item.text}} <i @click="delWhere(item.type)" class="iconfont icon_close2"></i></view>
				</view>


				<transition name="fade">
					<view v-show="showSearchBox" class="def-search topviewnebg">
						<view class="weui-search-bar weui-search-bar_focusing" id="searchBar">

							<view class="weui-search-bar__form">
								<view class="weui-search-bar__box weui-flex" style="align-items: center; padding: 0 30rpx">
									<image src="https://osstbfile.tb-n.com/xcxscale/static/image/icon_search.png" style="width: 30rpx; height: 30rpx; margin-right: 25rpx"></image>
									<input v-model="searchtxt" placeholder-class="phcolor" class="weui-input weui-flex__item" placeholder="请输入关键字" style="font-size: 14px" />
									<image v-if="searchtxt" @click="delSearchtxt" src="https://osstbfile.tb-n.com/xcxscale/static/image/icon_close.png" style="width: 30rpx; height: 30rpx"></image>
								</view>
							</view>
							<view class="weui-flex" style="align-items: center; ">
								<text @click="search" style="margin-left: 5px; color:#C23C43; font-size: 14px;width:28px;">搜索</text>
							</view>
						</view>
					</view>
				</transition>
			</view>

			<!-- 内容部分 -->
			<scroll-view class="panel-scroll-box toplinebg" :scroll-y="newsenableScroll" @scrolltolower="loadnewsMore" style="">
				<view v-for="(item, index) in objnews.List" :key="index" class="messagelist ">
					<view @click="newsDetail(item)" :data-id="item.id" class="weui-cell" style="align-items: flex-start;">
						<image :src="'../../../static/image/home/<USER>'+item.msgtype+'.png'" class="jcnf" />
						<view class="weui-cell__bd">
							<view class="weui-flex blocktxt" style="justify-content: space-between;">
								<view>{{msgTypeObj[item.msgtype]}}</view>
								<view class="graytit">{{item.msgtime}}</view>
							</view>
							<view class="weui-flexcen graytit" style="justify-content: space-between; margin-top: 5rpx;">
								<view>{{item.titile}}</view>
								<block v-if="item.isread==0">
									<text></text>
								</block>
							</view>
						</view>
					</view>
				</view>

				<!-- 上滑加载更多组件 -->
				<mix-load-more :status="objnews.loadMoreStatus"></mix-load-more>
			</scroll-view>

		</mix-pulldown-refresh>


		<!--筛选弹窗-->
		<view v-if="isshow" @click="hidePop" class="shadow-con drop-down">
			<view @click.stop="" class="shadow-box">
				<view class="mgbot70">
					<view class="screen-selbot">
						<view class="filter-lt">筛选日期 </view>
						<view class="screen-sellist">
							<view><text @click="changeTimeIndex(1)" :class="timeIndex == 1 ? 'current' : ''">今天</text>
							</view>
							<view><text @click="changeTimeIndex(2)" :class="timeIndex == 2 ? 'current' : ''">昨天</text>
							</view>
							<view><text @click="changeTimeIndex(3)" :class="timeIndex == 3 ? 'current' : ''">本周</text>
							</view>
							<view><text @click="changeTimeIndex(4)" :class="timeIndex == 4 ? 'current' : ''">上周</text>
							</view>
							<view><text @click="changeTimeIndex(5)" :class="timeIndex == 5 ? 'current' : ''">本月</text>
							</view>
							<!-- <view><text @click="changeTimeIndex(6)" :class="timeIndex == 6 ? 'current' : ''">上月</text></view>
							<view><text @click="changeTimeIndex(7)" :class="timeIndex == 7 ? 'current' : ''">最近7天</text></view>
							<view><text @click="changeTimeIndex(8)" :class="timeIndex == 8 ? 'current' : ''">3个月</text></view> -->
							<view><text @click="changeTimeIndex(9)" :class="timeIndex == 9 ? 'current' : ''">自定义</text>
							</view>
						</view>
					</view>
					<view class="custom-time weui-flex_center" style="clear: both;margin: 20px 20px 0 15px;">
						<view class="custom-list weui-flex_center">
							<image src="static/image/timeico.png" style="width: 10px; height: 10px;"></image>
							<picker mode="date" :value="startdate" :disabled="timeIndex == 9 ? false : true" @change="chooseStartDate">
								<input type="text" class="time-input" :value="startdate" disabled="true" placeholder="选择日期">
							</picker>
						</view>
						<text>~</text>
						<view class="custom-list weui-flex_center">
							<image src="static/image/timeico.png" style="width: 10px; height: 10px;"></image>
							<picker mode="date" :value="enddate" :disabled="timeIndex == 9 ? false : true" @change="chooseEndDate">
								<input type="text" class="time-input" :value="enddate" disabled="true" placeholder="选择日期">
							</picker>
						</view>
					</view>
					<view class="screen-selbot">
						<view class="filter-lt">消息类型 </view>
						<view class="screen-sellist">
							<view><text @click="changeTypeIndex(2)" :class="msgtype == 2 ? 'current' : ''">就餐确认</text>
							</view>
							<view><text @click="changeTypeIndex(3)" :class="msgtype == 3 ? 'current' : ''">就餐缴费</text>
							</view>
							<view><text @click="changeTypeIndex(4)" :class="msgtype == 4 ? 'current' : ''">请假申请</text>
							</view>
							<view><text @click="changeTypeIndex(5)" :class="msgtype == 5 ? 'current' : ''">财务审核</text>
							</view>
						</view>
					</view>
					<view class="screen-selbot">
						<view class="filter-lt">消息状态 </view>
						<view class="screen-sellist">
							<view><text @click="changeStatusIndex(1)" :class="statusIndex == 1 ? 'current' : ''">已读</text></view>
							<view><text @click="changeStatusIndex(0)" :class="statusIndex === 0 ? 'current' : ''">未读</text></view>
						</view>
					</view>
				</view>
			</view>
			<view @click.stop="" style="padding: 15px 0;position: fixed;bottom: 0;width: 90%;z-index: 9; background: #fff; right: 0;">
				<!--保存发布按钮-->
				<view class="weui-flex" style="margin:0px 15px; border: 1px solid #C23C43;border-radius:40px; background: #C23C43;">
					<a href="javascript:;" @click="reset" class="weui-btn weui-flex__item" style="background:#FFECEF;border-radius:40px 0 40px 40px;color: #C23C43;">重置</a>
					<a href="javascript:;" @click="apply" class="weui-btn" style="background: #C23C43;border-radius:0 40px 40px 0;margin: 0;width:49%;">确认 </a>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import mixPulldownRefresh from '@/components/mix-pulldown-refresh/mix-pulldown-refresh';
	import mixLoadMore from '@/components/mix-load-more/mix-load-more';
	import MessageUtil from '../../../util/sendMsg.js';
	var util = require("../../../common/util.js");
	let windowWidth = 0,
		scrollTimer = false,
		tabBar;
	var app = getApp();
	var pagesize = 10;
	export default {
		components: {
			mixPulldownRefresh,
			mixLoadMore
		},
		data() {
			return {
				menutype: '1',
				menu2type: '1',
				hasbaby: 0,
				prefix: app.globalData.childhomeurl + "/",
				tabCurrentIndex: 0, //当前选项卡索引
				scrollLeft: 0, //顶部选项卡左滑距离
				enableScroll: true,
				newsenableScroll: true,
				arrtop: [],
				tabBars: [],
				objbaby: {},
				objnews: {
					List: [],
					loadMoreStatus: 0,
					refreshing: 0,
					pageIndex: 0,
					isload: 0
				},
				arrapply: [],
				searchtxt: '',
				isshow: false,
				statusIndex: '',
				statusIndexText: '',
				startdate: '',
				enddate: '',
				timeIndex: 0,
				timeIndexText: '',
				msgtype: 0,
				typeIndexText: '',
				swhere: {},
				msgTypeObj: {
					1: '消息通知',
					2: '就餐确认',
					3: '就餐缴费',

				},
				showSearchBox: false,
				//classboxheight: 500
			}
		},
		async onLoad() {
			// 获取屏幕宽度
			windowWidth = uni.getSystemInfoSync().windowWidth;
			this.loadNewsList('add');
			this.Subscribe(); //订阅消息
		},
		onReady() {
			//this.classboxheight = uni.getSystemInfoSync().screenHeight + 360;
		},
		methods: {
			logincallback: function() {
				this.loadNewsList('add');
			},
			//新闻资讯
			//设置scroll-view是否允许滚动，在小程序里下拉刷新时避免列表可以滑动
			setnewsEnableScroll(enable) {
				if (this.newsenableScroll !== enable) {
					this.newsenableScroll = enable;
				}
			},
			//下拉刷新
			onPulldownRereshnews() {
				this.loadNewsList('refresh');
			},
			//上滑加载
			loadnewsMore() {
				this.loadNewsList('add');
			},
			//列表
			loadNewsList(type) {
				// if (!app.checkLogin()) { //未登录
				// 	return uni.navigateTo({
				// 		url: "/pages/login/login?frompage=foodhealth"
				// 	})
				// } 
				var _this = this;
				let tabItem = this.objnews;
				//type add 加载更多 refresh下拉刷新
				if (type === 'add') {
					if (tabItem.loadMoreStatus === 2) {
						return;
					}
					tabItem.pageIndex++;
					tabItem.loadMoreStatus = 1;
				} else if (type === 'refresh') {
					tabItem.pageIndex = 1;
					tabItem.refreshing = true;
				}
				if (this.searchtxt) {
					this.swhere.titile = [this.searchtxt];
				} else {
					delete this.swhere.titile;
				}
				var uid = app.globalData.objuserinfo && app.globalData.objuserinfo.id || 0;
				uni.sm(function(res, err) {
					if (err) {
						tabItem.loadMoreStatus = 2;
						return uni.msg(err.msg);
					} else {
						if (type === 'refresh') {
							tabItem.List = []; //刷新前清空数组
						}
						var re = res[0];
						for (var i = 0; i < re.length; i++) {
							if (re[i].imgurl) {
								var arrimgurl = JSON.parse(re[i].imgurl);
								re[i].imgurl = arrimgurl[0].url;
							}
						}
						if (re.length > 0) {
							tabItem.List = tabItem.List.concat(re);
						}
						//下拉刷新 关闭刷新动画
						if (type === 'refresh') {
							_this.$refs.mixPulldownRefresh && _this.$refs.mixPulldownRefresh.endPulldownRefresh();
							// #ifdef APP-PLUS
							tabItem.refreshing = false;
							// #endif
							tabItem.loadMoreStatus = 0;
						}
						var totalcount = res[1].totalcount;
						tabItem.loadMoreStatus = tabItem.List.length >= totalcount ? 2 : 0;
					}
				}, [
					["xcx.home.messagelist", uid, (tabItem.pageIndex - 1) * pagesize, pagesize, uni.msgwhere(_this.swhere)],
					["xcx.home.messagecount", uid, uni.msgwhere(_this.swhere)]
				], {
					route: uni.svs.zxx_home_biz
				});
			},
			newsDetail(msgObj) {
				var _this = this;
				this.Subscribe(function(res) {
					//更新消息为已读
					if (msgObj.isread == 0) {
						_this.updateMsgRead(msgObj.id);
					}

					if (res.errMsg == 'requestSubscribeMessage:ok') {
						var url = '';
						if (msgObj.msgtype == 1) {
							url = msgObj.otherdata ? JSON.parse(msgObj.otherdata).url : '';
						} else if (msgObj.msgtype == 2) {
							return uni.alert(msgObj.titile);
						} else if (msgObj.msgtype == 3) {
							return uni.alert(msgObj.titile);
						}
						if (!url) {
							return uni.alert(msgObj.titile);
						}
						uni.navigateTo({
							url: url
						})
					} else {
						console.warn('用户未成功订阅', res);
					}
				});
			},

			//订阅消息
			Subscribe(cb) {
				uni.requestSubscribeMessage({
					//此处填写刚才申请模板的模板ID
					tmplIds: [MessageUtil.getTemplateId('xcxhome', 1)],
					success(res) {
						console.log(res)
						cb && cb(res)
					}
				})
			},
			updateMsgRead(msgid) {
				var _this = this;
				uni.sm(function(re, err) {
					if (re) {
						_this.onPulldownRereshnews();
					}
				}, ["xcx.home.messageupdateread", uni.msgwhere({
					ids: uni.msgpJoin([msgid])
				}), 1], {
					route: uni.svs.zxx_home_biz
				})
			},

			// 发送消息
			sendMsg() {
				let self = this
				//下方的thing1，thing2和其他，对应的是你选取模板的模板详情中的字段名称(可在小程序后台模板查看对应的字段，要和上面的字段一样)，需要更改成你自己的
				const pushmsg = {
					"touser": app.globalData.objuserinfo.openid_xcx,
					"template_id": "SxWEPUY0eaZxFKGt-uyy31-WiCQRVJ6YYczIcfr5XS8",
					"data": {
						"time2": {
							"value": "20:00"
						},
						"thing3": {
							"value": "这是个消息内容"
						},
						"thing4": {
							"value": "这是一个备注哦。。"
						}
					}
				}
				uni.request({
					// 此处的mytoken
					url: 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=' + this.get_wx_access_token(),
					data: JSON.stringify(pushmsg),
					method: 'POST',
					success: function(res) {
						// console.log('success');
					}
				})
			},
			showPop() {
				this.isshow = true;
			},
			hidePop() {
				this.isshow = false;
			},
			changeTimeIndex(index) {
				this.timeIndex = index;
				var today = new Date();
				if (index == 1) { //今天
					this.startdate = util.getStrByDate(today);
					this.enddate = util.getStrByDate(today);
					this.timeIndexText = '今天';
				} else if (index == 2) { //昨天
					this.startdate = util.getpreday(util.getStrByDate(today), 1);
					this.enddate = util.getpreday(util.getStrByDate(today), 1);
					this.timeIndexText = '昨天';
				} else if (index == 3) { //本周
					this.startdate = util.getMondayDate(today);
					this.enddate = util.getSundayDate(today);
					this.timeIndexText = '本周';
				} else if (index == 4) { //上周
					var date = util.getpreday(util.getStrByDate(today), 7);
					date = util.getDateByStr(date);
					this.startdate = util.getMondayDate(date);
					this.enddate = util.getSundayDate(date);
					this.timeIndexText = '上周';
				} else if (index == 5) { //本月
					this.startdate = util.getMonthFirstDay(today);
					this.enddate = util.getMonthLastDay(today);
					this.timeIndexText = '本月';
				} else if (index == 6) { //上月
					this.startdate = util.getMonthFirstDay(today, -1);
					this.enddate = util.getMonthLastDay(today, -1);
					this.timeIndexText = '上月';
				} else if (index == 7) { //最近7天
					this.startdate = util.getpreday(util.getStrByDate(today), 6);
					this.enddate = util.getStrByDate(today);
					this.timeIndexText = '最近7天';
				} else if (index == 8) { //3个月
					this.startdate = util.getMonthDate(util.getStrByDate(today), -3);
					this.enddate = util.getStrByDate(today);
					this.timeIndexText = '3个月';
				} else if (index == 9) { //自定义
					this.startdate = '';
					this.enddate = '';
					this.timeIndexText = '自定义';
				}
			},
			chooseStartDate(e) {
				this.startdate = e.detail.value;
			},
			chooseEndDate(e) {
				this.enddate = e.detail.value;
			},
			changeStatusIndex(index) {
				this.statusIndex = index;
				if (index == 1) {
					this.isread = 1;
					this.statusIndexText = '已读';
				} else if (index == 0) {
					this.isread = 0;
					this.statusIndexText = '未读';
				}
			},
			changeTypeIndex(index) {
				//消息类型 1:'消息通知',2:'就餐确认',3:'就餐缴费',4:'请假申请',5:'财务审核'
				this.msgtype = index;
				if (index == 1) {
					this.typeIndexText = '消息通知';
				} else if (index == 2) {
					this.typeIndexText = '就餐确认';
				} else if (index == 3) {
					this.typeIndexText = '就餐缴费';
				} else if (index == 4) {
					this.typeIndexText = '请假申请';
				} else if (index == 5) {
					this.typeIndexText = '财务审核';
				}
			},
			reset() {
				this.timeIndex = 0;
				this.timeIndexText = '';
				this.startdate = '';
				this.enddate = '';
				this.statusIndex = '';
				this.statusIndexText = '';
				this.isread = '';
				this.msgtype = '';
				this.typeIndexText = '';
			},
			apply() {
				this.isshow = false;
				var arrapply = [];
				if (this.timeIndexText) {
					arrapply.push({
						type: "time",
						text: this.timeIndexText
					});
				}
				if (this.statusIndexText) {
					arrapply.push({
						type: "isread",
						text: this.statusIndexText
					});
				}
				if (this.typeIndexText) {
					arrapply.push({
						type: "msgtype",
						text: this.typeIndexText
					});
				}
				this.arrapply = arrapply;
				var swhere = {}
				if (this.isread === 0 || this.isread === 1) {
					swhere.isread = [this.isread];
				}
				if (this.msgtype) {
					swhere.msgtype = [this.msgtype];
				}
				if (this.startdate && this.enddate) {
					swhere.time = [this.startdate, this.enddate];
				}
				this.swhere = swhere;
				this.search();
			},
			search() {
				this.loadNewsList('refresh');
			},
			delWhere(type) {
				if (type == 'time') {
					this.timeIndex = 0;
					this.timeIndexText = '';
					this.startdate = '';
					this.enddate = '';
				}
				if (type == 'isread') {
					this.isread = '';
					this.statusIndexText = '';
				}
				if (type == 'msgtype') {
					this.msgtype = 0;
					this.typeIndexText = '';
				}
				this.apply();
			},
			delSearchtxt() {
				this.searchtxt = '';
				this.search();
			},
			// 全部标为已读
			markAllAsRead() {
				var _this = this;
				uni.showModal({
					title: '提示',
					content: '是否全部设置为已读',
					success(res) {
						if (res.confirm) {
							var idArr = [];
							for (var i = 0; i < _this.objnews.List.length; i++) {
								idArr.push(_this.objnews.List[i].id);
							}
							uni.sm(function(re, err) {
								if (re) {
									_this.onPulldownRereshnews();
									uni.showToast({
										title: '已全部标为已读',
										icon: 'success'
									});
								}
							}, ["xcx.home.messageupdateread", uni.msgwhere({
								ids: uni.msgpJoin(idArr)
							}), 1], {
								route: uni.svs.zxx_home_biz
							})
						}
					}
				})
			},
			toggleSearchBox() {
				this.showSearchBox = !this.showSearchBox;
			}
		},
		computed: {
			unreadCount() {
				return this.objnews.List.filter(item => item.isread === 0).length;
			}
		}
	}
</script>

<style lang='scss'>
	/* 列表必须 */
	page,
	.content {
		height: 100%;
		overflow: hidden;
	}

	.panel-scroll-box {
		height: 100%;
	}
</style>

<style lang='scss'>
	page,
	.content {
		background-color: #fff;
		height: 100%;
		overflow: auto;
	}

	page {
		background: #ffffff;
	}

	.panel-view {
		padding: 30rpx 0 10rpx 0rpx;
	}

	.booklist {
		position: absolute;
		padding-bottom: 10rpx;
		top: 0px;
		right: 0;
		left: 0;
	}

	.bookcen {
		margin: 10px 20rpx;
	}

	.bookcen img {
		width: 100%;
	}

	.booklist:last-child {
		border-bottom: none;
		margin-bottom: 0;
	}

	.booktxt {
		font-size: 24rpx;
		color: #a7a8a9;
		position: absolute;
		bottom: 10px;
		width: 100%;
	}

	.booktxt text {
		color: #827E7B;
		margin-left: 20px;
	}

	.img_def {
		width: 198rpx;
		height: 198rpx;
		border-radius: 10rpx;
		display: block;
		margin-left: 30rpx;
	}



	.weui-cell {
		padding: 10px 0;
	}

	.weui-cell .weui-cell__bd {
		font-size: 14px;
		color: #999999;
	}

	.messagetxt {
		width: 102rpx;
		height: 40rpx;
		background: #C7C7CC;
		border-radius: 50rpx;
		text-align: center;
		margin: 10px auto;
		font-size: 24rpx;
		color: #FFFFFF;
	}

	.messagelist {
		font-size: 28rpx;
		margin: 15px 30rpx;
		padding: 0rpx;
		border-bottom: 1px solid #ebedef;
	}

	.messagelist text {
		width: 15rpx;
		height: 15rpx;
		background: #EE464F;
		margin-left: 10rpx;
		display: inline-block;
		border-radius: 25px;

	}

	.jcnf {
		width: 90rpx;
		height: 90rpx;
		margin-right: 10rpx;
	}

	.nomesssage {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 100%;
		transform: translate(-50%, -50%);
		text-align: center;
	}

	.mark-all-read {
		display: flex;
		align-items: center;
		padding: 5px;
		cursor: pointer;
	}

	/*分割线*/
	.border-rgt {
		position: relative;
		width: 33%;
	}

	.border-rgt:after {
		content: "";
		border-right: 1px solid #cdcdcd;
		height: 50%;
		position: absolute;
		right: -10px;
		top: 25%;
	}

	.border-rgt:last-child::after {
		content: none;
	}

	.border-rgt.current {
		color: #C23C43;
	}

	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.3s;
	}

	.fade-enter,
	.fade-leave-to {
		opacity: 0;
	}
</style>
