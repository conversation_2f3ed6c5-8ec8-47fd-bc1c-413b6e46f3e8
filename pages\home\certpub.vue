<template>
	<view>
		<mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" :height="mescrollHeight"
			@up="loadList" :up="{page: {size: 20}}" :bottombar="false">
			<!-- 没有数据 -->
			<view v-if="arrList.length == 0" style="text-align: center;">
				<view class="weui-flex_center" style="height: 80rpx; margin-top: 20%;">
					<image src="https://osstbfile.tb-n.com/xcxscale/static/image/shiantongjian/oerderno.png"
						style="width: 64rpx;height: 74rpx;display: block;">
					</image>
				</view>
				<text class="photo-oerder">暂无证件照片</text>
			</view>
			<view v-else v-for="(item,i) in arrList" :key="i" style="margin-bottom: 10px;">
				<view class="weui-cells weui-cells_after-title">
					<view class="weui-cell">
						<view class="weui-cell__bd newstxt">{{item.type_name}}</view>
						<view class="weui-cell__ft weui-flex" style="align-items: center;">
							<view>共<text style="margin:0 5px; color: #303030;">{{item.arrcert ? item.arrcert.length : 0}}</text>张</view>
						</view>
					</view>
				</view>
				<view style="background: #fff;">
					<view class="photoimg">
						<view class="photolist" v-for="(cert,index) in item.arrcert" :key="cert.id">
							<image :src="ossPrefix + cert.url" mode="aspectFill" style="width:100%; height: 165rpx;"></image>
						</view>
					</view>
				</view>
			</view>
		</mescroll-uni>
	</view>
</template>

<script>
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	var app = getApp();
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				ossPrefix: app.globalData.ossPrefix,
				myinfo: app.globalData.objuserinfo,
				mescrollHeight: 1200,
				arrList: []
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			var that = this;
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			loadList(page) {
				var _this = this;
				var pageNo = page.num;
				var pageSize = page.size;
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg("系统错误");
					}
					if (pageNo == 1) {
						_this.arrList = [];
					}
					var arrList = _this.arrList;
					var hasNext = true;
					if (re.length < pageSize) {
						hasNext = false;
					}
					// _this.$refs.mescrollRef.endSuccess(re.length, hasNext);
					_this.mescroll.endSuccess(re.length, hasNext);
					var arrtypeid = [];
					for (var i = 0; i < re.length; i++) {
						arrtypeid.push(re[i].id);
					}
					_this.getCert(arrtypeid, function(arrcert){
						var objarr = {};
						for (var i = 0; i < arrcert.length; i++) {
							if(!objarr[arrcert[i].type_id]){
								objarr[arrcert[i].type_id] = [];
							}
							objarr[arrcert[i].type_id].push(arrcert[i]);
						}
						for (var i = 0; i < re.length; i++) {
							re[i].arrcert = objarr[re[i].id];
						}
						arrList = arrList.concat(re);
						_this.arrList = arrList;
						_this.$forceUpdate();
						console.log('arrList', arrList);
					})
				}, ["home.certtype.list", pageSize, pageSize * (pageNo - 1)], { route: uni.svs.zxx })
			},
			getCert(arrtypeid, cb){
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg("系统错误");
					}
					cb && cb(re);
				}, ["home.cert.list", uni.msgwhere({typeid: uni.msgpJoin(arrtypeid)})], { route: uni.svs.zxx })
			},
			reloadList() {
				this.mescroll.resetUpScroll(); 
			}
		}
	};
</script>

<style>
	page {
		background: #F2F4F7;
	}
	
	.newstxt {
		font-size: 28rpx;
		color: #303030;
	}
	
	.newsrttxt {
		font-size: 24rpx;
		color: #999999;
	}
	
	.photoimg {
		background-color: #fff;
		padding: 20rpx 0rpx 20rpx 30rpx;
		overflow: hidden;
	}
	
	.photoimg view.photolist {
		width: 30%;
		margin-right: 10px;
		float: left;
		position: relative;
	
	}
	
	.weui-cells:after,
	.weui-cells:before {
		left: 15px;
		right: 15px;
	}
	
	.weui-cells:before {
		border-top: none;
	}
	
	.weui-cell__ft {
		font-size: 28rpx;
	}
	
	.photoimg view.showtxt {
		background: #FFECEF;
		border-radius: 0px 2px 0px 2px;
		font-size: 24rpx;
		color: #C23C43;
		line-height: 24px;
		position: absolute;
		left: 0;
		bottom: 6px;
		padding: 0 2px;
		text-align: center;
	}
	
	.photoimg view.showtxt.noshow {
		color: #FFFFFF;
		background: #C23C43;
	}
	
	.add-certificate {
		color: #C23C43;
		background: #FFECEF;
		border-radius: 6rpx;
		border: 1px solid #C23C43;
		width: 192rpx;
		display: block;
		text-align: center;
		font-size: 24rpx;
		margin: 10px 30rpx;
		padding: 5px 0;
	}
	
	.weui-uploader__input-box {
		width: 100%;
		height: 165rpx;
	}
</style>
