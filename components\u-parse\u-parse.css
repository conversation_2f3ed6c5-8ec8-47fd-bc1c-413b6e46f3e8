/**
 * author: <PERSON> (微信小程序开发工程师)
 * organization: WeAppDev(微信小程序开发论坛)(http://weappdev.com)
 *         垂直微信小程序开发交流社区
 *
 * github地址: https://github.com/icindy/wxParse
 *
 * for: 微信小程序富文本解析
 * detail : http://weappdev.com/t/wxparse-alpha0-1-html-markdown/184
 */

.wxParse {
  width: 100%;
  font-family: Helvetica, sans-serif;
  font-size: 30upx;
  color: #666;
  line-height: 1.8;
}

.wxParse view {
  word-break: hyphenate;
}

.wxParse .inline {
  display: inline;
  margin: 0;
  padding: 0;
}

.wxParse .div {
  margin: 0;
  padding: 0;
}

.wxParse .h1 .text {
  font-size: 2em;
  margin: 0.67em 0;
}
.wxParse .h2 .text {
  font-size: 1.5em;
  margin: 0.83em 0;
}
.wxParse .h3 .text {
  font-size: 1.17em;
  margin: 1em 0;
}
.wxParse .h4 .text {
  margin: 1.33em 0;
}
.wxParse .h5 .text {
  font-size: 0.83em;
  margin: 1.67em 0;
}
.wxParse .h6 .text {
  font-size: 0.67em;
  margin: 2.33em 0;
}

.wxParse .h1 .text,
.wxParse .h2 .text,
.wxParse .h3 .text,
.wxParse .h4 .text,
.wxParse .h5 .text,
.wxParse .h6 .text,
.wxParse .b,
.wxParse .strong {
  font-weight: bolder;
}


.wxParse .p {
  margin: 1em 0;
}

.wxParse .i,
.wxParse .cite,
.wxParse .em,
.wxParse .var,
.wxParse .address {
  font-style: italic;
}

.wxParse .pre,
.wxParse .tt,
.wxParse .code,
.wxParse .kbd,
.wxParse .samp {
  font-family: monospace;
}
.wxParse .pre {
  overflow: auto;
  background: #f5f5f5;
  padding: 16upx;
  white-space: pre;
  margin: 1em 0upx;
}
.wxParse .code {
  display: inline;
  background: #f5f5f5;
}

.wxParse .big {
  font-size: 1.17em;
}

.wxParse .small,
.wxParse .sub,
.wxParse .sup {
  font-size: 0.83em;
}

.wxParse .sub {
  vertical-align: sub;
}
.wxParse .sup {
  vertical-align: super;
}

.wxParse .s,
.wxParse .strike,
.wxParse .del {
  text-decoration: line-through;
}

.wxParse .strong,
.wxParse .s {
  display: inline;
}

.wxParse .a {
  color: deepskyblue;
}

.wxParse .video {
  text-align: center;
  margin: 22upx 0;
}

.wxParse .video-video {
  width: 100%;
}

.wxParse .img {
  display: inline-block;
  width: 0;
  height: 0;
  max-width: 100%;
  overflow: hidden;
}

.wxParse .blockquote {
  margin: 10upx 0;
  padding: 22upx 0 22upx 22upx;
  font-family: Courier, Calibri, "宋体";
  background: #f5f5f5;
  border-left: 6upx solid #dbdbdb;
}
.wxParse .blockquote .p {
  margin: 0;
}

.wxParse .ul, .wxParse .ol {
  display: block;
  margin: 1em 0;
  padding-left: 33upx;
}
.wxParse .ol {
  list-style-type: disc;
}
.wxParse .ol {
  list-style-type: decimal;
}
.wxParse .ol>weixin-parse-template,.wxParse .ul>weixin-parse-template {
  display: list-item;
  align-items: baseline;
  text-align: match-parent;
}

.wxParse .ol>.li,.wxParse .ul>.li {
  display: list-item;
  align-items: baseline;
  text-align: match-parent;
}
.wxParse .ul .ul, .wxParse .ol .ul {
  list-style-type: circle;
}
.wxParse .ol .ol .ul, .wxParse .ol .ul .ul, .wxParse .ul .ol .ul, .wxParse .ul .ul .ul {
    list-style-type: square;
}

.wxParse .u {
  text-decoration: underline;
}
.wxParse .hide {
  display: none;
}
.wxParse .del {
  display: inline;
}
.wxParse .figure {
  overflow: hidden;
}

.wxParse .table {
  width: 100%;
}
.wxParse .thead, .wxParse .tfoot, .wxParse .tr {
  display: flex;
  flex-direction: row;
}
.wxParse .tr {
  width:100%;
  display: flex;
  border-right: 2upx solid #e0e0e0;
  border-bottom: 2upx solid #e0e0e0;
}
.wxParse .th,
.wxParse .td {
  display: flex;
  width: 1276upx;
  overflow: auto;
  flex: 1;
  padding: 11upx;
  border-left: 2upx solid #e0e0e0;
}
.wxParse .td:last {
  border-top: 2upx solid #e0e0e0;
}
.wxParse .th {
  background: #f0f0f0;
  border-top: 2upx solid #e0e0e0;
}
