<template>
	<view class="map-box">
		<map id="qqmap" ref="map1" class="tui-maps" :longitude="longitude" :latitude="latitude" :scale="17" show-location @regionchange="regionchange" :style="{'width':windowWidth + 'px','height':'420px'}">
			<view class="view-search" v-if="showCitySearch">
				<view class="search-view">
					<text @click="openPopup()">{{selectorCityName || "城市"}}</text>
					<view class="sep-line"></view>
					<input class="address-info-input" v-model="keyword" placeholder="请输入地址" />
					<text class="search-text" @click="searchLocAddress">搜索</text>
				</view>
				<scroll-view class="list-view" scroll-y v-if="searchCityResultList != null && searchCityResultList.length>0">
					<view class="search-address" v-for="(item,index) in searchCityResultList" :key="index" @click="didTapToKeywordSearchLoction(index)">
						<text>{{item.ad_info.province + item.ad_info.city + item.ad_info.district + item.title}}</text>
					</view>
				</scroll-view>
			</view>
			<image class="image" :src="locPath" />
			<view class="view-loc">
				<image class="reset-icon" :src="resetBtnPath" @click="resetLocation"></image>
			</view>
		</map>

		<view v-if="showPopup" class="hl-popup" ref="popup">
			<view class="shade-bg-view"></view>
			<HLSelectorCity class="city-selector" @citySelector="selectorSelector"></HLSelectorCity>
		</view>
	</view>
</template>

<script>
	const QQMapWX = require('./config/qqmap-wx-jssdk.min.js')
	import HLSelectorCity from './hl-selector-city.vue'
	export default {
		components: {
			HLSelectorCity
		},

		props: {
			locPath: {
				type: String
			},

			resetBtnPath: {
				type: String
			},

			mapKey: {
				type: String,
				default: 'EEDBZ-2WLWW-6TBRY-O4JJM-RDA7T-KMB3Q'
			},

			showCitySearch: {
				type: Boolean,
				default: true,
			}

		},
		data() {
			return {
				windowWidth: null,
				longitude: null,
				latitude: null,
				mapContext: null,
				loc_addressInfo: null,
				isInitialized: true,
				qqmapsdk: null,

				showPopup: false,

				selectorCityName: null, //选择城市
				keyword: '', //输入框关键字搜索
				searchCityResultList: [] //搜索的地址列表

			}
		},

		onReady() {
			this.mapContext = uni.createMapContext('qqmap', this)
		},

		//第一次初始化用户位置信息
		mounted() {
			this.mapContext = uni.createMapContext('qqmap', this)
			try {
				this.qqmapsdk = new QQMapWX({
					key: this.mapKey
				})

				var that = this;

				const res = uni.getSystemInfoSync();
				this.windowWidth = res.windowWidth;
				uni.showLoading({
					title: '正在获取定位中',
				});
				uni.getLocation({
					type: 'gcj02',
					isHighAccuracy: true,
					success: (res) => {
						uni.hideLoading()
						that.longitude = res.longitude
						that.latitude = res.latitude
						that.getAddressInfo(that.latitude, that.longitude)
					},
					fail() {
						console.log('进入回调:fail');
					},
					complete() {
						console.log('进入回调:complete');
					}
				})
			} catch (e) {
				// error
			}
		},
		methods: {
			//移动地图获取地址
			regionchange(e) {
				if (e.type == "end" && this.isInitialized == true) {
					this.isInitialized = false
					let longitude = e.detail.centerLocation.longitude
					let latitude = e.detail.centerLocation.latitude
					this.getAddressInfo(latitude, longitude)
				}
			},
			//获取附近位置信息
			async getAddressInfo(longitude, latitude) {
				let location = [longitude, latitude]
				let stringLocation = location.toString()
				var that = this;
				this.qqmapsdk.reverseGeocoder({
					location: stringLocation,
					get_poi: 1,
					success: res => {
						if (res.result.pois.length > 0) {
							if (that.selectorCityName == null) {
								that.selectorCityName = res.result.ad_info.city
							}
							that.loc_addressInfo = res.result.pois[0]
							that.$emit('addressUpdate', {
								'result': that.loc_addressInfo
							})
						}
						that.isInitialized = true
					},
					fail: err => {
						console.log(err.toString())

						uni.showToast({
							title: err.toString(),
							icon: 'none',
							duration: 3000
						})
					}
				})
			},

			resetLocation: function() {
				let that = this
				this.mapContext.moveToLocation({
					longitude: this.longitude,
					latitude: this.latitude,
					complete: res => {
						setTimeout(() => {
							that.getAddressInfo(that.latitude, that.longitude)
						}, 500)
					}
				})
			},

			searchLocAddress() {
				let that = this
				// 调用接口
				this.qqmapsdk.search({
					region: this.selectorCityName,
					keyword: this.keyword, //搜索关键词
					success(res) { //搜索成功后的回调
						if (res.data.length > 0) {
							that.searchCityResultList = res.data
						} else {
							uni.showToast({
								title: "查询地址失败",
								icon: 'error'
							})
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "查询地址失败",
							icon: 'error'
						})
					},
				});
			},

			didTapToKeywordSearchLoction: function(index) {
				let model = this.searchCityResultList[index]
				this.keyword = model.ad_info.province + model.ad_info.city + model.ad_info.district + model.title
				let that = this
				this.mapContext.moveToLocation({
					longitude: model.location.lng,
					latitude: model.location.lat,
					complete: res => {
						setTimeout(() => {
							that.searchCityResultList = []
							that.getAddressInfo(model.location.lat, model.location.lng)
						}, 500)
					}
				})
			},

			selectorSelector: function(e) {
				if (e.result == true) {
					this.selectorCityName = e.city
				}
				this.closedPopup()
			},

			openPopup: function() {
				this.showPopup = true
			},

			closedPopup: function() {
				this.showPopup = false
			},
		}
	}
</script>

<style lang="scss" scoped>
	$uni-color-theme : #007aff !default .map-box {
		display: flex;
		position: relative;
		width: 100%;

		.image {
			width: 40rpx;
			height: 68rpx;
			position: absolute;
			top: 50%;
			left: 50%;
		}

		.view-search {
			display: flex;
			position: relative;
			flex-direction: column;
			align-items: flex-end;
			justify-content: center;
			position: absolute;
			top: 94px;
			left: 20px;
			right: 20px;

			.search-view {
				display: flex;
				flex-direction: row;
				align-items: center;
				box-sizing: border-box;
				padding: 0 10px;
				border-radius: 6px;
				width: 100%;
				height: 38px;
				background-color: #FFF;
				font-size: 14px;
				color: #000;

				.sep-line {
					background-color: #000;
					width: 1px;
					height: 16px;
					margin-left: 10px;
				}

				.address-info-input {
					color: #000;
					margin-left: 10px;
					font-size: 14px;
					flex: 1;
				}

				.search-text {
					color: $uni-color-theme;
				}
			}

			.list-view {
				width: 100%;
				margin-top: 10px;
				max-height: 250px;
				background-color: #FFF;
				border-radius: 6px;
				z-index: 9999;

				.search-address {
					padding: 0 10px;
					margin: 10px 0;
					font-size: 14px;
				}
			}
		}

		.view-loc {
			display: flex;
			position: relative;
			flex-direction: column;
			align-items: flex-end;
			justify-content: center;
			position: absolute;
			bottom: 20px;
			left: 20px;
			right: 20px;

			.reset-icon {
				width: 50px;
				height: 50px;
				border-radius: 25px;
				margin-bottom: 12px;
			}
		}
	}

	.hl-popup {
		display: flex;
		position: fixed;
		z-index: 999;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background-color: transparent;

		.shade-bg-view {
			position: fixed;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			background-color: #000;
			opacity: 0.25;
		}

		.city-selector {
			position: fixed;
			right: 0;
			bottom: 0;
			left: 0;
			width: 100%;
		}
	}
</style>