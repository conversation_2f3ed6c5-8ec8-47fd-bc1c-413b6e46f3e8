<template>
	<view>
		<!-- pages/search.wxml -->
		<view class="def-search toplinebg">
			<view class="weui-search-bar weui-search-bar_focusing" id="searchBar">
				<view class="weui-search-bar__form">
					<view class="weui-search-bar__box weui-flex" style="align-items: center; padding: 0 30rpx">
						<image src="https://osstbfile.tb-n.com/xcxscale/static/image/icon_search.png"
							style="width: 30rpx; height: 30rpx; margin-right: 25rpx"></image>
						<input @input="getyeyname" @confirm="search" placeholder-class="phcolor" class="weui-input weui-flex__item" placeholder="请输入学校名称"
							style="font-size: 14px" />
						<image src="https://osstbfile.tb-n.com/xcxscale/static/image/icon_close.png"
							style="width: 30rpx; height: 30rpx"></image>
					</view>
				</view>
				<text @click="search" style="margin-left: 5px; " class="font14">搜索</text>
			</view>
		</view>
		<view v-if="arryey.length > 0" class="weui-cells noafter nobefore" style="margin-top:10px;">
			<block v-for="(item,index) in arryey" :key="index">
				<view class="weui-cell" @click="chooseYey(item.id, item.yeyname, item.guid)">
					<view class="weui-cell__bd">
						<view>{{item.yeyname}}</view>
						<view class="font12 adreesstxt">{{item.address}}</view>
					</view>
				</view>
			</block>
		</view>

		
			<!--  暂无数据-->
	<view  class="noinfo-view weui-flex_center"  v-else>
							<image src="/static/image/home/<USER>"
								style="width: 277rpx; height:199rpx; margin-top: 200rpx"></image>
							<view style="margin-top: 30rpx">暂无数据</view>
				
						</view>
		
		
		
	</view>
</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js")
	var app = getApp();
	var pagesize = 10;
	
	export default {
		data() {
			return {
				yeyname: '',
				arryey: []
			}
		},
		onLoad: function(params) {
			
		},
		onShow() {
		
		},
		methods: {
			getyeyname(e){
				this.yeyname = e.detail.value;
			},
			search(){
				var _this = this;
				if(!this.yeyname){
					return uni.msg('请输入要搜索的学校名称');
				}
				uni.sm(function(re, err){
					if(err){
						return uni.msg(err);
					}
					console.log('re:', re);
					_this.arryey = re;
				}, ["yey.selectyey", this.yeyname], {route: uni.svs.zxx_home_biz})
			},
			chooseYey(yeyid, yeyname, guid){
				uni.showModal({
					title: "提示",
					content: "确定关联《" + yeyname + "》吗？",
					showCancel: true,
					confirmText: "确定",
					success(res) {
						console.log('res:', res);
						if (res.confirm) {
							uni.navigateTo({
								url: "/pages/mine/student_add?yeyid=" + yeyid + "&yeyname=" + yeyname + "&guid=" + guid
							})
						}
					},
					fail(err) {
						console.log(err)
					}
				})
			}
			
		}
	}
</script>

<style>
	page {
		background: #fff;
	}

	.weui-cell {
		padding:13px 15px;
		font-size: 14px;
		color: #303030;
	}

	.weui-cell:before {
		right: 15px;
	}
	.weui-cell .adreesstxt{color: #999999; margin-top: 5px;}
	.weui-cells:after, .weui-cells:before{    border-top: none;}
	.weui-cells:after{border-bottom:none;}
</style>