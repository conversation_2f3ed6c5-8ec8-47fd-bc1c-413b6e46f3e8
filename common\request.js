// let baseUrl = "https://captcha.anji-plus.com/captcha-api"
const app = getApp();
let baseUrl = app.globalData.requrl + "/" + app.globalData.projectname; //"http://localhost:8080"

export const myRequest = (option = {}) => {
	return new Promise((reslove, reject) => {
		uni.request({
			// url: baseUrl + option.url,
			// #ifdef H5
			url:  uni.svs.zxx_home_auth + option.url,
			// #endif
			// #ifndef H5
			url: app.globalData.geturl(uni.svs.zxx_home_auth, option.url),
			// #endif				
			data: option.data,
			method: option.method || "GET",
			success: (result) => {
				reslove(result)
			},
			fail: (error) => {
				reject(error)
			}
		})
	})
}