<template>
	<view>
		<view>
			<view class="module-top">
				<view class="weui-cell weui-cell_access index-top-tit">
					<view class="weui-cell__hd" style="position: relative; margin-right: 10px;" id="btnchangebaby">
						<image src="/static/image/bzn/img_default.png"
							style="width:110rpx;height: 110rpx;  border-radius: 50%;">

					</view>
					<view class="weui-cell__bd">
						<view style="font-size: 15px;" id="babyname">{{stu.stuname}}</view>
						<view style="font-size: 12px;" id="babyage">{{stu.age}}</view>
					</view>

					<view class="weui-cell__ft">
						<view class="askleave-num weui-flex_center">

							<view>累计请假 {{leaveNum}} 天</view>
						</view>
					</view>

					<view>




					</view>
				</view>

				<view class="weui-flex " style="align-items: center;justify-content: center; background: #fff;">
					<view class="tab-common10 weui-flex" style="width: 100%;">
						<view :class="curType == 0 ? 'current' : ''" @click="tabClick(0)" style="flex: 1;"> 请假单</view>
						<view :class="curType == 1 ? 'current' : ''" @click="tabClick(1)" style="flex: 1;">请假历史</view>


					</view>
				</view>

			</view>

			<view v-if="curType == 0" class="remarks-main">
				<view class="remarksview">
					<view class="weui-cells askle-opr">
						<view class="weui-flex">
							<view class="askle-time">
								<view class="weui-cell">
									<view class="weui-cell__bd">
										<view style="color: #333;">开始日期<i class="red">*</i></view>
									</view>
									<view class="weui-cell__pft">
										<!-- 2024-09-13<text style="visibility: visible;">（明天）</text> -->

										<picker mode="date" :value="startDate" @change="bindDateChange">
											<view class="uni-input">{{ startDate }}</view>
										</picker>

										<picker @change="bindPickerChange" :value="index" :range="array">
											<view class="uni-input">{{ array[index] }}</view>
										</picker>
									</view>

								</view>
								<view class="weui-cell">
									<view class="weui-cell__bd">
										<view style="color: #333;">结束日期<i class="red">*</i></view>
									</view>
									<view class="weui-cell__pft">
										<!-- 2024-09-13<text style="visibility: visible;">（明天）</text> -->
										<picker mode="date" :value="endDate" @change="bindDateChange2">
											<view class="uni-input">{{ endDate }}</view>
										</picker>
										<picker @change="bindPickerChange2" :value="index" :range="array">
											<view class="uni-input">{{ array[index2] }}</view>
										</picker>
									</view>
								</view>
							</view>
							<view class="stat-sum">
								共<view style="color: #C23C43;">{{ day }}</view>
								天
							</view>
						</view>
						<view class="weui-cell weui-cell_access">
							<view class="weui-cell__bd">
								<view>请假事由<i class="red">*</i></view>
							</view>
							<view class="weui-cell__hd  weui-clr weui-cells_checkbox">

								<radio-group @change="qjtypeChange">
									<view class="weui-flex">
										<label class="radio_style weui-flex_center">
											<radio checked="check" value="2" style="top: -8px;"></radio>
										</label>病假
									</view>
									<view class="weui-flex" style="margin-left: 15px;"><label
											class="radio_style weui-flex_center">
											<radio value="3" style="top: -8px;"></radio>
										</label>事假
									</view>
								</radio-group>
							</view>
						</view>
						<view v-if="leavetype == 2" class="weui-cell weui-cell_access">
							<view class="weui-cell__bd">
								<view>发病日期</view>
							</view>
							<view class="weui-cell__hd  weui-clr ">
								<view class="weui-flex">
									<input type="text" id="txtfbdate" placeholder="请选择" autocomplete="off"
										class="weui-flex__item"
										style="border: none; font-size: 12px; text-align: right;" readonly="readonly" />
									<view class="iconfont icon_arrow-right" id="iconfbdate"
										style="height: 20px; line-height: 20px;"></view>
								</view>
							</view>
						</view>
						<view class="weui-cell weui-cell_access" href="javascript:;">
							<view class="weui-cell__bd">
								<view>请假期间是否在本地</view>
							</view>
							<view class="weui-cell__hd  weui-clr weui-cells_checkbox">

								<view class="weui-flex">
									<label class="radio_style weui-flex_center">
										<radio checked="check" style="top: -8px;"></radio>
									</label>是
								</view>
								<view class="weui-flex" style="margin-left: 15px;"><label
										class="radio_style weui-flex_center">
										<radio style="top: -8px;"></radio>
									</label>否</view>



							</view>
						</view>
						<view class="weui-cell weui-cell_access illreason">
							<view class="weui-cell__bd">
								<view>{{leavetype==2?'病假症状':'事假原因'}}<i class="red">*</i></view>
							</view>
						</view>
						<view class="complement illreason" style="border-bottom: none;">
							<block v-if="leavetype == 2">
								<text :class="hasillreason('感冒')?'current':''" @click="selillreason('感冒')">感冒</text>
								<text :class="hasillreason('咳嗽')?'current':''" @click="selillreason('咳嗽')">咳嗽</text>
								<text :class="hasillreason('发热')?'current':''" @click="selillreason('发热')">发热</text>
								<text :class="hasillreason('腹痛')?'current':''" @click="selillreason('腹痛')">腹痛</text>
								<text :class="hasillreason('皮疹')?'current':''" @click="selillreason('皮疹')">皮疹</text>
								<text :class="hasillreason('腹泻')?'current':''" @click="selillreason('腹泻')">腹泻</text>
								<text :class="hasillreason('黄疸')?'current':''" @click="selillreason('黄疸')">黄疸</text>
								<text :class="hasillreason('结膜红肿')?'current':''" @click="selillreason('腹痛')">结膜红肿</text>
								<text :class="hasillreason('呕吐')?'current':''">呕吐</text>
								<text :class="hasillreason('牙疼')?'current':''">牙疼</text>
								<text :class="hasillreason('外伤')?'current':''">外伤</text>
								<text :class="hasillreason('咽痛')?'current':''">咽痛</text>
								<text :class="hasillreason('鼻塞')?'current':''">鼻塞</text>
								<text :class="hasillreason('流涕')?'current':''">流涕</text>
								<text :class="hasillreason('呼吸道感染')?'current':''">呼吸道感染</text>
								<text :class="hasillreason('肺炎')?'current':''">肺炎</text>
								<text :class="hasillreason('其他')?'current':''">其他</text>
							</block>
							<block v-else>
								<text :class="hasillreason('休息')?'current':''">休息</text>
								<text :class="hasillreason('辅导班')?'current':''">辅导班</text>
								<text :class="hasillreason('寒暑假')?'current':''">寒暑假</text>
								<text :class="hasillreason('旅游')?'current':''">旅游</text>
								<text :class="hasillreason('境外')?'current':''">境外</text>
								<text :class="hasillreason('其他')?'current':''">其他</text>
							</block>
						</view>
						<view class="weui-cell weui-cell_access">
							<view class="weui-cell__bd">
								<view><i class="red">*</i>是否发热</view>
							</view>
							<view class="weui-cell__hd  weui-clr weui-cells_checkbox">
								<view class="weui-flex">
									<label class="radio_style weui-flex_center">
										<radio checked="check" style="top: -8px;"></radio>
									</label>是
								</view>
								<view class="weui-flex" style="margin-left: 15px;"><label
										class="radio_style weui-flex_center">
										<radio style="top: -8px;"></radio>
									</label>否</view>

								<input class="degrees_txt" id="tiwen" type="text"
									style="margin-left: 15px; width: 50px;border-bottom: 1px solid #C5C5C5;">℃
							</view>
						</view>
						<view class="weui-cell weui-cell_access" href="javascript:;" id="viewjiuzhen">
							<view class="weui-cell__bd">
								<view>是否就诊<i class="red">*</i></view>
							</view>
							<view class="weui-cell__hd  weui-clr weui-cells_checkbox">
								<view class="weui-flex">
									<label class="radio_style weui-flex_center">
										<radio checked="check" style="top: -8px;"></radio>
									</label>是
								</view>
								<view class="weui-flex" style="margin-left: 15px;"><label
										class="radio_style weui-flex_center">
										<radio style="top: -8px;"></radio>
									</label>否</view>

								<text style="color: blue; margin-left: 15px; display: none" id="spjiuzhen">填写就诊情况</text>
							</view>
						</view>
						<view class="weui-cell weui-cell_access">
							<view class="weui-cell__bd">请假描述（0/50） </view>

						</view>
						<view class="inputbg">

							<textarea class="uni-input" style="width: 100%; " placeholder-style="color:#a7a8a9;"
								placeholder="请输入请假详情" /> </textarea>
						</view>



					</view>



				</view>

			</view>
		</view>
		<!-- 提交按钮 -->
		<view v-if="curType == 0" style="height: 160rpx;box-shadow: none;border-top:none;">
			<button class="common-btn   noafter">提交请假单</button>
		</view>


		<!-- 请假历史模块 -->
		<view v-if="curType == 1">
			<view class="date-div">
				<image src="/pages/home/<USER>/image/arrow-left.png" style="width: 33rpx; height: 33rpx;"></image>
				<text id="spyearmonth">----年--月</text>
				<text id="sptotal" class="redtit">该月份共请假-天</text>
				<image src="/pages/home/<USER>/image/arrow-right.png" style="width: 33rpx; height: 33rpx;" />
			</view>
			<view class="total-tab">
				<uni-table>
					<!-- 表头行 -->
					<uni-tr>
						<uni-th align="center">请假类别</uni-th>
						<uni-th align="center" class="redtit">总天数</uni-th>
						<uni-th align="center">爸爸请的</uni-th>
						<uni-th align="center">妈妈请的</uni-th>
						<uni-th align="center">其他</uni-th>
					</uni-tr>
					<!-- 表格数据行 -->
					<uni-tr>
						<uni-td align="center" class="redtit">全部</uni-td>
						<uni-td align="center" class="redtit">0天</uni-td>
						<uni-td align="center" class="redtit">0天</uni-td>
						<uni-td align="center" class="redtit">0天</uni-td>
						<uni-td align="center" class="redtit">0天</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td align="center">病假</uni-td>
						<uni-td align="center" class="redtit">0天</uni-td>
						<uni-td align="center">0天</uni-td>
						<uni-td align="center">0天</uni-td>
						<uni-td align="center">0天</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td align="center">事假</uni-td>
						<uni-td align="center" class="redtit">0天</uni-td>
						<uni-td align="center">0天</uni-td>
						<uni-td align="center">0天</uni-td>
						<uni-td align="center">0天</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td align="center">其他</uni-td>
						<uni-td align="center" class="redtit">0天</uni-td>
						<uni-td align="center">0天</uni-td>
						<uni-td align="center">0天</uni-td>
						<uni-td align="center">0天</uni-td>
					</uni-tr>

				</uni-table>

			</view>
			<view class="record-content">
				<view style="text-align: center">暂无请假历史</view>

			</view>


		</view>


	</view>
</template>

<script>
var app = getApp();
import uploadUtil from '@/common/uploadUtil.js';
var moment = require("../../common/moment.min.js");
var reasonArr=[];
export default {
	data() {
		return {
			 stu:{},
			 curType: 0,
			 startDate: moment().format('YYYY-MM-DD'),
			 endDate: moment().format('YYYY-MM-DD'),
			 array: ['上午', '下午'],
			 index: 0,
			 index2: 1,
			 day:1,
			 leaveNum:0,
			 leavetype:2,
			 hasillreason:function (str) {
				 return reasonArr.indexOf(str) > -1;
			 }
		}
	},
	onLoad(params) { 
		console.log('params', params);
		this.initData();
	},
	
	 

	methods: {
		initData() {
			var _this = this;
			_this.stu=app.globalData.objuserinfo;
			//
		},
		// 切换
		tabClick(type) {
			this.curType = type;

		},
		bindDateChange: function(e) {
            this.startDate = e.detail.value;
			this.computerDay([this.startDate, this.array[this.index]], [this.endDate, this.array[this.index2]]);
        },
		
		bindDateChange2: function(e) {
            this.endDate = e.detail.value;
			this.computerDay([this.startDate, this.array[this.index]], [this.endDate, this.array[this.index2]]);
        },
		bindPickerChange: function(e) {
            console.log('picker发送选择改变，携带值为', e.detail.value)
            this.index = e.detail.value;
			this.computerDay([this.startDate, this.array[this.index]], [this.endDate, this.array[this.index2]]);
        },
		bindPickerChange2: function(e) {
            console.log('picker发送选择改变，携带值为', e.detail.value)
            this.index2 = e.detail.value;
			this.computerDay([this.startDate, this.array[this.index]], [this.endDate, this.array[this.index2]]);
        },

		/*得到时间差*/
		computerDay: function (arrstart, arrend) {
			var startdate = new Date(arrstart[0].replace(/-/g, "/"));
			var enddate = new Date(arrend[0].replace(/-/g, "/"));
			var day = (enddate.getTime() - startdate.getTime()) / (24 * 60 * 60 * 1000);
			var startnoon = arrstart[1];
			var endnoon = arrend[1];
			if (startnoon == "上午" && endnoon == "下午") {//起始位上午结束位下午与原计算值相同不需处理
				 day=day + 1;
			} else if (startnoon == "下午" && endnoon == "上午") {//起始位下午结束位上午应该按整天一整天处理不是按照两天处理，在原计算的基础上减去1才是真正请假时间
				 day=day;
			} else if (startnoon == "上午" && endnoon == "上午" || startnoon == "下午" && endnoon == "下午") {//同为上午或下午时应该按0.5天算，在原计算的基础上减去0.5才是真正请假时间
				 day=day + 0.5;
			}

			this.day = day;
		},

		qjtypeChange: function (e) {
			console.log('radio发生change事件，携带value值为:', e.detail.value);
			this.leavetype = e.detail.value;
		},

		selillreason: function (str) {
			if (this.hasillreason(str)) {
				reasonArr.splice(reasonArr.indexOf(str), 1);
			} else {
				reasonArr.push(str);
			}
			//强制刷新
			this.$forceUpdate();
		},
		saveData: function () {
			var _this = this;
			var intreason = 0;
			for (var i = 0; i < reasonArr.length; i++) {
				if (reasonArr[i] == '感冒') {
					intreason += 2;
				} else if (reasonArr[i] == '咳嗽') {
					intreason += 4;
				} else if (reasonArr[i] == '发热') {
					intreason += 8;
				} else if (reasonArr[i] == '腹痛') {
					intreason += 16;
				} else if (reasonArr[i] == '皮疹') {
					intreason += 32;
				} else if (reasonArr[i] == '腹泻') {
					intreason += 64;
				} else if (reasonArr[i] == '黄疸') {
					intreason += 128;
				} else if (reasonArr[i] == '结膜红肿') {
					intreason += 256;
				} else if (reasonArr[i] == '呕吐') {
					intreason += 512;
				} else if (reasonArr[i] == '牙疼') {
					intreason += 1024;
				} else if (reasonArr[i] == '外伤') {
					intreason += 2048;
				} else if (reasonArr[i] == '咽痛') {
					intreason += 4096;
				} else if (reasonArr[i] == '鼻塞') {
					intreason += 8192;
				} else if (reasonArr[i] == '流涕') {
					intreason += 16384;
				} else if (reasonArr[i] == '呼吸道感染') {
					intreason += 32768;
				} else if (reasonArr[i] == '肺炎') {
					intreason += 65536;
				} else if (reasonArr[i] == '其他') {
					intreason += 1;
				} else if (reasonArr[i] == '休息') {
					intreason += 2;
				} else if (reasonArr[i] == '辅导班') {
					intreason += 4;
				} else if (reasonArr[i] == '寒暑假') {
					intreason += 8;
				} else if (reasonArr[i] == '旅游') {
					intreason += 16;
				} else if (reasonArr[i] == '境外') {
					intreason += 32;
				} else if (reasonArr[i] == '其他') {
					intreason += 1;
				}
			}


		},


		chooseAndUploadImage() {
			var _this = this;
			uni.chooseImage({
				count: 9, // 最多选择9张图片
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					console.log('res', res);
					uni.showLoading();
					uploadUtil.uploadServer({
						arrfile: res.tempFiles,
						// #ifdef H5
						serverurl: "/" + uni.svs.zxx + '/ueditorController?Authorization=Bearer ' + uni.getStorageSync('token') + '&action=uploadfile&upRePath=unieye/users/',// 文件接收服务端

						// #endif
						// #ifndef H5
						serverurl: app.globalData.geturl(uni.svs.zxx, "ueditorController") + '?Authorization=Bearer ' + uni.getStorageSync('token') + '&action=uploadfile&upRePath=unieye/users/',// 文件接收服务端
						// #endif
						complete: function (arrpath) {
							console.log('arrpath', arrpath);
							var idcardphoto = arrpath[0].key;
							//保存证件照片
							// uni.sm(function (re, err) {
							// 	uni.hideLoading();
							// 	if (err) {
							// 		return uni.msg("上传失败，请重试");
							// 	}
							// 	uni.msg("上传成功");
							// 	_this.objdata.idcard_photo = idcardphoto;
							// 	if (_this.objdata.userimg && _this.objdata.nickname && _this.objdata.mobile && _this.objdata.truename && _this.objdata.idcard && _this.objdata.idcard_photo) {
							// 		_this.doComplete("iscomplete", "1");
							// 	}
							// }, ["unieye.user.editoneitembyid", uni.msgwhere({ column: ['idcard_photo'] }), uni.msgwhere({ val: [idcardphoto] })])


						}
					})
				}
			});
		},


	}
}
</script>

<style>
	.module-top {
		background: #fff;
	}

	.index-top-tit {
		height: 80px;
		padding: 6px 0px 10px 15px !important;
		color: #fff !important;
		background: #C23C43;
		border-bottom-left-radius: 200px 20px;
		border-bottom-right-radius: 200px 20px;
	}

	.askleave-num {
		height: 40px;
		color: #C23C43;
		background: #ffffff;
		line-height: 17px;
		padding: 0 15px 0 20px;
		border-radius: 30px 0 0 30px;
	}

	.remarks-main .weui-cells_checkbox {
		margin: 5px 0 0 0;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
	}

	.remarks-main .weui-cells_checkbox>label {
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		align-items: center;
		margin-left: 5px;
	}

	.complement {
		padding-bottom: 10px;
		margin: 4px 10px;
	}

	.complement text {
		font-size: 15px;
		padding: 3px 8px;
		text-align: center;
		height: 20px;
		position: relative;
		display: inline-block;
		margin: 2px;
	}

	.complement text:after {
		content: "";
		height: 80%;
		border-right: 1px solid #ddd;
		position: absolute;
		right: 0;
	}

	.complement text:last-child:after {
		border: 0 none;
	}

	.complement-remind {
		text-align: center;
		margin: 30px auto;
	}

	.complement .current {
		background: #FF622C;
		color: #fff;
		border-radius: 5px;
		border-right: none;
	}

	.remarks-main {
		width: 100%;
		margin: 0 auto;
		overflow: auto;
		font-size: 15px;

	}

	.remarks-area {
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		margin: 15px 0px 10px 10px;
		padding-bottom: 15px
	}

	.remarks-main .remarks-area textarea {
		border: 1px solid #F4F4F4;
		width: 100%;
		height: 50px;
		vertical-align: top;
		outline: none;
		padding: 10px;
		border-radius: 3px;
		margin-left: 10px;
		margin-right: 10px;
	}

	.remarksdiv .weui-cells {
		margin: 10px;
		font-size: 15px;
	}

	.remarksdiv {
		background: #fff;
		padding: 0px 5px
	}

	.remarksdiv .weui-cell {
		padding: 10px 0px;
	}

	.remarks-main .weui-cells_checkbox .weui-check:checked+.weui-icon-checked::before {
		color: #ff5a5a;
	}



	.askle-time {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
	}

	.stat-sum {
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		height: 64px;
		padding: 4px 0;
		text-align: center;
		border: 1px solid #C23C43;
		width: 32px;
		border-radius: 5px;
		margin: 4px 10px 0px 10px;
		font-size: 14px
	}

	.askle-time input {
		width: 120px;
		border: none;
	}

	.askle-opr .weui-cell .weui-cell__bd {
		-webkit-box-flex: inherit;
		-webkit-flex: inherit;
		flex: inherit;
	}

	.askle-opr .weui-cell .weui-cell__hd,
	.askle-opr .weui-cell .weui-cell__pft {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		margin-left: 15px;
	}

	.askle-opr .weui-cell .weui-cells_checkbox {
		margin: 0 0 0 5px;
	}

	.remarks-main .weui-cells {
		margin-top: 10px;
		font-size: 14px;
		padding-bottom: 10px;
	}

	.red {
		color: #F45357;
	}

	.weui-cells:before {
		border-top: none;
	}

	.weui-cells:after {
		border-bottom: none
	}

	.weui-cell:before {
		right: 15px;
	}

	.inputbg {
		text-align: left;
		background: #F8F8F8;
		border-radius: 3px;
		padding: 5px 10px;
		font-size: 24rpx;
		color: #303030;
		line-height: 60rpx;
		height: 140rpx;
		margin: 0 15px 0px 15px;


	}

	.uni-input {
		text-align: left;
		font-size: 24rpx;
		color: #999999;
		line-height: 60rpx;
		height: 60rpx;
	}

	.date-div {
		height: 45px;
		background: #FFEDEF;
		display: flex;
		display: -webkit-flex;
		align-items: center;
		justify-content: space-around;
		font-size: 14px;
		color: #303030;
	}

	.date-div .iconfont {
		font-size: 14px;
		width: 30px;
		height: 100%;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.total-tab {
		background: #fff;

	}

	.table-border {
		border-left: none;
	}

	/deep/ .uni-table {
		min-width: auto !important;
	}

	.uni-table-th {
		color: #303030;
		font-weight: normal;
	}

	.redtit {
		color: #C23C43;
	}

	.record-content {
		background: #fff;
		margin-top: 10px;
		padding-top: 15px;
		padding-bottom: 15px;
		font-size: 32rpx;
		color: #303030;
	}
</style>