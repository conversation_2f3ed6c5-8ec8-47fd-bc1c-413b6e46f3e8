## wu-ui-tools 工具集

> **组件名：wu-ui-tools**

wu-ui 工具集成，包括网络Http请求、便捷工具、节流防抖、对象操作、时间格式化、路由跳转、全局唯一标识符、规则校验等等。

需要在自己的项目中使用请参考[扩展配置](https://wuui.cn/zh-CN/components/extendedConfiguration.html)。

## <a href="https://wuui.cn/js/intro.html" target="_blank">查看文档</a>

## [完整示例项目下载 | 关注更多组件](https://ext.dcloud.net.cn/plugin?name=wu--ui)
(请勿下载插件zip)

<a href="https://ext.dcloud.net.cn/plugin?name=wu--ui">
	<img src="https://wuui.cn/intr.png">
</a>

**如使用过程中有任何问题，或者您对wu-ui有一些好的建议，欢迎加入 [wu-ui 交流群](https://wuui.cn/zh-CN/components/qqFeedBack.html)**
