{"id": "smh-countDown", "displayName": "smh-countDown 倒计时 动画", "version": "1.0.10", "description": "基于canvas的圆形倒计时动画，可自定义大小，颜色，线条宽度等", "keywords": ["smh-countDown", "canvas", "倒计时", "倒计时动画"], "repository": "", "engines": {"HBuilderX": "^3.6.2"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "", "type": "component-vue"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "n", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "n", "百度": "y", "字节跳动": "y", "QQ": "y"}, "快应用": {"华为": "u", "联盟": "u"}}}}}