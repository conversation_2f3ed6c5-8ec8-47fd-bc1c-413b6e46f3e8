<template>

	<view style="height:100%;overflow:hidden;">
		<z-paging-swiper>
			<view class="weui-flex topviewnebg" style="aviewgn-items: center;justify-content: center; background: #fff;">
				<view class="tab-common10 weui-flex" style="width: 100%;">
					<!-- <view class="current" style="flex: 1;">待确认</view>
					<view style="flex: 1;">已确认</view> -->
					<z-tabs ref="tabs" 
						:tabsStyle="tabsStyle" 
						:activeColor="activeColor" 
						barHeight="4" 
						:list="arrTab" 
						nameKey="name" 
						:current="current" 
						@change="tabsChange" 
						style="width:100%;"/>
				</view>
			</view>
			
			<view style="height:100%;overflow:hidden;">
				<swiper class="swiper" :current="current" @animationfinish="animationfinish">
					<swiper-item class="swiper-item" v-for="(tabItem, i) in arrTab" :key="i" style="overflow:auto;">
						<view v-if="tabItem.type == 2" class="def-search topviewnebg">
							<view class="weui-search-bar weui-search-bar_focusing" id="searchBar">
								<view @click="showPop" class="weui-flex_center redtit">筛选<text v-if="arrapply.length > 0" class="droptxt">{{ arrapply.length }}</text>
									<image src="static/image/downimg.png" style="width: 40rpx;height: 40rpx; margin-right: 5px;">
									</image>
								</view>
								<view class="weui-search-bar__form">
									<view class="weui-search-bar__box weui-flex" style="aviewgn-items: center; padding: 0 30rpx">
										<image src="https://osstbfile.tb-n.com/xcxscale/static/image/icon_search.png"
											style="width: 30rpx; height: 30rpx; margin-right: 25rpx"></image>
										<input v-model="searchtxt" placeholder-class="phcolor" class="weui-input weui-flex__item"
											placeholder="请输入就餐统计名称" style="font-size: 14px" />
										<image v-if="searchtxt" @click="delSearchtxt" src="https://osstbfile.tb-n.com/xcxscale/static/image/icon_close.png"
											style="width: 30rpx; height: 30rpx"></image>
									</view>
								</view>
								<text @click="search" style="margin-left: 5px; color:#C23C43; font-size: 14px;">搜索</text>
							</view>
							<view v-if="arrapply.length > 0" class="filter-results redtit">筛选结果：
								<view v-for="(item, i) in arrapply" :key="i" class="weui-flexcen"> {{item.text}} <i @click="delWhere(item.type)" class="iconfont icon_close2"></i></view>
							</view>
						</view>
						<z-paging-item ref="swiperItem" :tabIndex="i" :currentIndex="current" @query="initData" @updateList="updateList">
							<!-- 没有数据 -->
							<view v-if="tabItem.arrList.length == 0" class="nomesssage" style="display: none;">
								<image src="static/image/no_metadataimg.png" style="width: 197rpx;height: 200rpx;">
								</image>
								<text class="nomesssage-txt">{{ tabItem.type == 1 ? '当前没有待确认就餐情况！' : '当前没有已确认就餐情况！'}}</text>
							</view>
							<view v-else class="dine-view" v-for="(item, index) in tabItem.arrList" :key="item.recordid">
								<view class="weui-cell" style="border-bottom: 1px soviewd #EBEBEB;">
									<view class="weui-cell__bd">
										<view class="sel-person-viewst green-left">
											<view class="weui-flex " style="aviewgn-items: center;">{{item.name}}</view>
										</view>
									</view>
									<view v-if="item.parentstatus == 1" class="weui-cell__ft graytxt">{{item.altime}}</view>
								</view>
								<view class="dining-viewst">
									<view v-if="tabItem.type == 2" class="weui-cell">
										<view class="weui-cell__bd graytxt">
											就餐确认结果
										</view>
										<view class="weui-cell__ft">
											<text v-if="item.parentstatus == 3" class="greentxt">确认无误</text>
											<text v-else class="yellowtxt">有疑问，已留言</text>
										</view>
									</view>
									<view class="weui-cell">
										<view class="weui-cell__bd graytxt">
											就餐统计时间
										</view>
										<view class="weui-cell__ft">{{item.startdate + '~' + item.enddate}}</view>
									</view>
									<view class="weui-cell">
										<view class="weui-cell__bd graytxt">
											缺勤未就餐天数
										</view>
										<view class="weui-cell__ft">{{item.leavedays}}天</view>
									</view>
									<view @click="toConfirm(item.recordid, index)" class="dining-days">
										{{item.parentstatus == 1 ? '去确认' : '查看详情'}}
									</view>
								</view>
							</view>
						</z-paging-item>
					</swiper-item>
				</swiper>
			</view>
		</z-paging-swiper>

		<!--筛选弹窗-->
		<view v-if="isshow" @click="hidePop" class="shadow-con drop-down">
			<view @click.stop="" class="shadow-box">
				<view class="mgbot70">
					<view class="screen-selbot">
						<view class="filter-lt">筛选日期 </view>
						<view class="screen-sellist">
							<view><text @click="changeTimeIndex(1)" :class="timeIndex == 1 ? 'current' : ''">今天</text></view>
							<view><text @click="changeTimeIndex(2)" :class="timeIndex == 2 ? 'current' : ''">昨天</text></view>
							<view><text @click="changeTimeIndex(3)" :class="timeIndex == 3 ? 'current' : ''">本周</text></view>
							<view><text @click="changeTimeIndex(4)" :class="timeIndex == 4 ? 'current' : ''">上周</text></view>
							<view><text @click="changeTimeIndex(5)" :class="timeIndex == 5 ? 'current' : ''">本月</text></view>
							<view><text @click="changeTimeIndex(6)" :class="timeIndex == 6 ? 'current' : ''">上月</text></view>
							<view><text @click="changeTimeIndex(7)" :class="timeIndex == 7 ? 'current' : ''">最近7天</text></view>
							<view><text @click="changeTimeIndex(8)" :class="timeIndex == 8 ? 'current' : ''">3个月</text></view>
							<view><text @click="changeTimeIndex(9)" :class="timeIndex == 9 ? 'current' : ''">自定义</text></view>
						</view>
					</view>
					<view class="custom-time weui-flex_center" style="clear: both;margin: 20px 20px 0 15px;">
						<view class="custom-list weui-flex_center">
							<image src="static/image/timeico.png" style="width: 10px; height: 10px;"></image> 
							<picker mode="date" :value="startdate" :disabled="timeIndex == 9 ? false : true" @change="chooseStartDate">
								<input type="text" class="time-input" :value="startdate" disabled="true" placeholder="选择日期">
							</picker>
						</view>
						<text>~</text>
						<view class="custom-list weui-flex_center">
							<image src="static/image/timeico.png" style="width: 10px; height: 10px;"></image> 
							<picker mode="date" :value="enddate" :disabled="timeIndex == 9 ? false : true" @change="chooseEndDate">
								<input type="text" class="time-input" :value="enddate" disabled="true" placeholder="选择日期">
							</picker>
						</view>
					</view>
					<view class="screen-selbot">
						<view class="filter-lt">账单状态 </view>
						<view class="screen-sellist">
							<view><text @click="changeStatusIndex(1)" :class="statusIndex == 1 ? 'current' : ''">确认无误</text></view>
							<view><text @click="changeStatusIndex(2)" :class="statusIndex == 2 ? 'current' : ''">有疑问，已留言</text></view>
						</view>
					</view>
				</view>
			</view>
			<view @click.stop="" style="padding: 15px 0;position: fixed;bottom: 0;width: 90%;z-index: 9; background: #fff; right: 0;">
				<!--保存发布按钮-->
				<view class="weui-flex" style="margin:0px 15px; border: 1px solid #C23C43;border-radius:40px; background: #C23C43;"> 
					<a href="javascript:;" @click="reset" class="weui-btn weui-flex__item" style="background:#FFECEF;border-radius:40px 0 40px 40px;color: #C23C43;">重置</a> 
					<a href="javascript:;" @click="apply" class="weui-btn" style="background: #C23C43;border-radius:0 40px 40px 0;margin: 0;width:49%;">确认 </a> 
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js");
	var app = getApp();
	var pagesize = 10;

	export default {
		data() {
			return {
				myinfo: app.globalData.objuserinfo,
				yeyname: '',
				tabsStyle: {},
				activeColor: "#C23C43",
				arrTab: [{
					name: "待确认",
					type: 1,
					isfirst: true,
					arrList: [],
					//当前tab下对应的筛选条件
					where: {
						parentstatus_wait: [1]
					},
				},
				{
					name: "已确认",
					type: 2,
					isfirst: true,
					arrList: [],
					where: {
						parentstatus_confirm: [2]
					}
				}],
				current: 0,
				arrapply: [],
				searchtxt: '',
				isshow: false,
				timeIndex: 0,
				timeIndexText: '',
				startdate: '',
				enddate: '',
				statusIndex: 0,
				statusIndexText: '',
				parentstatus: 0,
				swhere: {
					timeIndex: 0,
					timeIndexText: '',
					startdate: '',
					enddate: '',
					statusIndex: 0,
					statusIndexText: '',
					parentstatus: 0
				},
			}
		},
		onLoad: function(params) {
		},
		onShow() {

		},
		methods: {
			initData(pageNo, pageSize, e, tabIndex) {
				var _this = this;
				var where = {};
				if(this.current == 1){
					where.parentstatus_confirm = [2];
					if(this.searchtxt){
						where.searchtxt = [this.searchtxt];
					}
					var swhere = this.swhere;
					if(swhere.startdate){
						where.startdate = [swhere.startdate];
					}
					if(swhere.enddate){
						where.enddate = [swhere.enddate + ' 23:59:59'];
					}
					if(swhere.parentstatus){
						where.parentstatus = [swhere.parentstatus];
					}
				} else {
					where.parentstatus_wait = [1];
				}
				console.log("where:", where);
				var offset = (pageNo - 1) * pageSize;
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg(err);
					}
					console.log('re:', re);
					var arrList = _this.arrTab[tabIndex].arrList;
					if(pageNo == 1){
						arrList = [];
					}
					arrList = arrList.concat(re);
					_this.arrTab[tabIndex].arrList = arrList;
					_this.$refs.swiperItem[tabIndex].complete(re);
				}, ["meals.selectconfirmlistbystuno", uni.msgwhere(where), pageSize, offset], {route: uni.svs.zxx})
			},
			updateList(data) {
				console.log("updateList");
				console.log(data);
				// 更新当前对应tab的数据，注意这里请用$set而非this.dataList[this.current]=data，因为需要触发列表渲染
				// this.$set(this.dataList, this.current, data);
			},
			//swiper滑动结束
			animationfinish: function(e) {
				var index = e.detail.current;
				this.tabsChange(index);
				this.$refs.swiperItem[index].reload();
			},
			tabsChange(index){
				console.log("index:", index);
				console.log("current:", this.current);
				this.current = index;
			},
			toConfirm(recordid, i){
				this.index = i;
				uni.navigateTo({
					url: "/pages/home/<USER>" + recordid
				})
			},
			refresh(parentstatus){
				this.$refs.swiperItem[this.current].reload();
			},
			showPop(){
				this.isshow = true;
			},
			hidePop(){
				this.isshow = false;
			},
			changeTimeIndex(index){
				this.timeIndex = index;
				var today = new Date();
				if(index == 1){//今天
					this.startdate = util.getStrByDate(today);
					this.enddate = util.getStrByDate(today);
					this.timeIndexText = '今天';
				} else if(index == 2){//昨天
					this.startdate = util.getpreday(util.getStrByDate(today), 1);
					this.enddate = util.getpreday(util.getStrByDate(today), 1);
					this.timeIndexText = '昨天';
				} else if(index == 3){//本周
					this.startdate = util.getMondayDate(today);
					this.enddate = util.getSundayDate(today);
					this.timeIndexText = '本周';
				} else if(index == 4){//上周
					var date = util.getpreday(util.getStrByDate(today), 7);
					date = util.getDateByStr(date);
					this.startdate = util.getMondayDate(date);
					this.enddate = util.getSundayDate(date);
					this.timeIndexText = '上周';
				} else if(index == 5){//本月
					this.startdate = util.getMonthFirstDay(today);
					this.enddate = util.getMonthLastDay(today);
					this.timeIndexText = '本月';
				} else if(index == 6){//上月
					this.startdate = util.getMonthFirstDay(today, -1);
					this.enddate = util.getMonthLastDay(today, -1);
					this.timeIndexText = '上月';
				} else if(index == 7){//最近7天
					this.startdate = util.getpreday(util.getStrByDate(today), 6);
					this.enddate = util.getStrByDate(today);
					this.timeIndexText = '最近7天';
				} else if(index == 8){//3个月
					this.startdate = util.getMonthDate(util.getStrByDate(today), -3);
					this.enddate = util.getStrByDate(today);
					this.timeIndexText = '3个月';
				} else if(index == 9){//自定义
					this.startdate = '';
					this.enddate = '';
					this.timeIndexText = '自定义';
				}
			},
			chooseStartDate(e){
				this.startdate = e.detail.value;
			},
			chooseEndDate(e){
				this.enddate = e.detail.value;
			},
			changeStatusIndex(index){
				this.statusIndex = index;
				if(index == 1){
					this.parentstatus = 3;
					this.statusIndexText = '确认无误';
				} else {
					this.parentstatus = 2;
					this.statusIndexText = '有疑问，已留言';
				}
			},
			reset(){
				this.timeIndex = 0;
				this.timeIndexText = '';
				this.startdate = '';
				this.enddate = '';
				this.statusIndex = 0;
				this.statusIndexText = '';
				this.parentstatus = 0;
			},
			apply(){
				this.isshow = false;
				var arrapply = [];
				if(this.timeIndexText){
					arrapply.push({
						type: "time",
						text: this.timeIndexText
					});
				}
				if(this.statusIndexText){
					arrapply.push({
						type: "status",
						text: this.statusIndexText
					});
				}
				this.arrapply = arrapply;
				var swhere = {
					timeIndex: this.timeIndex,
					timeIndexText: this.timeIndexText,
					startdate: this.startdate,
					enddate: this.enddate,
					statusIndex: this.statusIndex,
					statusIndexText: this.statusIndexText,
					parentstatus: this.parentstatus
				}
				this.swhere = swhere;
				this.search();
			},
			delWhere(type){
				if(type == 'time'){
					this.timeIndex = 0;
					this.timeIndexText = '';
					this.startdate = '';
					this.enddate = '';
				}
				if(type == 'status'){
					this.statusIndex = 0;
					this.statusIndexText = '';
					this.parentstatus = 0;
				}
				this.apply();
			},
			delSearchtxt(){
				this.searchtxt = '';
				this.search();
			},
			search(){
				this.$refs.swiperItem[1].reload();
			}

		}
	}
</script>

<style>
	page {
		background: #F8F8F8;
	}
	.swiper {
		height: calc(100% - 55px);
	}
	.weui-cell {
		padding: 0px 15px;
	}

	.dine-view {
		background: #fff;
		margin-top: 8px;
		position: relative;
	}

	.approved-img {
		top: 60rpx;
		position: absolute;
		left: 300rpx;
	}

	.weui-cell__bd {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell__ft {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell:before {
		right: 0px;
		left: 0;
		border-top: none;
	}

	.weui-cell__ft.graytxt,
	.weui-cell__bd.graytxt {
		color: #999999;
	}

	.dining-viewst {
		padding: 10px 0;
		margin: 0 15px;
	}

	.dining-viewst .weui-cell {
		padding: 5px 0px;
	}


	.dining-days {
		font-size: 28rpx;
		color: #C23C43;
		text-aviewgn: right;
		text-decoration: underviewne;
		cursor: pointer;
	}

	.filter-results {
		background: #fff;
		padding: 0 15px 10px 15px;
		font-size: 24rpx;
	}

	.filter-results .weui-flexcen {
		margin-right: 5px;
	}
	
	.filter-results view {
		display: inline-block;
		background: #FFEDEF;
		border-radius: 10rpx;
		color: #C23C43;
		padding: 5rpx 10rpx;
		font-size: 22rpx;
		font-weight: normal;
		cursor: pointer;
	}
	
	
	.icon_close2:after {
		color: #C23C43;
		font-size: 16rpx;
		vertical-align: middle;
		margin-left: 10px;
	}

	.droptxt {
		padding: 0 8rpx;
		height: 30rpx;
		viewne-height: 30rpx;
		background: #C23C43;
		border-radius: 12px;
		font-size: 20rpx;
		color: #FFFFFF;
		text-aviewgn: center;
		margin-left: 3px;
		font-weight: normal;
	}

	.costtxt {
		background: #FFECEF;
		border-radius: 10rpx;
		color: #C23C43;
		padding: 5rpx 10rpx;
		margin-left: 10px;
		font-size: 24rpx;

	}
</style>
