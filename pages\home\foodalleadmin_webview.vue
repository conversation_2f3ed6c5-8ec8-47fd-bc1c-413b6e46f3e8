<template>
	<view>
		<web-view 
			ref="webview"
			:webview-styles="webviewStyles" 
			:src="webviewUrl"
			@message="handleMessage"
			bindmessage="handleMessage"
		></web-view>
	</view>
</template>

<script>
var app = getApp();
import uploadUtil from '@/common/uploadUtil.js';
export default {
	data() {
		return {
			webviewStyles: {
				progress: {
					color: '#FF3333'
				}
			},
			webviewUrl: '',
			params: {}
		}
	},
	onLoad(params) {
		this.params = params;
		console.log('params',params);
		this.initData();
	},

	methods: {
		initData() {
			var _this = this;
			//根据日期生成一个随机数
			var date = new Date();
			var random = date.getTime();
			var stuno = app.globalData.objuserinfo.stuno;
			var token = uni.getStorageSync('token');
			// _this.webviewUrl = app.globalData.zxxurl + '/wxzxx/askleave.html?v=' + random + '&stuno='+stuno+'&token=' + token;
			_this.webviewUrl = app.globalData.zxxurl + '/wxzxx/zxxwxmini.html?v=' + random + '&isminiprogram=1&flag=foodalleadmin&stuno='+stuno+'&token=' + token;
			if(_this.params.upimgs){
				_this.webviewUrl = _this.webviewUrl + '&upimgs=' + _this.params.upimgs
			}
			console.log(_this.webviewUrl)
		},
		
		handleMessage(e) {
			console.log('收到H5消息:', e);
			// 小程序环境
			// #ifdef MP-WEIXIN
			const message = e.detail.data[0];
			// #endif

			// H5环境
			// #ifdef H5
			const message = e.data;
			// #endif

			if(message && message.action === 'message') {
					switch(message.type) {
						case 'uploadImage':
							this.chooseAndUploadImage();
							break;
					}
				}
			
			 
		},
		
		chooseAndUploadImage() {
			var _this = this;
			uni.chooseImage({
				count: 9, // 最多选择9张图片
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => { 
					console.log('res', res);
					uni.showLoading();
					uploadUtil.uploadServer({
						arrfile: res.tempFiles,
						// #ifdef H5
						serverurl: "/" +uni.svs.zxx+ '/ueditorController?Authorization=Bearer ' + uni.getStorageSync('token') + '&action=uploadfile&upRePath=unieye/users/',// 文件接收服务端

						// #endif
						// #ifndef H5
						serverurl: app.globalData.geturl(uni.svs.zxx, "ueditorController") + '?Authorization=Bearer ' + uni.getStorageSync('token') + '&action=uploadfile&upRePath=unieye/users/',// 文件接收服务端
						// #endif
						complete: function (arrpath) {
							console.log('arrpath', arrpath);
							var idcardphoto = arrpath[0].key;
							//保存证件照片
							// uni.sm(function (re, err) {
							// 	uni.hideLoading();
							// 	if (err) {
							// 		return uni.msg("上传失败，请重试");
							// 	}
							// 	uni.msg("上传成功");
							// 	_this.objdata.idcard_photo = idcardphoto;
							// 	if (_this.objdata.userimg && _this.objdata.nickname && _this.objdata.mobile && _this.objdata.truename && _this.objdata.idcard && _this.objdata.idcard_photo) {
							// 		_this.doComplete("iscomplete", "1");
							// 	}
							// }, ["unieye.user.editoneitembyid", uni.msgwhere({ column: ['idcard_photo'] }), uni.msgwhere({ val: [idcardphoto] })])

							_this.postMessageToWebview({
								type: 'uploadSuccess',
								data: idcardphoto
							});

						}
					})	
				}
			});
		},
		
		postMessageToWebview(message) {
			//todo 以下方法无效 小程序不能通过web-view组件通信

			// 小程序环境通过web-view组件通信
			// #ifdef MP-WEIXIN
			this.$refs.webview.postMessage({
					data: [message] // 小程序需要数组格式
				});
			// #endif
			
			// H5环境
			// #ifdef H5 
			const currentWebview = this.$scope.$getAppWebview();
				currentWebview.evalJS(`
					window.postMessage(${JSON.stringify({
						data: [message]
					})}, '*')
				`);
			// #endif
		}
	}
}
</script>

<style></style>