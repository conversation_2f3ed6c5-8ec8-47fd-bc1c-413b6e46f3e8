<template>
	<view>
		<view  class="pad160">
		<view class="marbot10">
			<view class="yllist" v-for="(item,key) in objyeyarrstu" :key="key">
				<view>
					<view class="weui-cell" style="border-bottom: 1px solid #EBEBEB;padding: 0px 15px; ">
						<view class="weui-cell__bd">
							<view class="sel-person-list green-left" style="height: 38px;">
								<view class="weui-flex " style="align-items: center;">{{item.yeyname}}</view>
							</view>
						</view>
						<!-- <view class="weui-cell__ft weui-cell__ft_in-access"></view> -->
					</view>
				</view>
				<radio-group @change="chooseStu">
				<view v-for="(objstu, i) in item.arrstu" :key="i">
					<view class="weui-cell cleancen ">
						<view class="weui-cell__bd weui-flexcen">
							<label class="radio_style weui-flex_center">
								<radio :checked="objstu.checked" :value="objstu.id"></radio>
							</label>
							<image src="static/image/img_def.png" class="img_def"></image>
							<view>
								<view class="clean-list"><text style="color: #303030;" class="font14">{{objstu.stuname}}-我是{{objstu.pname}}</text>（{{objstu.claname}}）
								</view>
								<view class="clean-list"><text class="font14">{{ objstu.birthday | getAge}}</text></view>
							</view>
						</view>
						<view class="weui-cell__ft">
							<button class="common-btn" style="margin: 20px auto;background:none;" open-type="share" 
								:data-stuid="objstu.id" 
								:data-stuname="objstu.stuname" 
								:data-pname="objstu.pname">
								<image src="static/image/share.png" style="width: 49rpx; height:49rpx;"></image>
							</button>
							
						</view>
					</view>

					<view class="relevance-list weui-flexcen">
						<view style="color: #999999;">关联家长：</view>
						<view class="relevance-listjz" v-for="(objparent, index) in objstu.arrparent" :key="index">
							<image src="static/image/img_def.png"
								style="width: 48rpx; height: 48rpx;border-radius: 50%;"></image>
							<text class="font12" style="display: block;">{{objparent.pname}}</text>
						</view>
					</view>
				</view>
				</radio-group>
			</view>
		</view>
		</view>
		        <!-- 提交按钮 -->
		                        <view class="button-bottom " style="height: 160rpx;box-shadow: none;border-top:none;">
		                                <button class="common-btn   noafter" @click="addStu">添加学生</button>
		                        </view>
	
</view>
</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js")
	var app = getApp();
	
	export default {
		data() {
			return {
				myinfo: app.globalData.objuserinfo,
				arrptype: [
					{ptype: 1, pname: '爸爸'},
					{ptype: 2, pname: '妈妈'},
					{ptype: 3, pname: '爷爷'},
					{ptype: 4, pname: '奶奶'},
					{ptype: 5, pname: '姥爷'},
					{ptype: 6, pname: '姥姥'}
				],
				objyeyarrstu: {}
			}
		},
		onLoad(params) {
			this.initData2();
		},
		onShow() {
		
		},
		onShareAppMessage: function(res) {
			console.log(res);
		    var _this = this;
		    if (res.from === 'button') {
		      // 来自页面内转发按钮
		      console.log(res.target);
		    }
			var pname = res.target.dataset.pname;
			var stuid = res.target.dataset.stuid;
			var stuname = res.target.dataset.stuname;
		    return {
		      title: stuname + pname + '邀请您加入',
		      path: '/pages/mine/student_share_add?stuid=' + stuid + '&inviterid=' + _this.myinfo.id
		    }
		},
		computed: {
		},
		filters: {
			getAge(birthday) {
				if(!birthday){
					return '';
				}
				return util.GetAge(birthday, new Date(), 'text', true);
			}
		},
		methods: {
			initData(){
				var starttime = new Date().getTime();
				var _this = this;
				var arrptype = this.arrptype;
				uni.sm(function(re, err){
					if(err){
						return uni.msg(err);
					}
					var arrmsg = [];
					for (var i = 0; i < re.length; i++) {
						arrmsg.push(["userstu.selectuserptype", re[i].yeyid, re[i].stuno]);
						var ptype = re[i].ptype;
						for (var k = 0; k < arrptype.length; k++) {
							if(arrptype[k].ptype == ptype){
								re[i].pname = arrptype[k].pname;
								break;
							}
						}
					}
					uni.sm(function(re2, err2){
						for (var i = 0; i < re2.length; i++) {
							for (var j = 0; j < re2[i].length; j++) {
								var ptype = re2[i][j].ptype;
								for (var k = 0; k < arrptype.length; k++) {
									if(arrptype[k].ptype == ptype){
										re2[i][j].pname = arrptype[k].pname;
										break;
									}
								}
							}
							re[i].arrparent = re2[i];
						}
						var objyeyarrstu = {};
						for (var i = 0; i < re.length; i++) {
							var obj = objyeyarrstu[yeyid];
							var yeyid = re[i].yeyid;
							if(!objyeyarrstu[yeyid]){
								obj = {
									yeyid: yeyid,
									yeyname: re[i].yeyname,
									arrstu: []
								};
							}
							obj.arrstu.push(re[i]);
							objyeyarrstu[yeyid] = obj;
						}
						console.log(objyeyarrstu);
						console.log(new Date().getTime() - starttime);
						_this.arrstu = re;
					}, arrmsg, {route: uni.svs.zxx_home_biz})
				}, ["stu.selectuserstu", this.myinfo.id], {route: uni.svs.zxx_home_biz})
			},
			initData2(){
				var starttime = new Date().getTime();
				var _this = this;
				var arrptype = this.arrptype;
				uni.sm(function(re, err){
					if(re[0].error || re[1].error){
						return uni.msg(re[0].error || re[1].error);
					}
					var objstuparents = {};
					for (var i = 0; i < re[1].length; i++) {
						var obj = re[1][i];
						var key = obj.yeyid + '_' + obj.stuno;
						if(!objstuparents[key]){
							objstuparents[key] = [];
						}
						var ptype = re[1][i].ptype;
						for (var k = 0; k < arrptype.length; k++) {
							if(arrptype[k].ptype == ptype){
								re[1][i].pname = arrptype[k].pname;
								break;
							}
						}
						objstuparents[key].push(re[1][i]);
					}
					var objyeyarrstu = {};
					for (var i = 0; i < re[0].length; i++) {
						var ptype = re[0][i].ptype;
						for (var k = 0; k < arrptype.length; k++) {
							if(arrptype[k].ptype == ptype){
								re[0][i].pname = arrptype[k].pname;
								break;
							}
						}
						if(re[0][i].id == _this.myinfo.stu_id){
							re[0][i].checked = true;
						}
						var key = re[0][i].yeyid + '_' + re[0][i].stuno;
						re[0][i].arrparent = objstuparents[key] || [];
						var yeyid = re[0][i].yeyid;
						if(!objyeyarrstu[yeyid]){
							objyeyarrstu[yeyid] = {
								yeyid: yeyid,
								yeyname: re[0][i].yeyname,
								arrstu: []
							}
						}
						objyeyarrstu[yeyid].arrstu.push(re[0][i]);
					}
					console.log(objyeyarrstu);
					console.log(new Date().getTime() - starttime);
					_this.objyeyarrstu = objyeyarrstu;
				}, [["stu.selectuserstu", this.myinfo.id],
				["userstu.selectparents", this.myinfo.id]], {route: uni.svs.zxx_home_biz})
			},
			addStu(){
				uni.navigateTo({
					url: '/pages/home/<USER>'
				})
			},
			toMyinfo(){
				uni.navigateTo({
					url: "/pages/mine/myinfo" 
				})
			},
			toMybabys(){
				uni.navigateTo({
					url: "/pages/mine/mybabys" 
				})
			},
			chooseStu(e){
				console.log(e);
				var stuid = e.detail.value;
				this.changeCurBindStu(stuid, function(){
					uni.msg("切换学生成功");
				})
			},
			changeCurBindStu(stuid, cb){//切换当前关联学生
				var _this  = this;
				uni.smaction(function(re, err){
					uni.hideLoading();
					if(err){
						return uni.msg(err);
					}
					app.globalData.objuserinfo.stu_id = stuid;
					app.globalData.objuserinfo.yeyid = re.yeyid;
					app.globalData.objuserinfo.yeyname = re.yeyname;
					app.globalData.objuserinfo.guid = re.yguid;
					app.globalData.objuserinfo.classno = re.classno;
					app.globalData.objuserinfo.claname = re.claname;
					app.globalData.objuserinfo.stuno = re.stuno;
					app.globalData.objuserinfo.stuname = re.stuname;
					app.globalData.objuserinfo.stusex = re.sex;
					app.globalData.objuserinfo.photopath = re.photopath;
					app.globalData.objuserinfo.birthday = re.birthday;
					app.globalData.objuserinfo.intime = re.intime;
					app.globalData.objuserinfo.filenumber = re.filenumber;
					cb && cb();
				}, {stuid: stuid}, {route:uni.svs.zxx_home_biz, action: 'mine/changestu'})
			}
		}
	}
</script>

<style>
	page {
		background: #F2F4F5;
	}

	.coffee-col {
		color: #CF9155;
	}

	.cleancen {
		padding: 15px 30rpx;
	}


	.img_def {
		margin: 0px 20rpx 0 20rpx;
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;

	}

	.numbertxt {
		font-size: 26rpx;
		color: #827E7B;

	}

	.clean-list text {
		color: #999999;
	}

	.clean-list {
		color: #666666;
		font-size: 14px;
		line-height: 25px;
	}

	.relevance-list {
		background: #EBFBF8;
		padding: 10px;
		font-size: 28rpx;
	}

	.relevance-listjz {
		text-align: center;
		width: 48rpx;
		margin-left: 10px;
	}

	.weui-cell:before {
		border-top: none;
	}
</style>