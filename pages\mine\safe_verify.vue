<template>
	<view>
		<view style="margin: 120rpx 60rpx 0 60rpx;">
			<view class="login-inp weui-flex">
				<input type="number" v-model="phone" @input="inputmobile" placeholder="请输入孩子档案中父亲或母亲手机号" placeholder-style="color: #cccccc;" style="width:100%;" />
			</view>
			<view class="login-inp weui-flex" style="margin-top: 20rpx;">
				<input type="text" v-model="yanzhengmavalue" placeholder="请输入验证码" placeholder-style="color: #cccccc;" class="weui-flex__item" />
				<Verify @success='success' :mode="'pop'" :captchaType="'clickWord'" ref="verify" :imgSize="{width:'310px',height:'155px'}"></Verify>
				<button v-if="yanzhengmabtndisabled" class="borderbtn noafter">{{yanzhengmabtntext}}</button>
				<button v-else class="borderbtn borblue noafter" @click="showVerify">获取验证码</button>
			</view>
			<button class="fullbtn noafter" style="margin-top: 70rpx;" @click="bindVerify()">开始验证</button>
		</view>
	</view>
</template>

<script>
	import Verify from "@/components/verify/verify.vue"
	const app = getApp();

	export default {
		data() {
			return {
				env_version: app.globalData.env_version,
				t: null,
				phone: '',
				yanzhengmavalue: "",
				yanzhengmabtndisabled: true,
				yanzhengmabtntext: "获取验证码",
				objsendtime: 300
			}
		},
		onLoad(params) {
			this.faphone = params.faphone;
			this.mophone = params.mophone;
		},
		components: {
			Verify
		},
		methods: {
			showVerify() {
				if(this.phone == this.faphone || this.phone == this.mophone){
					this.$refs.verify.show();
				} else {
					uni.msg("请输入孩子档案中父亲或母亲的手机号");
				}
			},
			success(data) {
				var _this = this;
				setTimeout(function() {
					_this.$refs.verify.hide();
				}, 1000);
				this.tapgetyanzhengma(data.captchaVerification);
			},
			inputmobile(e) {
				var txtmobile = e.detail.value;
				if (txtmobile && txtmobile.charAt(0) == 1 && txtmobile.length == 11) {
					this.yanzhengmabtndisabled = false
				} else {
					this.yanzhengmabtndisabled = true;
				}
			},
			//获取验证码
			tapgetyanzhengma(captchaVerification) {
				var _this = this;
				var txtmobile = this.phone;
				if (txtmobile && txtmobile.charAt(0) == 1 && txtmobile.length == 11) {
					uni.smaction(function(re, err) {
						if(err){
							_this.yanzhengmabtndisabled = false;
							console.log('系统错误', err)
							return;
						}
						_this.objphone = txtmobile;
						uni.msg("发送成功，" + re.msg);
						if (_this.t) {
							clearInterval(_this.t);
						}
						_this.t = setInterval(function() {
							_this.objsendtime--;
							_this.yanzhengmabtndisabled = true;
							_this.yanzhengmabtntext = _this.objsendtime + ' 秒';
							if (_this.objsendtime == 0) {
								_this.yanzhengmabtndisabled = false;
								_this.yanzhengmabtntext = "获取验证码";
								clearInterval(_this.t);
								_this.objsendtime = 300;
							}
						}, 1000);
						// if (re.code == 200) {
							
						// } else if (re.code == 500) {
						// 	_this.yanzhengmabtndisabled = false
						// 	console.log(err)
						// } else {
						// 	_this.yanzhengmabtndisabled = false
						// 	console.log('系统错误')
						// }
					}, {mobile: txtmobile, captchaVerification: captchaVerification}, {route: uni.svs.zxx_home_biz, action: 'mine/sendvcode'});
				} else {
					return uni.msg("请输入正确的手机号");
				}
			},
			//开始验证
			bindVerify() {
				if (!this.phone) {
					return uni.msg("请输入手机号");
				}
				if (!this.yanzhengmavalue) {
					return uni.msg("请输入验证码");
				}
				uni.showLoading({
					title: '验证中...',
				})
				var _this = this;
				uni.smaction(function(re, err) {
					uni.hideLoading();
					if (re.code == 200) {
						uni.getPrePage().startBind(2, function(){
							uni.reLaunch({
								url: "/pages/tabBar/home/<USER>"
							})
						})
					} else {
						console.log('系统错误')
					}
				}, {mobile: this.phone, vcode: this.yanzhengmavalue}, {route: uni.svs.zxx_home_biz, action: 'mine/verifyvcode'});
			}
		}
	}

	function _logincallback() {
		var PrePage = uni.getPrePage();
		if (PrePage) {
			PrePage.logincallback && PrePage.logincallback();
			uni.navigateBack();
		} else {
			uni.reLaunch({
				url: "/pages/tabBar/home/<USER>"
			});
		}
	}
</script>

<style>
	page {
		background: #ffffff;
	}

	#app,
	uni-app,
	uni-page,
	uni-page-wrapper {
		height: inherit;
	}

	.login-inp {
		align-items: center;
		border-bottom: 1px solid #f8f8f8;
		height: 70rpx;
		line-height: 70rpx;
		padding: 15rpx 0;
	}


	.tab-menu {
		display: flex;
	}

	.tab-item {
		width: 50%;
		box-sizing: border-box;
		height: 50rpx;
		line-height: 50rpx;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.tab-item:nth-child(1) {
		border-right: 1rpx solid #ccc;
	}

	.active {
		color: #007AFF;
	}

	.content {
		width: 700rpx;
		margin: 100rpx auto;
		/* border: 1rpx solid #007AFF; */
		padding: 10rpx;
	}

	.title {
		font-size: 35rpx;
		color: #808080;
	}

	.form {
		margin-top: 15rpx;
		padding: 10rpx;
	}

	.form-item {
		padding: 10rpx 10rpx 10rpx 0;
		margin-top: 10rpx;
	}

	.form-item input {
		width: 90%;
		border: 1rpx solid #ccc;
		height: 50rpx;
		line-height: 50rpx;
		padding: 10rpx;
		color: #C8C7CC;
	}

	.verify-btn {
		margin: 10rpx 0;
		width: 90%;
		padding-left: 10rpx;
	}

	.space {
		height: 5rpx;
		background-color: #ccc;
		margin: 30rpx 0;
	}

	uni-checkbox .uni-checkbox-input {
		width: 16px;
		height: 16px;
	}
</style>