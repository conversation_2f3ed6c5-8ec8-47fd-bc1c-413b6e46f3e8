<template>
	<view>
		<view id="scale-wrapper" class="wrapper" :style="'background:' + bgoutside">
			<view class="zhongjianxian" :style="'height:' + h / 2 + 'px;border:1px ' + lineSelect + ' solid;'"></view>
			<scroll-view class="scroll-view" :scroll-x="true" :scroll-left="centerNum" :scroll-with-animation="true" @scroll.stop.prevent="bindscroll">
				<view id="canvas" class="scroll-wrapper">
					<view class="seat" :style="'width:' + (windowWidth / 2 - fiexNum / 2) + 'px;'"></view>
					<view class="scale-image" :style="'width:' + width + 'px; height:' + h + 'px'">
						<image :src="ruleimgsrc" mode=""></image>
					</view>
					<view class="seat" :style="'width:' + (windowWidth / 2 - fiexNum / 2) + 'px;'"></view>
				</view>
				<view class="oldvalxian" :style="'height:' + (h / 2 - 5) + 'px;border:1px red solid;left:' + oldvalpx + 'px'"></view>
			</scroll-view>
		</view>
		<view class="canvas" :style="'width:' + width + 'px;'" v-if="!ruleimgsrc">
			<canvas class="canvas" :style="'width:' + width + 'px;'" canvas-id="canvas"></canvas>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imageWidth: '',
				bgoutside: '#dbdbdb',
				lineSelect: '#52b8f5',
				scaleId: '',
				rul: {},
				assingOldVal: -1,
				windowWidth: '',
				width: '',
				oldvalpx: '',
				centerNum: '',
				ruleimgsrc: '',
				round: '',
				canvashide: false
			};
		},
		/**
		 * 组件的属性列表
		 */
		props: {
			// 最小值
			min: {
				type: Number,
				default: 0
			},
			//最大值
			max: {
				type: Number,
				default: 100
			},
			// 是否开启整数模式
			int: {
				type: Boolean,
				default: true
			},
			// 每个格子的长度（只能是 1 ，2 ，5 一个能被10整除的数字 ）
			step: {
				type: Number,
				default: 1
			},
			// 每个格子的实际行度 （单位px ，相对默认值）
			single: {
				type: Number,
				default: 10
			},
			// 卡尺左右的余量 ，最为60
			fiexNum: {
				type: Number,
				default: 60
			},
			// 高度
			h: {
				type: Number,
				default: 80
			},
			// 高度
			rulesrc: {
				type: String,
				default: ''
			},
			// 当前选中
			activeH: {
				type: null,
				default: '0'
			},
			styles: {
				type: Object,
				default: () => ({
					line: '#dbdbdb',
					bginner: '#fbfbfb',
					bgoutside: '#dbdbdb',
					lineSelect: '#52b8f5',
					font: '#404040'
				})
			}
		},
		mounted() {
			// 每次初始化 全局变量
			this.init_h();
		},
		/**
		 * 组件的方法列表
		 */
		methods: {
			/**
			 * 初始化卡尺
			 */
			init_h() {
				let self = this;
				let rul = {
					spa: '',
					// 单个格子的距离
					unitNum: '',
					// 格子总数
					minNum: self.min,
					maxNum: self.max,
					num: self.max - self.min,
					// 仿数据总数
					FIXED_NUM: self.fiexNum,
					// 标尺左右空余部分
					single: self.single,
					step: self.step,
					h: self.h,
					activeH: '',
					styles: self.styles
				};

				this._getErro(rul); //  获取节点信息，获取节点宽度

				var query = uni.createSelectorQuery().in(this);
				query
					.select('#scale-wrapper')
					.boundingClientRect((res) => {
						res.top; // 这个组件内 #the-id 节点的上边界坐标
					})
					.exec((e) => {
						// 获节点宽度
						rul.windowWidth = e[0].width; // 判断是否使用整数类型

						if (self.int) {
							rul.unitNum = rul.num / rul.step;
						} else {
							rul.unitNum = rul.num * (rul.single / rul.step);
						} 
						
						// 设置单个格子的长度
						rul.spa = rul.single * rul.step;
						rul.total = rul.spa * rul.unitNum + rul.FIXED_NUM;

						if (self.rulesrc) {
							let centerNum = self.assignValue(self, rul);
							var rulesrc = self.rulesrc;
							var oldvalpx = centerNum + rul.windowWidth / 2 - 1;
							self.windowWidth= e[0].width;
							self.width= rul.total;
							self.fiexNum= rul.FIXED_NUM;
							self.rul=rul;
							self.oldvalpx=oldvalpx;
							self.centerNum= centerNum;
							self.ruleimgsrc= "/static/image/" + rulesrc + ".png";
							self.round= self.int ? rul.minNum : rul.minNum.toFixed(1);
							self.bgoutside= rul.styles.bgoutside;
							self.lineSelect= rul.styles.lineSelect;
							 
							self.$emit('valueH', {
								detail: {
									value: self.round
								}
							});
						} else {
							self.windowWidth= e[0].width;
							self.width= rul.total;
							self.fiexNum= rul.FIXED_NUM;
							self.rul=rul;
							self.draw(rul.num, rul.total, {
								self,
								rul
							});
						}
					});
			},

			/**
			 * 绘制
			 * 生成卡尺
			 */
			draw(num, total, {self,rul}) {
				let canvasHeight = 80;
				let ctx = uni.createCanvasContext('canvas', self); //  绘制背景

				ctx.save();
				ctx.setFillStyle(rul.styles.bginner);
				ctx.fillRect(0, 0, total, canvasHeight);
				ctx.restore();
				ctx.beginPath();
				ctx.setLineWidth(1);
				ctx.setStrokeStyle(rul.styles.line);
				ctx.moveTo(rul.FIXED_NUM / 2, 0);
				ctx.lineTo(total - rul.FIXED_NUM / 2, 0);
				ctx.stroke();

				for (let i = 0; i < rul.unitNum + 1; i++) {
					// 绘制文字
					if (i % (rul.single / rul.step) === 0) {
						ctx.setFontSize(18);
						ctx.setFillStyle(rul.styles.font);
						ctx.setTextAlign('center');

						if (self.int) {
							ctx.fillText(i * rul.step + rul.minNum, rul.FIXED_NUM / 2 + i * rul.spa, canvasHeight - 15);
						} else {
							ctx.fillText(i / (rul.single / rul.step) + rul.minNum, rul.FIXED_NUM / 2 + i * rul.spa, canvasHeight - 15);
						}
					} // 绘制刻度

					if (i % 5 === 0) {
						ctx.beginPath();
						ctx.setLineWidth(2);
						ctx.setStrokeStyle(rul.styles.line);
						ctx.moveTo(rul.FIXED_NUM / 2 + i * rul.spa, 0);
						ctx.lineTo(rul.FIXED_NUM / 2 + i * rul.spa, canvasHeight / 2);
						ctx.stroke();
					} else {
						ctx.beginPath();
						ctx.setLineWidth(1);
						ctx.setStrokeStyle(rul.styles.line);
						ctx.moveTo(rul.FIXED_NUM / 2 + i * rul.spa, 0);
						ctx.lineTo(rul.FIXED_NUM / 2 + i * rul.spa, canvasHeight - 50);
						ctx.stroke();
					}
				}

				ctx.draw(
					true,
					setTimeout(() => {
						uni.canvasToTempFilePath({
								x: 0,
								y: 0,
								width: total,
								height: canvasHeight,
								// destWidth: total * 4,
								// destHeight: canvasHeight * 4,
								canvasId: 'canvas',
								success: (res) => {
									// 改变高度重新计算
									rul.total = (rul.total / 80) * rul.h;
									rul.FIXED_NUM = (rul.FIXED_NUM / 80) * rul.h; // let centerNum = self.int ?
									//   ((rul.activeH - rul.minNum) / rul.step) *
									//   parseInt(rul.total - rul.FIXED_NUM) / rul.num * rul.step :
									//   ((rul.activeH - rul.minNum) * 10 / rul.step) *
									//   parseFloat((rul.total - rul.FIXED_NUM)) / rul.num / (rul.single / rul.step)

									let centerNum = this.assignValue(this, rul);
									self.canvashide= true;
									self.ruleimgsrc= res.tempFilePath;
									self.centerNum;
									self.width= rul.total;
									self.h= rul.h;
									self.fiexNum= rul.FIXED_NUM;
									self.round= self.int ? rul.minNum : rul.minNum.toFixed(1);
									self.bgoutside= rul.styles.bgoutside;
									self.lineSelect= rul.styles.lineSelect;
									self.$emit('valueH', {
										detail: {
											value: self.round
										}
									});
									self.$emit('imageload', {
										detail: {
											value: self.round
										}
									});
								},

								fail(e) {
									console.log(e);
								}
							},
							this
						);
					}, 100)
				);
			},

			/**
			 * 获取滑动的值
			 */
			bindscroll: function(e) {
				let rul = this.rul; // 移动距离

				let left = e.detail.scrollLeft; // 单格的实际距离

				let spa; // 判断是否是整数

				if (this.int) {
					spa = (parseInt(rul.total - rul.FIXED_NUM) / rul.num) * rul.step;
				} else {
					spa = parseFloat(rul.total - rul.FIXED_NUM) / rul.num / (rul.single / rul.step);
				} // 当前显示值

				let resultNum = Math.round(left / spa); // 还原为实际数值

				let redNum = Math.round(resultNum * spa); // 小数位处理

				if (this.int) {
					resultNum = resultNum * rul.step + rul.minNum;
				} else {
					resultNum = ((resultNum * rul.step) / 10 + rul.minNum).toFixed(1);
				}

				if (this.assingOldVal === resultNum) {
					return;
				}

				this.round= resultNum;
				this.assingOldVal= resultNum;
				this.$emit('valueH', {
					detail: {
						value: resultNum
					}
				});
				clearTimeout(rul.Timer);
				rul.Timer = setTimeout(() => {
					// console.log("执行了定时器")
					this.centerNum=redNum;
					this.round=resultNum; 
					this.assingOldVal=resultNum;
					this.$emit('valueH', {
						detail: {
							value: resultNum
						}
					});
				}, 1000);
			},

			/**
			 * 输出错误信息
			 */
			_getErro(rul) {
				// 判断 最大值 最小值 是否 正确
				if (rul.minNum >= rul.maxNum) {
					console.error('您输入的最大值 小于最小值，请检查 minNum ， maxNum');
					rul.minNum = 0;
					rul.maxNum = 100;
					rul.num = rul.maxNum - rul.minNum;
				} // 判断 是否开启整数类型

				if (rul.step !== 1 && rul.step !== 2 && rul.step !== 5) {
					console.error('步长只能是 1 ，2  ，5  ,请检查 step');
					rul.step = 1;
				}

				if (rul.FIXED_NUM < 60) {
					console.warn('左右余量 输入小于 60 ，可能影响显示效果，请检查 fiexNum');

					if (!rul.FIXED_NUM) {
						rul.FIXED_NUM = 60;
					}

					if (rul.FIXED_NUM < 0) {
						console.error('左右余量最小为0  ，请检查 fiexNum');
						rul.FIXED_NUM = 0;
					}
				}

				if (rul.single < 10) {
					console.warn('格子单位小于10 ，可能影响显示效果，请检查 single');

					if (!rul.single) {
						rul.single = 10;
					}
				}

				if (rul.h < 50) {
					console.warn('格子单位小于50 ，可能影响显示效果，请检查 h');

					if (!rul.h) {
						rul.h = 80;
					}

					if (rul.h < 20) {
						console.error('高度最小为20  ，请检查 h');
						rul.h = 20;
					}
				} // 当前选中位置设置

				if (this.activeH === 'min') {
					rul.activeH = rul.minNum;
				} else if (this.activeH === 'max') {
					rul.activeH = rul.maxNum;
				} else if (this.activeH === 'center') {
					rul.activeH = (rul.maxNum + rul.minNum) / 2;
				} else {
					rul.activeH = this.activeH;
				}

				if (this.activeH !== 'min' && this.activeH !== 'max' && this.activeH !== 'center') {
					// console.log("任意数值")
					if (rul.activeH < rul.minNum || rul.activeH > rul.maxNum) {
						console.error('您输入的数值（activeH）超入范围，请检查 activeH');
						this.$emit('error', {
							detail: {
								error: 'notinfanwei',
								activeH: rul.activeH,
								minNum: rul.minNum,
								maxNum: rul.maxNum
							}
						});
					}

					if (rul.activeH % rul.step !== 0 && rul.int) {
						console.warn('您输入的数值（activeH）不是合法数值，请检查，所以导致结果可能有错误');
					}

					if ((rul.activeH * 10) % rul.step !== 0 && !rul.int) {
						console.warn('您输入的数值（activeH）不是合法数值，请检查，所以导致结果可能有错误');
					}
				}

				if (!rul.styles) {
					rul.styles = {};

					if (!rul.styles.line) {
						rul.styles.line = '#dbdbdb';
					}

					if (!rul.styles.lineSelect) {
						rul.styles.lineSelect = '#52b8f5';
					}

					if (!rul.styles.bginner) {
						rul.styles.bginner = '#fbfbfb';
					}

					if (!rul.styles.bgoutside) {
						rul.styles.bgoutside = '#dbdbdb';
					}

					if (!rul.styles.font) {
						rul.styles.font = '#404040';
					}
				} else {
					if (!rul.styles.line) {
						rul.styles.line = '#dbdbdb';
					}

					if (!rul.styles.lineSelect) {
						rul.styles.lineSelect = '#52b8f5';
					}

					if (!rul.styles.bginner) {
						rul.styles.bginner = '#fbfbfb';
					}

					if (!rul.styles.bgoutside) {
						rul.styles.bgoutside = '#dbdbdb';
					}

					if (!rul.styles.font) {
						rul.styles.font = '#404040';
					}
				}
			},

			assignValue(self, rul) {
				return self.int ?
					((((rul.activeH - rul.minNum) / rul.step) * parseInt(rul.total - rul.FIXED_NUM)) / rul.num) * rul.step :
					((((rul.activeH - rul.minNum) * 10) / rul.step) * parseFloat(rul.total - rul.FIXED_NUM)) / rul.num / (rul.single / rul.step);
			}
		},
		watch: {
			activeH: {
				handler: function(newVal, oldVal, changedPath) {
					console.log("old："+oldVal+",new:"+newVal); 
					let centerNum = this.assignValue(this, this.rul);
					this.centerNum = centerNum;
					this.$emit('valueH', {
						detail: {
							value: newVal
						}
					});
				},

				immediate: true
			}
		}
	};
</script>
<style>
	/* pages/test/test.wxss */

	.wrapper {
		position: relative;
		width: 100%;
		box-sizing: border-box;
		background: #dbdbdb;
	}

	.zhongjianxian {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		width: 0px;
		/* height: 40px; */
		margin: auto;
		border: 1px #52b8f5 solid;
		z-index: 999;
	}

	.oldvalxian {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		width: 0px;
		/* height: 40px; */
		margin: auto;
		border: 1px #52b8f5 solid;
		z-index: 999;
	}

	.scroll-wrapper {
		display: flex;
	}

	.scale-image {
		flex-shrink: 0;
		height: 70px;
		box-sizing: border-box;
		/* border: 1px red solid; */
	}

	.scale-image image {
		width: 100%;
		height: 100%;
	}

	.seat {
		flex-shrink: 0;
		box-sizing: border-box;
	}

	.canvas {
		position: absolute;
		overflow: hidden;
		box-sizing: border-box;
		bottom: -500px;
		left: 0;
		z-index: -1;
	}
</style>
