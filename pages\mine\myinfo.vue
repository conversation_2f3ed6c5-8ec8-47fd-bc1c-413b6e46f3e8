<template>
	<view>
		<view class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
				<view class="weui-cell__bd">姓名</view>
				<view class="weui-cell__ft weui-cell__ft_in-access" @click="changePrompt('姓名','truename',userdetail.truename)">{{userdetail.truename || '请输入姓名'}}</view>
			</view>

			<view class="weui-cell">
				<view class="weui-cell__bd">民族</view>
				<view class="weui-cell__ft weui-cell__ft_in-access">
					<picker mode="selector" :range="arrnation" :value="nationIndex" @change="chooseNation" style="width:100%">
						{{userdetail.nation || '请选择民族'}}
					</picker>
				</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd">国籍</view>
				<view class="weui-cell__ft weui-cell__ft_in-access">
					<picker mode="selector" :range="arrnationality" :value="nationalityIndex" @change="chooseNationality" style="width:100%">
						{{userdetail.nationality || '请选择国籍'}}
					</picker>
				</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd">证件类型</view>
				<view class="weui-cell__ft weui-cell__ft_in-access">
					<picker mode="selector" :range="arrcredentialstype" :value="idcardtypeIndex" @change="chooseIdcardtype" style="width:100%">
						{{userdetail.idcard_type || '请选择证件类型'}}
					</picker>
				</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd">证件号码</view>
				<view class="weui-cell__ft weui-cell__ft_in-access" @click="changePrompt('证件号码','idcard',userdetail.idcard)">{{userdetail.idcard || '请输入证件号码'}}</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd">户口所在地</view>
				<view class="weui-cell__ft weui-cell__ft_in-access">
					<picker mode="selector" :range="arrlocation" :value="locationIndex" @change="chooseLocation" style="width:100%">
						{{userdetail.localtion || '请选择户口所在地'}}
					</picker>
				</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd">户口类型</view>
				<view class="weui-cell__ft weui-cell__ft_in-access">
					<picker mode="selector" :range="arrlocationtype" :value="locationtypeIndex" @change="chooseLocationtype" style="width:100%">
						{{userdetail.locationtype || '请选择户口类型'}}
					</picker>
				</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd">工作单位</view>
				<view class="weui-cell__ft weui-cell__ft_in-access" @click="changePrompt('工作单位','aunits',userdetail.aunits)">{{userdetail.aunits || '请输入工作单位'}}</view>
			</view>

		</view>
		<!-- 提交按钮 -->
		<view class="button-bottom " style="height: 160rpx;box-shadow: none;border-top:none;">
			<button class="common-btn noafter" @click="logout">退出登录</button>
		</view>
	</view>
</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js")
	var config = require("../../util/config.js")
	var app = getApp();
	
	export default {
		data() {
			return {
				myinfo: app.globalData.objuserinfo,
				userdetail: {},
				arrnation: config.nation,
				arrnationality: config.nationality,
				arrlocationtype: config.locationtype,
				arrlocation: config.location,
				arrcredentialstype: config.credentialstype,
				nationIndex: -1,
				nationalityIndex: -1,
				locationtypeIndex: -1,
				locationIndex: -1,
				idcardtypeIndex: -1
			}
		},
		onLoad(params) {
			this.initData();
		},
		onShow() {
		
		},
		methods: {
			initData(){
				var _this = this;
				uni.sm(function(re,err){
					if(err){
						return uni.msg(err);
					}
					_this.userdetail = re;
					_this.nationIndex = _this.arrnation.indexOf(re.nation);
					_this.nationalityIndex = _this.arrnationality.indexOf(re.nationality);
					_this.locationtypeIndex = _this.arrlocationtype.indexOf(re.locationtype);
					_this.locationIndex = _this.arrlocation.indexOf(re.location);
					_this.idcardtypeIndex = _this.arrcredentialstype.indexOf(re.idcard_type);
				}, ["user.selectdetail", this.myinfo.id], {route: uni.svs.zxx_home_biz})
			},
			chooseNation(e){
				this.nationIndex = e.detail.value;
				this.userdetail.nation = this.arrnation[this.nationIndex];
				var param = {
					nation: this.arrnation[this.nationIndex]
				}
				this.save(param);
			},
			chooseNationality(e){
				this.nationalityIndex = e.detail.value;
				this.userdetail.nationality = this.arrnationality[this.nationalityIndex];
				var param = {
					nationality: this.arrnationality[this.nationalityIndex]
				}
				this.save(param);
			},
			chooseIdcardtype(e){
				this.idcardtypeIndex = e.detail.value;
				this.userdetail.idcard_type = this.arrcredentialstype[this.idcardtypeIndex];
				var param = {
					idcard_type: this.arrcredentialstype[this.idcardtypeIndex]
				}
				this.save(param);
			},
			chooseLocation(e){
				this.locationIndex = e.detail.value;
				this.userdetail.localtion = this.arrlocation[this.locationIndex];
				var param = {
					localtion: this.arrlocation[this.locationIndex]
				}
				this.save(param);
			},
			chooseLocationtype(e){
				this.locationtypeIndex = e.detail.value;
				this.userdetail.locationtype = this.arrlocationtype[this.locationtypeIndex];
				var param = {
					locationtype: this.arrlocationtype[this.locationtypeIndex]
				}
				this.save(param);
			},
			//弹框输入
			changePrompt: function(title, itype, _val) {
				var _this = this;
				uni.prompt(title || "输入", _val, function(res) {
					if (itype == "idcard" && res.content.length > 18) {
						setTimeout(function() {
							uni.alert("长度不能大于18位");
						}, 100);
						return;
						// return _this.changePrompt(title, itype, res.content);
					}
					if (itype == "truename" && res.content.length > 10) {
						setTimeout(function() {
							uni.alert("长度不能大于10位");
						}, 100);
						return;
						// return _this.changePrompt(title, itype, res.content);
					}
					if (itype == "aunits" && res.content.length > 20) {
						setTimeout(function() {
							uni.alert("长度不能大于20位");
						}, 100);
						return;
						// return _this.changePrompt(title, itype, res.content);
					}
					_this.userdetail[itype] = _val = res.content;
					var param = {};
					param[itype] = res.content;
					_this.save(param);
				})
			},
			
			// //保存单项
			// editoneitem: function(itype, value) {
			// 	uni.sm(
			// 		(re, err) => {
			// 			if (!err) {
			// 				uni.msg('保存成功');
			// 			} else {
			// 				uni.alert('保存失败');
			// 			}
			// 		},
			// 		['apphome.homeuser.editoneitembyid', itype, value]
			// 	);
			// },
			save(param){
				uni.sm(function(re, err){
					if(err){
						return uni.msg(err);
					}
					uni.msg('保存成功');
					if(param.truename){//修改的是姓名时，需要同步到后台缓存中
						app.globalData.objuserinfo.truename = param.truename;
						uni.smaction(function(){
							
						}, {key: 'truename', value: param.truename}, {route: uni.svs.zxx_home_biz, action: 'mine/syncinfo'})
					}
				}, ["user.updateuser", JSON.stringify(param), uni.msgwhere({id: [this.myinfo.id]})], {route: uni.svs.zxx_home_biz})
			},
			logout(){
				uni.smaction(function(re, err){
					if(err){
						return uni.msg(err);
					}
					// uni.removeStorageSync('token');
					app.globalData.objuserinfo = {};
					uni.reLaunch({
						url: '/pages/tabBar/home/<USER>'
					})
				}, {}, {route: uni.svs.zxx_home_auth, action: 'exit'})
			}
		}
	}
</script>


<style>
	page {
		background: #fff;
	}


	.weui-cell {
		padding: 15px;
	}

	.weui-cell .weui-cell__bd {
		font-size: 14px;
		color: #303030;
	}

	.weui-cell .weui-cell__ft {
		font-size: 14px;
		color: #999999;
	}

	.weui-cell:before {
		right: 15px;
	}

	.weui-cells:after {
		right: 15px;
		left: 15px;
	}
</style>