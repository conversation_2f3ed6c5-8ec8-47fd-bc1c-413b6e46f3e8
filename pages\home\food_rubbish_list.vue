<template>
	<view>
		<Sticky sticky stickyThreshold="0" offsetTop="0">
			<template #sticky>
				<view class="weui-cell toplinebg font14" style="background: #fff; margin-bottom: 8px;">
					<view class="weui-cell__bd " style="color:#999999">
						<picker mode="date" fields="month" :value="handle_date" @change="choiceHandledate">
							<view class="weui-flexcen">
							<image src="static/image/calendar.png" style="width: 30rpx;height: 31rpx; margin-right: 5px;">
							</image>
							<text class="booktxt" style="color: #000000;">{{ handle_date ? handle_date : '请选择处理日期' }}</text>
							<image src="static/image/downimg.png"
								style="width: 40rpx;height: 40rpx; margin-right: 5px;">
							</image>
							</view>
						</picker>
					</view>
					<view class="weui-cell__ft weui-flex" style="align-items: center;">
						<view>共<text style="margin:0 5px; color: #C23C43;">{{total}}</text>条餐厨垃圾</view>
					</view>
				</view>
			</template>
		</Sticky>
		
		<mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" :height="mescrollHeight"
			@up="loadList" :up="{page: {size: 20}}" :bottombar="false">
			<!-- 没有数据 -->
			<view v-if="arrList.length == 0" style="text-align: center;">
				<view class="weui-flex_center" style="height: 80rpx; margin-top: 20%;">
					<image src="https://osstbfile.tb-n.com/xcxscale/static/image/shiantongjian/oerderno.png"
						style="width: 64rpx;height: 74rpx;display: block;">
					</image>
				</view>
				<text class="photo-oerder">暂无厨余垃圾记录</text>
			</view>
			<view class="marbot10" v-else v-for="(item,i) in arrList" :key="i" @click="toDetail(item.id)">
				<view class="yllist">
					<view>
						<view class="weui-cell" style="border-bottom: 1px solid #EBEBEB;padding: 0px 15px; ">
							<view class="weui-cell__bd">
								<view class="sel-person-list green-left" style="height: 38px;">
									<view class="weui-flex " style="align-items: center;">{{ item.handle_date }}</view>
								</view>
							</view>
							<view class="weui-cell__ft weui-cell__ft_in-access"></view>
						</view>
					</view>
					<view class="weui-flex cleancen" style="margin-left: 30rpx;">
						<view style="position: relative;width:100%;">
							<view class="clean-list"><text>重量：</text><view>{{item.weight}}</view></view>
							<view class="clean-list"><text>交接人：</text><view>{{item.charger}}</view></view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-uni>
	</view>
</template>

<script>
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	var util = require("@/common/util.js")
	var app = getApp();
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				mescrollHeight: 1400,
				handle_date: '',
				total: 0,
				arrList: []
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			var that = this;
			this.getTotal();
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			loadList(page) {
				var _this = this;
				var pageNo = page.num;
				var pageSize = page.size;
				var objwhere = {};
				if (this.handle_date) {
					objwhere.handle_date = [this.handle_date];
				}
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg("系统错误");
					}
					if (pageNo == 1) {
						_this.arrList = [];
					}
					var arrList = _this.arrList;
					arrList = arrList.concat(re);
					_this.$forceUpdate();
					console.log('arrList', arrList);
					var hasNext = true;
					if (re.length < pageSize) {
						hasNext = false;
					}
					_this.arrList = arrList;
					// _this.$refs.mescrollRef.endSuccess(re.length, hasNext);
					_this.mescroll.endSuccess(re.length, hasNext);
				}, ["home.rubbish.list", uni.msgwhere(objwhere), pageSize, pageSize * (pageNo - 1)], { route: uni.svs.zxx })
			},
			getTotal(){
				var _this = this;
				var objwhere = {};
				if (this.handle_date) {
					objwhere.handle_date = [this.handle_date];
				}
				uni.sm(function(re, err, obj) {
					if (err) {
						return uni.msg("系统错误");
					}
					_this.total = re.num;
				}, ["home.rubbish.total", uni.msgwhere(objwhere)], {
					route: uni.svs.zxx
				})
			},
			reloadList() {
				this.mescroll.resetUpScroll();
			},
			choiceHandledate(e) {
				console.log(e);
				this.handle_date = e.detail.value;
				this.getTotal();
				this.reloadList();
			},
			clearDate(){
				this.handle_date = '';
				this.getTotal();
				this.reloadList();
			},
			toDetail(id){
				uni.navigateTo({
					url: "/pages/home/<USER>" + id
				})
			}
		}
	};
</script>

<style>
	page {
		background: #F2F4F5;
	}

	.coffee-col {
		color: #CF9155;
	}

	.cleancen {
		padding: 15px 0rpx;
	}


	.img_def {
		margin: 0px 0rpx 0 30rpx;
		width: 216rpx;
		height: 216rpx;

	}

	.numbertxt {
		font-size: 26rpx;
		color: #827E7B;

	}


	.clean-list {
		color: #666666;
		font-size: 14px;
		line-height: 25px;
	}

.clean-list text {
		color: #999999;
	     width: 140rpx;
	     display:inline-block;
		 float: left;
	
	}
	.clean-list view{width: 70%; float: left;  display:inline-block;}
</style>