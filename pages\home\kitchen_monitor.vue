<template>

	<view>
		<view class="def-search toplinebg">
			<view class="weui-search-bar weui-search-bar_focusing" id="searchBar">
				<image src="static/image/bzn/calendar.png" style="width: 40rpx;height: 40rpx; margin-right: 5px;">
				</image>
				<view class="weui-search-bar__form">
					<view class="weui-search-bar__box weui-flex" style="align-items: center; padding: 0 30rpx">
						<image src="https://osstbfile.tb-n.com/xcxscale/static/image/icon_search.png"
							style="width: 30rpx; height: 30rpx; margin-right: 25rpx"></image>
						<!-- <input placeholder-class="phcolor" class="weui-input weui-flex__item" placeholder="搜索食品名"
							style="font-size: 14px" /> -->
						<picker mode="date" :value="eatdate" @change="choiceEatdate">
							<text class="booktxt" style="color: #000000;">{{ eatdate ? eatdate : '请选择登记日期' }}</text>
						</picker>
						<image v-if="eatdate" @click="clearDate" src="https://osstbfile.tb-n.com/xcxscale/static/image/icon_close.png"
							style="width: 30rpx; height: 30rpx"></image>
					</view>
				</view>
				<text @click="reloadList" style="margin-left: 5px; color: #33B596; font-size: 14px;">搜索</text>
				<text @click="addWaste" style="margin-left: 5px; color: #33B596; font-size: 14px;">新增</text>
			</view>
		</view>
		<view>
			<mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" :height="mescrollHeight"
				@up="loadList" :up="{page: {size: 20}}" :bottombar="false">
				<!-- 没有数据 -->
				<view v-if="arrList.length == 0" style="text-align: center;">
					<view class="weui-flex_center" style="height: 80rpx; margin-top: 20%;">
						<image src="https://osstbfile.tb-n.com/xcxscale/static/image/shiantongjian/oerderno.png"
							style="width: 64rpx;height: 74rpx;display: block;">
						</image>
					</view>
					<text class="photo-oerder">暂无厨余登记</text>
				</view>
				<view v-else v-for="(item,i) in arrList" :key="i" @click="toDatail(item.id)" style="border-bottom:1px solid #EBEBEB;margin-bottom: 5px;">
					<view class="yllist">
						<view class="weui-cell" style="border-bottom: 1px solid #EBEBEB;padding: 0px 15px; margin-bottom: 10px;">
							<view class="weui-cell__bd">
								<view class="sel-person-list green-left" style="height: 25px;line-height: 25px;">
									<view class="weui-flex " style="align-items: center;">登记日期：{{ item.eatdate }}
									</view>
								</view>
							</view>
							<view class="weui-cell__ft weui-cell__ft_in-access"></view>
						</view>
						<view class="yltit">全校餐余量<text>（系统计算）</text></view>
						<view style="display: inline-block;position: absolute;right: 30rpx;">
							<text @click.stop="btnEdit(item.id)" style="color: #1a3bda;margin-right: 20rpx;">编辑</text>
							<text @click.stop="btnDel(i)" style="color: #e40e0e;">删除</text>
						</view>
					</view>
					<view class="ylmain">
						<view class="ylleft">
							<view class="weui-flex_center" style="justify-content:flex-start">
								<image src="static/image/bzn/morning_icobg.png" style="width: 60rpx;height: 60rpx; margin-right: 5px;"></image>
								<view>
									<view>主食：
										<text class="greentxt">{{ item.breakstaple || 0 }}</text>
										<text class="graytxt">kg</text>
									</view>
									<view>副食：
										<text class="redtxt">{{ item.breaknostaple || 0 }}</text>
										<text class="graytxt">kg</text>
									</view>
								</view>
							</view>
						</view>
						<view class="ylleft">
							<view class="weui-flex_center" style="justify-content:flex-start">
								<image src="static/image/bzn/lunch_icobg.png" style="width: 60rpx;height: 60rpx; margin-right: 5px;"></image>
								<view>
									<view>主食：
										<text class="greentxt">{{ item.lunchstaple || 0 }}</text>
										<text class="graytxt">kg</text>
									</view>
									<view>副食：
										<text class="redtxt">{{ item.lunchnostaple || 0 }}</text>
										<text class="graytxt">kg</text>
									</view>
								</view>
							</view>
						</view>
						<view class="ylleft">
							<view class="weui-flex_center" style="justify-content:flex-start">
								<image src="static/image/bzn/dinner_icobg.png" style="width: 60rpx;height: 60rpx; margin-right: 5px;"></image>
								<view>
									<view>主食：
										<text class="greentxt">{{ item.supperstaple || 0 }}</text>
										<text class="graytxt">kg</text>
									</view>
									<view>副食：
										<text class="redtxt">{{ item.suppernostaple || 0 }}</text>
										<text class="graytxt"> kg</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</mescroll-uni>
		</view>
	</view>
</template>

<script>
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	var app = getApp();
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				scaleFontSize: 28,
				userimg: '', //人员头像
				yeyname: '', //学校名称
				fromname: '', //人员姓名
				fromid: '', //人员id
				openid: '', //人员openid
				mescrollHeight: 1400,
				eatdate: '',
				arrList: []
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			var that = this;
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {},
		methods: {
			loadList(page) {
				var _this = this;
				var pageNo = page.num;
				var pageSize = page.size;
				var objwhere = {};
				if (this.eatdate) {
					objwhere.eatdate = [this.eatdate];
				}
				// uni.sm(function(re, err) {
				// 	if (err) {
				// 		return uni.msg("系统错误");
				// 	}
				// 	if (pageNo == 1) {
				// 		_this.arrList = [];
				// 	}
				// 	var arrList = _this.arrList;
				// 	arrList = arrList.concat(re);
				// 	_this.$forceUpdate();
				// 	console.log('arrList', arrList);
				// 	var hasNext = true;
				// 	if (re.length < pageSize) {
				// 		hasNext = false;
				// 	}
				// 	_this.arrList = arrList;
				// 	// _this.$refs.mescrollRef.endSuccess(re.length, hasNext);
				// 	_this.mescroll.endSuccess(re.length, hasNext);
				// }, ["foodwaste.list", uni.msgwhere(objwhere), pageSize, pageSize * (pageNo - 1)])
			},
			reloadList() {
				this.mescroll.resetUpScroll();
			},
			choiceEatdate(e) {
				this.eatdate = e.detail.value;
			},
			clearDate(){
				this.eatdate = '';
				this.reloadList();
			},
			toDatail(id){
				uni.navigateTo({
					url: "/shiangongshi/foodwaste_detail?id=" + id
				})
			},
			btnEdit(id){
				uni.navigateTo({
					url: "/shiangongshi/foodwaste_edit?id=" + id
				})
			},
			addWaste(){
				uni.navigateTo({
					url: "/shiangongshi/foodwaste_edit?id=0"
				})
			},
			btnDel(i){
				var _this = this;
				var arrList = this.arrList;
				var obj = arrList[i];
				uni.confirm("确定删除" + obj.eatdate + "的厨余登记记录吗？", function(){
					uni.sm(function(re, err) {
						if (err) {
							return uni.msg("系统错误");
						}
						arrList.splice(i, 1);
						_this.arrList = arrList;
					}, ["foodwaste.delbyid", obj.id])
				})
				
			}


		}
	};
</script>

<style>
	page {
		background: #F2F4F5;
	}

	.yllist {
		background: #fff;
	}

	.yltit {
		background: #F8F8F8;
		border-radius: 0px 42px 42px 0px;
		display: inline-block;
		font-size: 24rpx;
		color: #303030;
		padding: 5px 10px;
		font-weight: bold;
	}

	.ylmain {
		background: #fff;
		padding: 10px 10px;
		overflow: hidden;
	}

	.yltit text {
		color: #666666;
	}

	.ylleft {
		width: 50%;
		float: left;
		font-size: 14px;
		color: #999999;
		margin: 5px 0;
	}
</style>
