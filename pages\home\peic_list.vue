<template>
	<view>

		<block v-if="peicrecordArr.length > 0">
			<view v-for="(item, index) in peicrecordArr" :key="index" class="marbot10">
				<view @click="toPeicDetail(index,item)" class="yllist">
					<view>
						<view class="weui-cell" style="border-bottom: 1px solid #EBEBEB;padding: 0px 15px; ">
							<view class="weui-cell__bd">
								<view class="sel-person-list green-left" style="height: 38px;">
									<view class="weui-flex " style="align-items: center;">{{ item.rdate }}</view>
								</view>

							</view>
							<view class="weui-cell__ft weui-cell__ft_in-access"></view>
						</view>
					</view>
					<view class="weui-flex cleancen">
						<view style="margin-right: 30rpx;">
							<image :src="ossPrefix+firstImg(item.pc_imgs)" class="img_def"></image>
						</view>
						<view style="position: relative;width: 100%;">
							<view class="clean-list"><text>餐次：</text>{{canbieObj[item.canbie]}}</view>
							<view class="clean-list"><text>陪餐人员：</text>
							<block v-if="item.pc_userinfo" v-for="(pcuser, index2) in toJSONArr(item.pc_userinfo)"
								:key="index2">
								<view>{{pcuser.departname}}-{{pcuser.zwname}}-{{ pcuser.empname }}</view>
							</block>
							</view> 
						</view>
					</view>
				</view>
			</view>
		</block>
		<block v-else>
			<view style="text-align: center;">
				暂无数据哦~
			</view>
		</block> 
	</view>





</template>


<script>
var app = getApp();
var moment = require("../../common/moment.min.js");


export default {
	data() {
		return {
			ossPrefix: app.globalData.ossPrefix,
			canbieObj: {
				"1": "早餐",
				"2": "加餐",
				"3": "午餐",
				"4": "午点",
				"5": "晚餐",
				"6": "晚点"
			},
			peicrecordArr: [],
			toJSONArr: function (jsonstr) {
				return !jsonstr ? [] : JSON.parse(jsonstr);
			},
			firstImg:function (imgstr){
				return !imgstr?"":imgstr.split(",")[0];
			},


		}
	},
	onLoad() {
		this.initData();
	},
	methods: {
		async initData() {
			// 判断该学生是否在校陪餐
			var haspeic = await this.checkPeicStu();
			if (!haspeic) {
				return uni.msg('该学生不在校就餐');
			}
			var _this = this;
			var rdate = moment().format('YYYY-MM-DD');
			uni.sm(function (re, err) { 
				if (err) {
					return uni.msg('查询陪餐记录失败' + err);
				}
				_this.peicrecordArr = re || [];


			}, ["xcx.home.peic.getdata", uni.msgwhere({ rdate: [rdate, rdate],isopen: [1] })], { route: uni.svs.zxx })
		},
		toPeicDetail(index,peiObj) { 
			uni.navigateTo({
				url: "/pages/home/<USER>" + index
			})
		},
		async checkPeicStu() {
			var res = await uni.smsync(["xcx.home.peic.haspeic", uni.msgwhere({ stuno: [app.globalData.objuserinfo.stuno] })], { route: uni.svs.zxx });
			console.log(res);
			return res.re ? res.re[0].ispeic : '';
		}



	}
}
</script>

<style>
page {
	background: #F2F4F7;
}

.coffee-col {
	color: #CF9155;
}

.cleancen {
	padding: 15px 0rpx;
}

.img_def {
	margin: 0px 0rpx 0 30rpx;
	width: 216rpx;
	height: 216rpx;

}

.numbertxt {
	font-size: 26rpx;
	color: #827E7B;

}

.clean-list text {
	color: #999999;
}

.clean-list {
	color: #666666;
	font-size: 14px;
	line-height: 25px;

}
</style>
