<script>
	import {
		aesEncrypt
	} from "@/common/ase.js"
	//app.js
	var global = require('./globaldata');
	var util = require("./common/util.js")
	var smutil = require('./util/smutil.js');
	var _this;

	export default {
		data() {
			return {};
		},
		onLaunch: async function() {
			_this = this;
			this.checkUpdate();
			// 拿到globaldata的数据
			for (const key in global) {
				if (Object.hasOwnProperty.call(global, key)) {
					this.globalData[key] = global[key];
				}
			}

			// 注册方法
			uni.sm = this.globalData.sm;
			uni.smsync = this.globalData.smsync;
			uni.smaction = this.globalData.smaction;
			uni.alert = this.globalData.alert;
			uni.msg = this.globalData.msg;
			uni.confirm = this.globalData.confirm;
			uni.prompt = this.globalData.prompt;
			uni.getPrePage = this.globalData.getPrePage;
			uni.msgwhere = this.globalData.msgwhere;
			uni.msgArrwhere = this.globalData.msgArrwhere;
			uni.msgpJoin = this.globalData.msgpJoin;
			uni.svs = this.globalData.services;

			var mobile = uni.getStorageSync('curmobile') || "";
			var openid = uni.getStorageSync('curopenid') || "";
			var token = uni.getStorageSync('token') || "";
			console.log("mobile:" + mobile);
			console.log("openid:" + openid);
			console.log("token:" + token);
			_this.globalData.loginByToken(token, function(){
				// 微信小程序
				// #ifdef MP-WEIXIN  
				uni.login({
					success: async (res) => {
						console.log(JSON.stringify(res));
						// e.detail.code = res.code;
						var param = {
							zhtype: _this.globalData.xcxtype, 
							logintype: "loginBycode",
							logincode: res.code
						}
						_this.globalData.applogin(param);
					}
				});
				//#endif 
				//#ifndef MP-WEIXIN
				var param = {
					zhtype: _this.globalData.xcxtype, 
					logintype: "loginByopenid",
					openid: openid
				}
				_this.globalData.applogin(param);
				//#endif 
			})
			//获取系统信息
			this.globalData.sysinfo = uni.getSystemInfoSync();
			//#ifdef MP-WEIXIN
			let menuButtonObject = uni.getMenuButtonBoundingClientRect();
			let statusBarHeight = this.globalData.sysinfo.statusBarHeight;
			let navTop = menuButtonObject.top; //胶囊按钮与顶部的距离
			let navHeight = statusBarHeight + menuButtonObject.height + (menuButtonObject.top - statusBarHeight) *
				2; //导航高度
			this.globalData.navHeight = navHeight;
			this.globalData.navTop = navTop;
			this.globalData.windowHeight = this.globalData.sysinfo.windowHeight;
			// #endif 
		},
		globalData: {
			waitlogin: true,
			mapkey: 'HUSBZ-552W6-PBHST-EDDJQ-YWOZJ-43FAI',
			qqmapkey: 'EEDBZ-2WLWW-6TBRY-O4JJM-RDA7T-KMB3Q',
			code: '',
			headers: {
				// 'content-type': 'application/json'
			},
			services:{
				zxx_home_auth:'zxx-home-auth',
				zxx_home_biz:'zxx-home-biz',
				zxx:'zxx'
			},
			loginByToken(token, cb){
				if(token){
					_this.globalData.headers['Authorization'] = 'Bearer ' + token;
					var param = {
						zhtype: _this.globalData.xcxtype, 
						logintype: "autologin"
					}
					_this.globalData.applogin(param, null, cb);
				} else {
					cb && cb();
				}
			},

			getUserInfoBack: function(obj) {
				if (obj && obj.sid) {
					uni.setStorageSync('curmobile', obj.objuser.mobile);
					uni.setStorageSync('token', obj.token);
					if(obj.openid || obj.objuser.openid_xcx){
						uni.setStorageSync('curopenid', obj.openid || obj.objuser.openid_xcx);
					}
					//#ifdef MP-WEIXIN
					_this.globalData.headers['Authorization'] = 'Bearer ' + obj.token;
					_this.globalData.headers['Cookie'] = 'JSESSIONID=' + obj.sid;
					// #endif
					_this.globalData.objuserinfo = obj.objuser;
					_this.globalData.code = obj.code;
					console.log("objuserinfo", _this.globalData.objuserinfo);
					_this.globalData.waitlogin = false;
					if (_this.globalData.userInfoReadyCallback) {
						_this.globalData.userInfoReadyCallback();
						_this.globalData.userInfoReadyCallback = null;
					}
				} else {
					//_this.alert("登录失败");
				}
			},
			//获取家长当前绑定的学生
			getCurBindStu(cb){
				var userid = _this.globalData.objuserinfo.id;
				this.sm(function(re, err){
					if(err){
						return uni.msg(err);
					}
					_this.globalData.objuserinfo.curstu = re;
					cb && cb();
				}, ["stu.selectcurbingstu", userid], {route: uni.svs.zxx_home_biz})
			},
			// 统一 登录流程
			applogin: function(param, succb, errcb) {
				var pobj = {
					route: uni.svs.zxx_home_auth,
					action: 'login',
					method: 'POST',
					datastring: true
				}
				this.smaction((re, err, obj) => {
					uni.hideLoading();
					if (err) {
						//#ifdef MP-WEIXIN
						if(obj.sid){
							_this.globalData.headers['Cookie'] = 'JSESSIONID=' + obj.sid;
						}
						// #endif
						if(param.logintype == "autologin" && errcb){//通过token登录失败，会再以其他方式再登录一次
							errcb();
						} else {
							console.log('未登录:', obj);
							if(getCurrentPages()[0].route != 'pages/login/login'){
								setTimeout(function(){
									uni.reLaunch({
										url: "/pages/tabBar/home/<USER>"
									})
								}, 1000)
							}
						}
					} else {
						_this.globalData.getUserInfoBack(re);
						succb && succb();
					}
				}, param, pobj)
				// }, ['apphome.applogin', _this.globalData.xcxtype, logintype, key, pwd || "", yzm || "", qrcodeuserid || "", qrcodegiftcode || ""]);
			},
			geturl(route, action){
				if(_this.globalData.selfsevice && _this.globalData.selfsevice["/" + route]){
					return _this.globalData.selfsevice["/" + route].target + '/' + action;
				} else {			
					return _this.globalData.requrl + '/' + route + '/' + action;
				}
			},
			/**
			 * 请求controller的方法
			 * @param {Object} cb 调用结束后的回调
			 * @param {Object} param 请求的参数 json对象格式
			 * @param {Object} pobj 必填，包含route(路由，既微服务名称)、action(controller路径)，其他key自定。 例如：{route:'bzn-xcx-tbpt', action: 'yey/list'}
			 */
			smaction: function(cb, param, pobj){
				if (typeof cb == 'object') {
					var arrtemp = cb;
					cb = param;
					param = arrtemp;
				}
				if(!pobj || !pobj.route || !pobj.action){
					return cb(null, '路由不能为空');
				}
				var token = uni.getStorageSync('token');
				if(token){
					param.Authorization = 'Bearer ' + token;
				}
				var url = '/' + pobj.route + '/' + pobj.action;
				uni.request({
					// #ifdef H5
					url:  url,
					// #endif
					// #ifndef H5
					url: _this.globalData.geturl(pobj.route, pobj.action),
					// #endif				
					//请求方式
					method: pobj.method || "GET",
					//请求数据
					data: pobj.datastring ? JSON.stringify(param) : param,
					// #ifndef H5
					header: _this.globalData.headers,
					// #endif
					//处理session问题
					success: function(res) {
						console.log(res);
						if (res.data) {
							if(res.data.code){
								var result = res.data && res.data.data ? JSON.parse(res.data.data) : {};
								if (res.data.code == 200) {
									return cb(result || res.data.msg, null, result || res.data.msg);
								} else {
									return cb(null, res.data.msg, result);
								}
							}
							var strtype = Object.prototype.toString.apply(res.data.data);
			
							if (strtype == '[object Array]') {
								cb(res.data);
							} else if (strtype == '[object Object]') {
								if (res.data.error || res.data.code == 500) {
									console.log('请求数据', param);
									console.log('请求服务', pobj);
									console.log(res.data);
									if (res.data.error == "nologin") {
										return uni.navigateTo({
											url: "/pages/login/login"
										})
									}
			
									if (typeof res.data == 'object') {
										if (res.data.mess) {}
									}
			
									cb(res.data, res.data.error || res.data.msg, res.data);
								} else {
									cb(res.data.re || res.data, res.data.error, res.data);
								}
							} else {
								cb(null, res.data.error || res.data.msg, res.data);
							}
						} else {
							cb(null, '请求失败');
						}
					},
					fail: function(res) {
						cb(null, '请求失败');
					}
				});
			},
			sm: function(cb, arr, pobj) {
				if (typeof cb == 'object') {
					var arrtemp = cb;
					cb = arr;
					arr = arrtemp;
				}
				// var objp = smutil.getSmParam(arr, pobj);
							
				var strp = '';
				var ismul = 0;
				var msgid = '';
				if (typeof arr[0] == 'object') {
					ismul = 1;
					var arr2 = [];
				
					for (var i = 0; i < arr.length; i++) {
						for (var j = 0; j < arr[i].length; j++) {
							if (typeof arr[i][j] == 'object') {
								arr[i][j] = JSON.stringify(arr[i][j]);
							}
						}
						arr2.push(smutil.encodeArr(arr[i]).join('%15'));
					}
				
					strp = arr2.join('%18');
				} else {
					for (var j = 0; j < arr.length; j++) {
						if (typeof arr[j] == 'object') {
							arr[j] = JSON.stringify(arr[j]);
						}
					}
					msgid = arr[0];
					strp = smutil.encodeArr(arr).join('%15');
				}

				var data = {
					arr: strp,
					lan: 'zh'
				};

				if (ismul) {
					data.ismul = ismul;
				}

				data.msgid = (pobj && pobj.msgid) || msgid || ''

				if (pobj && pobj.trans) {
					data.trans = pobj.trans;
				}

				if (pobj && pobj.rpc) {
					//远程调用
					data.rpc = pobj.rpc;
				}
				
				var token = uni.getStorageSync('token');
				if(token){
					data.Authorization = 'Bearer ' + token;
				}
				var url = '/Enter';
				if (pobj && pobj.route) {
					url = '/' + pobj.route + url;
				} else {
					cb(null, '路由不能为空');
					return;
				}
				uni.request({
					// #ifdef H5
					url:  url,
					// #endif
					// #ifndef H5
					url: _this.globalData.geturl(pobj.route, "Enter"),
					// #endif				
					//请求方式
					// method: "POST",
					//请求数据
					data: data,
					// #ifndef H5
					header: _this.globalData.headers,
					// #endif
					//处理session问题
					success: function(res) {
						console.log(res)
						if (res.data) {
							if(res.data.code){
								if(res.data.code == 200){
									var result = JSON.parse(res.data.data);
									return cb(result, null, result);
								} else {
									return cb(null, res.data);
								}
							}
							var strtype = Object.prototype.toString.apply(res.data);

							if (strtype == '[object Array]') {
								cb(res.data);
							} else if (strtype == '[object Object]') {
								if (res.data.error || res.data.code == 500) {
									console.log(strp);
									console.log(res.data);
									if (res.data.error == "nologin") {
										return uni.navigateTo({
											url: "/pages/login/login"
										})
									}

									if (typeof res.data == 'object') {
										if (res.data.mess) {}
									}

									cb(res.data, res.data.error || res.data.msg, res.data);
								} else {
									cb(res.data.re || res.data, res.data.error, res.data);
								}
							} else {
								cb(null, res.data.error || res.data.msg, res.data);
							}
						} else {
							cb(null, '请求失败');
						}
					},
					fail: function(res) {
						cb(null, '请求失败');
					}
				});
			},

			//同步请求方式
			smsync: function(arr, pobj) {
				if (typeof cb == 'object') {
					var arrtemp = cb;
					cb = arr;
					arr = arrtemp;
				} 
							
				var strp = '';
				var ismul = 0;
				var msgid = '';
				if (typeof arr[0] == 'object') {
					ismul = 1;
					var arr2 = [];
				
					for (var i = 0; i < arr.length; i++) {
						for (var j = 0; j < arr[i].length; j++) {
							if (typeof arr[i][j] == 'object') {
								arr[i][j] = JSON.stringify(arr[i][j]);
							}
						}
						arr2.push(smutil.encodeArr(arr[i]).join('%15'));
					}
				
					strp = arr2.join('%18');
				} else {
					for (var j = 0; j < arr.length; j++) {
						if (typeof arr[j] == 'object') {
							arr[j] = JSON.stringify(arr[j]);
						}
					}
					msgid = arr[0];
					strp = smutil.encodeArr(arr).join('%15');
				}
				
				var data = {
					arr: strp,
					lan: 'zh'
				};
				
				if (ismul) {
					data.ismul = ismul;
				}
				
				data.msgid = (pobj && pobj.msgid) || msgid || ''
				
				if (pobj && pobj.trans) {
					data.trans = pobj.trans;
				}
				
				if (pobj && pobj.rpc) {
					//远程调用
					data.rpc = pobj.rpc;
				}
				
				var token = uni.getStorageSync('token');
				if(token){
					data.Authorization = 'Bearer ' + token;
				}
				var url = '/Enter';
				
				return new Promise(function(resolve, reject) {
					if (pobj && pobj.route) {
						url = '/' + pobj.route + url;
					} else { 
						reject({
							re: null,
							err: '路由不能为空',
							obj: null
						});
						return;
					}
					
					uni.request({
						// #ifdef H5
						url:  url,
						// #endif
						// #ifndef H5
						url: _this.globalData.geturl(pobj.route, "Enter"),
						// #endif				
						//请求方式
						// method: "POST",
						//请求数据
						data: data,
						// #ifndef H5
						header: _this.globalData.headers,
						// #endif
						//处理session问题
						success: function(res) {
							if (res.data) {
								if(res.data.code){
									if(res.data.code == 200){
										var result = JSON.parse(res.data.data); 
										resolve({
											re: result,
											err: null,
											obj: res.data
										});
									} else {
										reject({
											re: null,
											err: res.data.msg,
											obj: res.data
										});
									}
									return;
								}
								var strtype = Object.prototype.toString.apply(res.data);

								if (strtype == '[object Array]') {
									resolve({
										re: res.data
									});
								} else if (strtype == '[object Object]') {
									if (res.data.error || res.data.code == 500) {
										console.error(strp);
										console.error(res.data);
										uni.showToast({
											title: res.data.error,
											icon: 'none',
											duration: 2000
										});
										reject({
											re: null,
											err: res.data.error || res.data.msg,
											obj: res.data
										});
									} else {
										resolve({
											re: res.data.re || res.data,
											err: res.data.error || res.data.msg,
											obj: res.data
										});
									}
								} else {
									resolve({
										re: null,
										err: null,
										obj: res.data
									});
								}
							} else {
								uni.showToast({
									title: '请求失败',
									icon: 'none',
									duration: 2000
								});
								reject({
									re: null,
									err: '请求失败'
								});
							}
						},
						fail: function() {
							uni.showToast({
								title: '请求失败',
								icon: 'none',
								duration: 2000
							});
							reject({
								re: null,
								err: '请求失败'
							});
						}
					});
				});
			},

			confirm: function(p1, p2, p3) {
				if (p3) {
					this.alert(p1, p2, p3, true);
				} else {
					this.alert('提示', p1, p2, true, false);
				}
			},

			prompt: function(p1, p2, p3) {
				if (p3) {
					this.alert(p1, p2, p3, true, true);
				} else {
					this.alert('提示', p1, p2, true, true);
				}
			},

			alert: function(p1, p2, p3, showCancel, editable) {
				var title = '提示';
				var content = '';
				var cb = null;

				if (!p1) {
					return;
				}
				if (!p2 && p2 != "") {
					//只有一个参数 那就是content
					content = p1;
				} else {
					if (!p3) {
						//只有两个参数
						if (typeof p2 == 'string') {
							//p2 是string 则 p1 是title p2 是content
							title = p1;
							content = p2;
						} else {
							//p2 是function 则 p1 是content p2 是callback
							content = p1;
							cb = p2;
						}
					} else {
						//三个参数则 p1 是title p2 是content p3是callback
						title = p1;
						content = p2;
						cb = p3;
					}
				}

				uni.showModal({
					title: title,
					content: content + '',
					showCancel: showCancel || false,
					editable: editable || false,
					success: function(res) {
						if (res.confirm) {
							if (cb) {
								cb(res);
							}
						}
					}
				});
			},

			msg: function(title) {
				uni.showToast({
					title: title + '',
					icon: 'none',
					style: "z-index: 99999;",
					duration: 2000
				});
			},
			//获取 前页面
			getPrePage: function(n = -1) {
				n = n < -1 ? n : -1;
				var pages = getCurrentPages(); // 页面栈
				var beforePage = pages[pages.length - (-(n - 1))]; // 前n个页面
				// #ifndef H5
				beforePage = beforePage.$vm;
				// #endif
				return beforePage;
			},
			msgwhere: function(obj1, obj2) {
				var obj = {};
				if (obj1) {
					obj = Object.assign(obj, obj1);
				}
				if (obj2) {
					obj = Object.assign(obj, obj2);
				}
				return JSON.stringify({
					msg_where: obj
				})
			},
			msgArrwhere: function(arr) {
				return JSON.stringify({
					msg_where: arr
				})
			},
			msgpJoin: function(arr) {
				var arrnew = [];
				for (var i = 0; i < arr.length; i++) {
					arrnew.push([arr[i]]);
				}
				return arrnew;
			},


		},
		methods: {
			checkLogin: function() {
				console.log("------------:" + _this.globalData.code)
				return _this.globalData.code === 0;
			},
			checkUpdate() {
				// #ifdef MP-WEIXIN
				const updateManager = uni.getUpdateManager();

				updateManager.onCheckForUpdate(function(res) {
					// 请求完新版本信息的回调
					console.log("是否有更新:", res.hasUpdate);
				});

				updateManager.onUpdateReady(function(res) {
					uni.showModal({
						title: '更新提示',
						content: '新版本已经准备好，是否重启应用？',
						success(res) {
							if (res.confirm) {
								// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
								updateManager.applyUpdate();
							}
						}
					});

				});

				updateManager.onUpdateFailed(function(res) {
					// 新的版本下载失败
					console.error('新的版本下载失败', res)
				});
				// #endif 
			}
		}
	};
</script>
<style lang="scss">
	/*每个页面公共css */
	@import '@/uni_modules/uni-scss/index.scss';
	/* #ifndef APP-NVUE */
	@import '@/static/customicons.css';
	@import '@/static/iconfont.css';
	@import '@/static/weui.css';


	@import '@/common/fui-app.css';
	@import '@/components/firstui/fui-theme/fui-theme.css';

	// 设置整个项目的背景色
	page {
		background-color: #f5f5f5;
	}

	/* #endif */
	.example-info {
		font-size: 14px;
		color: #333;
		padding: 10px;
	}
</style>