/**
 * 上传到服务器
 * @param {Object} config {arrfile,serverurl,cb}
 */
function uploadServer(config) {
	var res = [];
	uploadOneFile(config, config.arrfile, 0, res);
}

function uploadOneFile(config, arrfile, index, res) {
	if (index == arrfile.length) {
		return config.complete && config.complete(res);
	}
	uni.uploadFile({
		url: config.serverurl,
		filePath: arrfile[index].path,
		name: "file",
		success: function(re) {
			var resultdata = JSON.parse(re.data);
			res.push(resultdata);
			config.onecomplete && config.onecomplete(index);
			index++;
			uploadOneFile(config, arrfile, index, res);
		},
		fail: function(re, d, e, r) {
			uni.msg(arrfile[index].name + "上传失败");
			config.onecomplete && config.onecomplete(index);
			index++;
			uploadOneFile(config, arrfile, index, res);
		}
	})
}

module.exports = {
	uploadServer: uploadServer,
}