var phstandard = {};
var medicalstandard = {};

function countvalue(typenum, value, arritemval) {
  if (typenum == 1) {
    if (!isNaN(arritemval[0]) && value >= arritemval[0]) {
      return 0;
    } else if (value >= arritemval[1]) {
      return 1;
    } else if (value >= arritemval[2]) {
      return 2;
    } else if (value >= arritemval[3]) {
      return 3;
    } else if (value >= arritemval[4]) {
      return 4;
    } else {
      return 5;
    }
  } else if (typenum == 2) {
    if (!isNaN(arritemval[0]) && value <= arritemval[0]) {
      return 0;
    } else if (value <= arritemval[1]) {
      return 1;
    } else if (value <= arritemval[2]) {
      return 2;
    } else if (value <= arritemval[3]) {
      return 3;
    } else if (value <= arritemval[4]) {
      return 4;
    } else {
      return 5;
    }
  } else if (typenum == 3) { //不取等
    if (!isNaN(arritemval[0]) && value < arritemval[0]) {
      return 0;
    } else if (value < arritemval[1]) {
      return 1;
    } else if (value < arritemval[2]) {
      return 2;
    } else if (value < arritemval[3]) {
      return 3;
    } else if (value < arritemval[4]) {
      return 4;
    } else {
      return 5;
    }
  }
};

module.exports = {
  initData: function() {
    var _this = this;
    uni.sm(function(re, err) {
      if (re) {
        _this.phstandard = re;
      }
    }, ["apphome.bznReadjson", "phstandard"])
    uni.sm(function(re, err) {
      if (re) {
        _this.medicalstandard = re;
      }
    }, ["apphome.bznReadjson", "medicalstandard"])
  },
  phstandard: phstandard, //体测对应范围
  medicalstandard: medicalstandard, //各标准对应范围
  getbmi: function(height, weight) {
    return height == 0 ? 0 : parseInt((weight / ((height / 100) * (height / 100))) * 1000) / 1000;
  },
  strnor: "2", //使用几分法   1为三分法  2为五分法   3为六分法
  GetHW: function(hw, arr) {
    if (!hw || hw == "0") {
      return '';
    }
    if (hw < arr[1]) {
      return '下';
    } else if (this.strnor != "1" && hw < arr[2]) {
      return '中下';
    } else if (this.strnor == "3" && hw < arr[6]) {
      return "中-";
    } else if (hw >= arr[4]) {
      return '上';
    } else if (this.strnor != "1" && hw >= arr[3]) {
      return '中上';
    } else
      return this.strnor == "3" ? "中+" : "中";
  },
  GetFat: function(hwlesd, w) {
    var zhd = (parseFloat(hwlesd) * 1.5).toFixed(2);
    var zd = (parseFloat(hwlesd) * 1.3).toFixed(2);
    var qd = (parseFloat(hwlesd) * 1.2).toFixed(2);
    var cz = (parseFloat(hwlesd) * 1.1).toFixed(2);
    if (parseFloat(w) >= zhd)
      return "重度";
    else if (parseFloat(w) < zhd && parseFloat(w) >= zd)
      return "中度";
    else if (parseFloat(w) < zd && parseFloat(w) >= qd)
      return "轻度";
    else if (parseFloat(w) < qd && parseFloat(w) >= cz)
      return "超重";
    else
      return "正常";
  },
  GetThin:function(agehe, agewe, hewe) {
    var strMalnutrition = "正常";
    if (!agehe && !agewe) {
      strMalnutrition = "";
    }
    if (agewe == "下") {//年龄别体重= "下"
      strMalnutrition = "低体重";
    }
    if (agehe == "下" || hewe == "下") {//年龄别身高= "下" || 身高别体重= "下" 
      //        if (agehe == "下" && hewe == "下") {//年龄别身高= "下" && 身高别体重= "下" 
      //            strMalnutrition = "严重慢性营养不良";
      //        } else 
      if (agehe == "下") {//年龄别身高= "下"
        strMalnutrition = "生长迟缓";
      } else if (hewe == "下") {//身高别体重= "下" 
        strMalnutrition = "消瘦";
      }
    }
    return strMalnutrition;
  },
  getfatmal: function(height, weight, standardtype, age, sex, hest) { //获取肥胖和营养不良 hest卧立位 1 卧位 2 立位 默认为2
    var objre = {};
    var arrawhl = this.medicalstandard['agehw'][standardtype]["a_" + age + "_" + sex];
    var strah = this.GetHW(height, [arrawhl[0], arrawhl[1], arrawhl[2], arrawhl[4], arrawhl[5], arrawhl[6], arrawhl[3]]); //年龄别身高
    var straw = this.GetHW(weight, [arrawhl[7], arrawhl[8], arrawhl[9], arrawhl[11], arrawhl[12], arrawhl[13], arrawhl[10]]); //年龄别体重
    var arrhwle = this.medicalstandard['hwle'][standardtype]["a_" + sex + "_" + height.toFixed(1) + "_" + hest];
    if (sex == "男" && height > 143.9) {
      arrhwle = this.medicalstandard['hwle'][standardtype]["a_" + sex + "_143.9_" + hest];
    } else if (sex == "女" && height > 142) {
      arrhwle = this.medicalstandard['hwle'][standardtype]["a_" + sex + "_142.0_" + hest];
    }
    if (arrhwle) {
      var strhw = this.GetHW(weight, [arrhwle[0], arrhwle[1], arrhwle[2], arrhwle[4], arrhwle[5], arrhwle[6], arrhwle[3]]); //身高别体重
      objre.strfatleavel = this.GetFat(arrhwle[3], weight);
      if (strah != "" && straw != "") {
        objre.malleavel = this.GetThin(strah, straw, strhw);
      }
      if (strhw == "上") {
        objre.str = "肥胖";
      } else if (strhw == "中上") {
        objre.str = "超重";
      } else {
        objre.str = "正常";
        if (objre.malleavel && objre.malleavel !="正常"){
          objre.str = "营养不良";
        }
      }
      return objre;
    } else {
      return {
        str:""
      };
    }
  },
  getscore: function(arrsport, objdata, objinfo) {
    var phstandard = this.phstandard;
    for (var i = 0; i < arrsport.length; i++) {
      var key = arrsport[i].key;
      var value = objdata[key];
      var typenum = arrsport[i].typenum;
      var arritemval = phstandard.phitem["a_" + objinfo.sex + "_" + objinfo.age + "_" + key];
      var score = 1;
      if (arritemval){
        score = countvalue(typenum, value, arritemval);
      }
      arrsport[i].numscore = score;
    }
    return arrsport;
  },
  getheightscore: function(value, objinfo) { //身高/年龄
    var phstandard = this.phstandard;
    var arritemval = phstandard.phitem["a_" + objinfo.sex + "_" + objinfo.age + "_height"];
    if (!arritemval){
      return 1;
    }
    var score = countvalue(3, value, arritemval);
    return score;
  },
  getweightscore: function(height, value, objinfo) { //身高/体重
    var phstandard = this.phstandard;
    if ((height + "").indexOf(".") < 0) {
      height = height + ".0";
    }
    var arritemval = phstandard.phheight["a_" + objinfo.sex + "_" + height];
    var numscore = "";
    var strscore = "";
    if (!arritemval || value >= arritemval[0]) { //肥胖
      numscore = 1;
      strscore = "+1";
    } else if (value >= arritemval[1]) { //偏上
      numscore = 3;
      strscore = "+3";
    } else if (value >= arritemval[2]) { //正常
      numscore = 5;
      strscore = "5";
    } else if (value >= arritemval[3]) { //偏下
      numscore = 3;
      strscore = "-3";
    } else { //消瘦
      numscore = 1;
      strscore = "-1";
    }
    return {
      score: numscore,
      status: strscore
    };
  },
  randomNum: function (minNum, maxNum) {
    switch (arguments.length) {
      case 1:
        return parseInt(Math.random() * minNum + 1);
        break;
      case 2:
        return parseInt(Math.random() * (maxNum - minNum + 1) + minNum);
        break;
      default:
        return 0;
        break;
    }
  },
  getBabyPaiming: function(score) {
    var paiming = 0;
    if (score == 100) {
      paiming = 99;
    } else if (score >= 95) {
      paiming = 98;
    } else if (score >= 90) {
      paiming = 96;
    } else if (score >= 85) {
      paiming = 92;
    } else if (score >= 80) {
      paiming = 87;
    } else if (score >= 75) {
      paiming = 81;
    } else if (score >= 70) {
      paiming = 75;
    } else if (score >= 65) {
      paiming = 68;
    } else if (score >= 60) {
      paiming = 61;
    } else if (score >= 55) {
      paiming = 53;
    } else if (score >= 50) {
      paiming = 46;
    } else if (score >= 45) {
      paiming = 39;
    } else if (score >= 40) {
      paiming = 33;
    } else if (score >= 35) {
      paiming = 27;
    } else if (score >= 30) {
      paiming = 22;
    } else if (score >= 25) {
      paiming = 17;
    } else if (score >= 20) {
      paiming = 12;
    } else if (score >= 15) {
      paiming = 9;
    } else if (score >= 10) {
      paiming = 6;
    } else if (score >= 5) {
      paiming = 3;
    } else if (score > 0) {
      paiming = 1;
    } else if (score == 0) {
      paiming = 1;
    }
    return paiming == 0 ? 0 : paiming + "." + this.randomNum(1, 9);
  },
  getStatusBySocre: function(score) {
    var status = 0;
    if (score == 5) {
      status = "4"; //优秀
    } else if (score == 4) {
      status = "3"; //良好
    } else if (score == 3) {
      status = "2"; //及格
    } else {
      status = "1"; //较弱
    }
    return status;
  }
};