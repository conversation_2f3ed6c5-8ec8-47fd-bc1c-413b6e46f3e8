<template>
	<view>
		<view style="margin-bottom: 10px;">
			<view class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
					<view class="weui-cell__bd font14">{{supplierObj.sname}}</view>
				</view>
			</view>
			<view style="padding: 0px 15px; ">

				<view class="sel-person-list green-left" style="height: 38px;">
					<view class="weui-flex font14" style="align-items: center;color: #C23C43;">资质证照</view>
				</view>
			</view>
			<view style="font-size: 28rpx;
 padding: 0px 0px 0 15px;">营业执照
				<text style="padding-left: 40rpx;">证件号：{{supplierObj.licenseno}}</text>

			</view>
			<view class="photoimg">
				<image :src="ossPrefix + '/' + supplierObj.licensepic" style="width:100%; height: 342rpx;"></image>
			</view>
			<view style="font-size: 28rpx;
			padding: 0px 0px 0 15px;">食品流通许可证
							<text style="padding-left: 40rpx;">证件号：{{supplierObj.licenseno4}}</text>
			
						</view>
						<view class="photoimg">
							<image :src="ossPrefix + '/' + supplierObj.licensepic4" style="width:100%; height: 342rpx;"></image>
						</view>
		</view>
	</view>
</template>
<script>
var app = getApp();
	export default {
		data() {
			return {
				ossPrefix: app.globalData.ossPrefix,
				supplierObj:{},
			}
		},
		onLoad(options) {
			this.c_index = options.c_index;
			this.initData();
		},
		methods: {
			initData() {

				var prePage = uni.getPrePage();
				this.supplierObj =prePage.supplierArr[this.c_index];
				console.log(this.supplierObj);
				 
			}
		}
	}
</script>

<style>
	page {
		background: #fff;
	}



	.newsrttxt {
		font-size: 24rpx;
		color: #999999;
	}

	.photoimg {
		padding: 20rpx 30rpx;
	}


	.weui-cells:after,
	.weui-cells:before {
		left: 0px;
		right: 0px;
	}

	.weui-cells:before {
		border-top: none;
	}
</style>
