<template>

	<view>
		<view style="display: none;">
			<view class="module-top">
				<view class="weui-cell weui-cell_access index-top-tit">
					<view class="weui-cell__hd" style="position: relative; margin-right: 10px;" id="btnchangebaby">
						<image src="/static/image/bzn/img_default.png"
							style="width:110rpx;height: 110rpx;  border-radius: 50%;">

					</view>
					<view class="weui-cell__bd">
						<view style="font-size: 15px;" id="babyname">王多鱼</view>
						<view style="font-size: 12px;" id="babyage">4岁8个月</view>
					</view>

					<view class="weui-cell__ft">
						<view class="askleave-num weui-flex_center">

							<view>累计请假 0 天</view>
						</view>
					</view>

					<view>




					</view>
				</view>

				<view class="weui-flex " style="align-items: center;justify-content: center; background: #fff;">
					<view class="tab-common10 weui-flex" style="width: 100%;">
						<view class="current" style="flex: 1;"> 请假单</view>
						<view style="flex: 1;">请假历史</view>


					</view>
				</view>

			</view>

			<view class="remarks-main">
				<view class="remarksview">
					<view class="weui-cells askle-opr">
						<view class="weui-flex">
							<view class="askle-time">
								<view class="weui-cell">
									<view class="weui-cell__bd">
										<view style="color: #333;">开始日期<i class="red">*</i></view>
									</view>
									<view class="weui-cell__pft">
										2024-09-13<text style="visibility: visible;">（明天）</text>
									</view>
								</view>
								<view class="weui-cell">
									<view class="weui-cell__bd">
										<view style="color: #333;">结束日期<i class="red">*</i></view>
									</view>
									<view class="weui-cell__pft">
										2024-09-13<text style="visibility: visible;">（明天）</text>
									</view>
								</view>
							</view>
							<view class="stat-sum">
								共<view style="color: #C23C43;" id="askdaynum">1</view>
								天
							</view>
						</view>
						<view class="weui-cell weui-cell_access">
							<view class="weui-cell__bd">
								<view>请假事由<i class="red">*</i></view>
							</view>
							<view class="weui-cell__hd  weui-clr weui-cells_checkbox">

								<view class="weui-flex">
									<label class="radio_style weui-flex_center">
										<radio checked="check" style="top: -8px;"></radio>
									</label>病假
								</view>
								<view class="weui-flex" style="margin-left: 15px;"><label
										class="radio_style weui-flex_center">
										<radio style="top: -8px;"></radio>
									</label>事假</view>

								<view class="weui-flex" style="margin-left: 15px;"><label
										class="radio_style weui-flex_center">
										<radio style="top: -8px;"></radio>
									</label>其他</view>


							</view>



						</view>
						<view class="weui-cell weui-cell_access">
							<view class="weui-cell__bd">
								<view>发病日期</view>
							</view>
							<view class="weui-cell__hd  weui-clr ">
								<view class="weui-flex">
									<input type="text" id="txtfbdate" placeholder="请选择" autocomplete="off"
										class="weui-flex__item"
										style="border: none; font-size: 12px; text-align: right;" readonly="readonly" />
									<view class="iconfont icon_arrow-right" id="iconfbdate"
										style="height: 20px; line-height: 20px;"></view>
								</view>
							</view>
						</view>
						<view class="weui-cell weui-cell_access" href="javascript:;">
							<view class="weui-cell__bd">
								<view>请假期间是否在本地</view>
							</view>
							<view class="weui-cell__hd  weui-clr weui-cells_checkbox">

								<view class="weui-flex">
									<label class="radio_style weui-flex_center">
										<radio checked="check" style="top: -8px;"></radio>
									</label>是
								</view>
								<view class="weui-flex" style="margin-left: 15px;"><label
										class="radio_style weui-flex_center">
										<radio style="top: -8px;"></radio>
									</label>否</view>



							</view>
						</view>
						<view class="weui-cell weui-cell_access illreason">
							<view class="weui-cell__bd">
								<view>病假症状<i class="red">*</i></view>
							</view>
						</view>
						<view class="complement illreason" style="border-bottom: none;" id="selillreason">
							<text>感冒</text><text>咳嗽</text><text>发热</text><text>腹痛</text><text>皮疹</text><text>腹泻</text><text>黄疸</text><text>结膜红肿</text><text>呕吐</text><text>牙疼</text><text>外伤</text><text>咽痛</text><text>鼻塞</text>
							<text>流涕</text>
							<text>呼吸道感染</text>
							<text>肺炎</text>
							<text>其他</text>
						</view>
						<view class="weui-cell weui-cell_access">
							<view class="weui-cell__bd">
								<view><i class="red">*</i>是否发热</view>
							</view>
							<view class="weui-cell__hd  weui-clr weui-cells_checkbox">
								<view class="weui-flex">
									<label class="radio_style weui-flex_center">
										<radio checked="check" style="top: -8px;"></radio>
									</label>是
								</view>
								<view class="weui-flex" style="margin-left: 15px;"><label
										class="radio_style weui-flex_center">
										<radio style="top: -8px;"></radio>
									</label>否</view>

								<input class="degrees_txt" id="tiwen" type="text"
									style="margin-left: 15px; width: 50px;border-bottom: 1px solid #C5C5C5;">℃
							</view>
						</view>
						<view class="weui-cell weui-cell_access" href="javascript:;" id="viewjiuzhen">
							<view class="weui-cell__bd">
								<view>是否就诊<i class="red">*</i></view>
							</view>
							<view class="weui-cell__hd  weui-clr weui-cells_checkbox">
								<view class="weui-flex">
									<label class="radio_style weui-flex_center">
										<radio checked="check" style="top: -8px;"></radio>
									</label>是
								</view>
								<view class="weui-flex" style="margin-left: 15px;"><label
										class="radio_style weui-flex_center">
										<radio style="top: -8px;"></radio>
									</label>否</view>

								<text style="color: blue; margin-left: 15px; display: none" id="spjiuzhen">填写就诊情况</text>
							</view>
						</view>
						<view class="weui-cell weui-cell_access">
							<view class="weui-cell__bd">请假描述（0/50） </view>

						</view>
						<view class="inputbg">

							<textarea class="uni-input" style="width: 100%; " placeholder-style="color:#a7a8a9;"
								placeholder="请输入请假详情" /> </textarea>
						</view>



					</view>



				</view>

			</view>
		</view>
		<!-- 提交按钮 -->
		<view style="height: 160rpx;box-shadow: none;border-top:none;">
			<button class="common-btn   noafter">提交请假单</button>
		</view>


<!-- 请假历史模块 -->
<view>
		<view class="date-view">
			<image src="/pages/home/<USER>/image/arrow-left.png" style="width: 33rpx; height: 33rpx;"></image>
			<text id="spyearmonth">----年--月</text>
			<text id="sptotal" class="redtit">该月份共请假-天</text>
			<image src="/pages/home/<USER>/image/arrow-right.png" style="width: 33rpx; height: 33rpx;">
		</view>
		<view class="total-tab">
			<uni-table>
				<!-- 表头行 -->
				<uni-tr>
					<uni-th align="center">请假类别</uni-th>
					<uni-th align="center" class="redtit">总天数</uni-th>
					<uni-th align="center">爸爸请的</uni-th>
					<uni-th align="center">妈妈请的</uni-th>
					<uni-th align="center">其他</uni-th>
				</uni-tr>
				<!-- 表格数据行 -->
				<uni-tr>
					<uni-td align="center" class="redtit">全部</uni-td>
					<uni-td align="center" class="redtit">0天</uni-td>
					<uni-td align="center" class="redtit">0天</uni-td>
					<uni-td align="center" class="redtit">0天</uni-td>
					<uni-td align="center" class="redtit">0天</uni-td>
				</uni-tr>
				<uni-tr>
					<uni-td align="center">病假</uni-td>
					<uni-td align="center" class="redtit">0天</uni-td>
					<uni-td align="center">0天</uni-td>
					<uni-td align="center">0天</uni-td>
					<uni-td align="center">0天</uni-td>
				</uni-tr>
				<uni-tr>
					<uni-td align="center">事假</uni-td>
					<uni-td align="center" class="redtit">0天</uni-td>
					<uni-td align="center">0天</uni-td>
					<uni-td align="center">0天</uni-td>
					<uni-td align="center">0天</uni-td>
				</uni-tr>
				<uni-tr>
					<uni-td align="center">其他</uni-td>
					<uni-td align="center" class="redtit">0天</uni-td>
					<uni-td align="center">0天</uni-td>
					<uni-td align="center">0天</uni-td>
					<uni-td align="center">0天</uni-td>
				</uni-tr>

			</uni-table>

		</view>
		<view class="record-content">
			<view style="text-align: center">暂无请假历史</view>

		</view>

  <view class="record-content">
                <view class="record-rank" id="asklist">
                    <view class="record-con-list">
                        <image src="static/image/rec_mark.png" style="width: 13px; height: 13px;" class="record-img"></image>
					
                        <view class="recod-top">
                            <text>2016-08-09 上午</text><text class="record-date">2天</text><text>2016-08-10 下午</text></view>
                        <view class="word-list">
                            <view><text class="word-left">请假事由</text><text>：</text><text class="word-right">病假</text>
                            </view>
                            <view><text class="word-left">请假症状</text><text>：</text><text class="word-right">咳嗽、发热</text>
                            </view>
                            <view><span class="word-left">请假时间</span><span>：</span><p class="word-right">2018-05-16 18:15</p>
                            </view>
                            <view><text class="word-left"><a class="font-space"><i>请</i><i>假</i><i>人</i></a></text><text>：</text><text class="word-right">王小明妈妈</text>
                            </view>
                        </view>
                        <view class="record-btn"><a class="blue-label">待审批</a><a class="red-label">取消病假</a></view>
						
					<view class="word-list bortop">
					  
					    <view><text class="word-left">审批状态</text><text>：</text><text class="word-right redtxt">审批不通过</text>
					<!-- 	<text class="word-right greentxt">审批通过</text> -->
					<text class="leave-label">请假追踪</text>
					    </view>
					    <view><span class="word-left">审批时间</span><span>：</span><p class="word-right">2018-05-16 18:15</p>
					    </view>
					    <view><text class="word-left"><a class="font-space"><i>审</i><i>批</i><i>人</i></a></text><text>：</text><text class="word-right">王小明</text>
					    </view>
						  <view><text class="word-left">审批说明</text><text>：</text><text class="word-right">XXXXXXXXXXXXXXXXXXXXXXXXXXXXX</text>
											    </view>
					</view>
						
						
                    </view>
   </view>
      </view>



</view>


	</view>





</template>

<script>
</script>

<style>
	.module-top {
		background: #fff;
	}

	.index-top-tit {
		height: 80px;
		padding: 6px 0px 10px 15px !important;
		color: #fff !important;
		background: #C23C43;
		border-bottom-left-radius: 200px 20px;
		border-bottom-right-radius: 200px 20px;
	}

	.askleave-num {
		height: 40px;
		color: #C23C43;
		background: #ffffff;
		line-height: 17px;
		padding: 0 15px 0 20px;
		border-radius: 30px 0 0 30px;
	}

	.remarks-main .weui-cells_checkbox {
		margin: 5px 0 0 0;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
	}

	.remarks-main .weui-cells_checkbox>label {
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		align-items: center;
		margin-left: 5px;
	}

	.complement {
		padding-bottom: 10px;
		margin: 4px 10px;
	}

	.complement text {
		font-size: 15px;
		padding: 3px 8px;
		text-align: center;
		height: 20px;
		position: relative;
		display: inline-block;
		margin: 2px;
	}

	.complement text:after {
		content: "";
		height: 80%;
		border-right: 1px solid #ddd;
		position: absolute;
		right: 0;
	}

	.complement text:last-child:after {
		border: 0 none;
	}

	.complement-remind {
		text-align: center;
		margin: 30px auto;
	}

	.complement .current {
		background: #FF622C;
		color: #fff;
		border-radius: 5px;
		border-right: none;
	}

	.remarks-main {
		width: 100%;
		margin: 0 auto;
		overflow: auto;
		font-size: 15px;

	}

	.remarks-area {
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		margin: 15px 0px 10px 10px;
		padding-bottom: 15px
	}

	.remarks-main .remarks-area textarea {
		border: 1px solid #F4F4F4;
		width: 100%;
		height: 50px;
		vertical-align: top;
		outline: none;
		padding: 10px;
		border-radius: 3px;
		margin-left: 10px;
		margin-right: 10px;
	}

	.remarksview .weui-cells {
		margin: 10px;
		font-size: 15px;
	}

	.remarksview {
		background: #fff;
		padding: 0px 5px
	}

	.remarksview .weui-cell {
		padding: 10px 0px;
	}

	.remarks-main .weui-cells_checkbox .weui-check:checked+.weui-icon-checked::before {
		color: #ff5a5a;
	}



	.askle-time {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
	}

	.stat-sum {
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		height: 64px;
		padding: 4px 0;
		text-align: center;
		border: 1px solid #C23C43;
		width: 32px;
		border-radius: 5px;
		margin: 4px 10px 0px 10px;
		font-size: 14px
	}

	.askle-time input {
		width: 120px;
		border: none;
	}

	.askle-opr .weui-cell .weui-cell__bd {
		-webkit-box-flex: inherit;
		-webkit-flex: inherit;
		flex: inherit;
	}

	.askle-opr .weui-cell .weui-cell__hd,
	.askle-opr .weui-cell .weui-cell__pft {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		margin-left: 15px;
	}

	.askle-opr .weui-cell .weui-cells_checkbox {
		margin: 0 0 0 5px;
	}

	.remarks-main .weui-cells {
		margin-top: 10px;
		font-size: 14px;
		padding-bottom: 10px;
	}

	.red {
		color: #F45357;
	}

	.weui-cells:before {
		border-top: none;
	}

	.weui-cells:after {
		border-bottom: none
	}

	.weui-cell:before {
		right: 15px;
	}

	.inputbg {
		text-align: left;
		background: #F8F8F8;
		border-radius: 3px;
		padding: 5px 10px;
		font-size: 24rpx;
		color: #303030;
		line-height: 60rpx;
		height: 140rpx;
		margin: 0 15px 0px 15px;


	}

	.uni-input {
		text-align: left;
		font-size: 24rpx;
		color: #999999;
		line-height: 60rpx;
		height: 60rpx;
	}

	.date-view {
		height: 45px;
		background: #FFEDEF;
		display: flex;
		display: -webkit-flex;
		align-items: center;
		justify-content: space-around;
		font-size: 14px;
		color: #303030;
	}

	.date-view .iconfont {
		font-size: 14px;
		width: 30px;
		height: 100%;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.total-tab {
		background: #fff;

	}

	.table-border {
		border-left: none;
	}

	/deep/ .uni-table {
		min-width: auto !important;
	}

	.uni-table-th {
		color: #303030;
		font-weight: normal;
	}

	.redtit {
		color: #C23C43;
	}

	.record-content {
		background: #fff;
		margin-top: 10px;
		padding-top: 15px;
		padding-bottom: 15px;
		font-size: 32rpx;
		color: #303030;
	}
	
	
	.record-content{background: #fff;margin-top: 10px;padding-top: 15px;padding-bottom: 15px;}
	.record-rank{border-left: 3px solid #F7F9FB;margin-left: 15px;}
	.record-con-list{margin: 0 15px 15px 10px;position: relative;border-radius: 5px;background: #F8F8F8;}
	.record-con-list .recod-top{position:relative;display:-webkit-box;display:-webkit-flex;display:flex;align-items:center;justify-content:center;border-radius:5px 5px 0 0;color:#fff;font-size:14px;height: 40px;background: #C23C43;}
	.record-con-list .record-img{position: absolute;left: -19px;top: 2px;}
	.record-con-list .record-date{margin:0 5px 15rpx 5px;font-size: 12px;width: 93rpx;text-align: center;background: url(static/image/arrow_pic.png)no-repeat 50% 100%;background-size: 93rpx auto; padding-bottom: 3px;}
	.word-list{margin: 0 15px;padding: 10px 0;}
	.word-list view{display:-webkit-box;display:-webkit-flex;display:flex;font-size: 14px;line-height: 24px; }
	.word-list view .word-left{display:-webkit-box;display:-webkit-flex;display:flex;justify-content:space-between;width: 58px;text-align: right;color: #999999;}
	.word-list view .word-right{color: #646464; width:70%; white-space:nowrap; text-overflow:ellipsis; -o-text-overflow:ellipsis; overflow:hidden;}
	.word-list view .word-left .font-space{display:-webkit-box;display:-webkit-flex;display:flex;justify-content: space-between;width: 56px;color: #999999;}
	.font-space i{font-style:normal;}
	.blue-label{font-size: 24rpx;color: #EB7D1C;line-height: 42rpx;text-decoration-line: underline;}
	.record-btn{ margin: 0px 30rpx 0px 30rpx; border-top: 1px solid #EBEBEB; padding: 8px 0; display: flex;justify-content: space-between;}
	.red-label{color: #C23C43;background: #FFFFFF;
border-radius: 4rpx;
border: 1px solid #C23C43; font-size: 20rpx; text-align: center; padding:2px 5px; cursor: pointer;}

.bortop{border-top: 1px solid #EBEBEB; }
.leave-label{color: #5B87FD;background: #FFFFFF;
border-radius: 4rpx;
border: 1px solid #5B87FD; font-size: 20rpx; text-align: center; padding:0px 5px; cursor: pointer;position: absolute;right: 30rpx;}
</style>