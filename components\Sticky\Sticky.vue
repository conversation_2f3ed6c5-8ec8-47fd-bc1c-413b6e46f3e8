<template>
	<view class="sticky_wrap">
		<!-- 以此元素与顶部的距离判断是否需要吸顶 -->
		<view @scroll="scrllview()" class="sticky__depend" :style="[dependStyle]" />
		<!-- 吸顶元素占位器 -->
		<view class="sticky__placeholder" :style="[placeholderStyle]" />
		<view class="sticky" style="width:100%;z-index:9;" :style="[stickyStyle]">
			<slot name="sticky"></slot>
		</view>
	</view>
</template>

<script>
	export default {
		name:"Sticky",
		data() {
			return {
				isFixed: false, //是否吸顶
				scrollViewRect: 0, //吸顶元素的节点信息
			};
		},
		props:{
			sticky: {
				type: Boolean,
				default: false
			},
			stickyThreshold: {
				type: Number,
				default: 0
			},
			offsetTop: {
				type: [Number, String],
				default: 0
			},
		},
		computed: {
			// 吸顶的依赖元素样式
			dependStyle() {
				// stickyThreshold：是一个边界值，用于设置元素滚动到距屏幕顶部多少px时(计算方式是：this.stickyThreshold - this.offsetTop)，触发吸顶函数进行吸顶判断
				return { top: `${this.stickyThreshold}px` };
			},
			placeholderStyle() {
				console.log('placeholderStyle');
				if (!this.isFixed) {
					return {}
				}
				console.log('this.scrollViewRect:');
				console.log(this.scrollViewRect);
				return { height: this.scrollViewRect?.height + 'px' };
			},
			stickyStyle() {
				console.log('stickyStyle');
				if (!this.isFixed) {
					return {}
				}
				console.log('this.offsetTop:' + this.offsetTop);
				return { position: 'fixed', top: this.offsetTop + 'px' };
			},
		},
		watch:{
			isFixed(val) {
				// emit 吸顶变化事件
				this.$emit('sticky-change', { isFixed: val });
				this.updateScrollViewRect();
			},
		},
		mounted() {
			this.init();
		},
		methods: {
			async init() {
				this.scrollViewRect = await this.getRect('.sticky'); //sticky的节点信息
				console.log('this.scrollViewRect:');
				console.log(this.scrollViewRect);
				this.observeSticky();
			},
			scrllview(){
				console.log(2222222222222);
			},
			// 断掉观察，释放资源
			disconnectObserver(observerName) {
				var observer = this.dependObserver;
				observer && observer.disconnect();
			},
			// 观察 - 标签栏吸顶
			observeSticky() {
				//  offsetTop（只在初始化时有效，不能动态变更），使用resize初始化一下可生效。
				console.log('this.sticky:' + this.sticky);
				if (!this.sticky) return;
				this.disconnectObserver(); // 先断掉之前的观察
				// 检测的区间范围(在目标节点与参照节点在页面显示区域内相交或相离，且相交或相离程度达到目标节点布局区域的20%和50%时，触发回调函数)
				var dependObserver = this.createIntersectionObserver({
					thresholds: [0.01, 0.5, 1]
				});
				console.log('this.offsetTop:' + this.offsetTop);
				dependObserver.relativeToViewport({ top: -this.offsetTop }); // 到屏幕顶部的高度时触发
				// 绑定观察的元素
				dependObserver.observe('.sticky__depend', res => {
					console.log(111111111111);
					// 判断是否达到吸顶条件范围
					this.isFixed = res.intersectionRatio <= 0 && res.boundingClientRect.top <= this.offsetTop;
					
				});
				console.log('this.isFixed:' + this.isFixed);
				this.dependObserver = dependObserver;
			},
			// 更新节点信息
			updateScrollViewRect() {
			},
			// 获取元素信息
			getRect(selector) {
				return new Promise((resolve, reject) => {
					if (!selector) reject('Parameter is empty');
					const query = uni.createSelectorQuery().in(this);
					// 在百度小程序中，如果有多个tabs共存在一个页面，则获取的节点信息永远是第一个，这里使用根节点唯一的class进行隔离
					query.select(`${selector}`).boundingClientRect();
					query.exec(function(data){
						resolve(Array.isArray(data) ? data[0] : data);
					});
				})
			},
		}
	}
</script>

<style>
	.sticky__depend{
		position: absolute;
		top: 0;
		left: 0;
		height: 1px;
		width: 100%;
	}
</style>