
@font-face {
  font-family: 'iconfont';  /* project id 856087 */
  src: url('//at.alicdn.com/t/font_856087_j4tjk5k9b5l.eot');
  src: url('//at.alicdn.com/t/font_856087_j4tjk5k9b5l.eot?#iefix') format('embedded-opentype'),
  url('//at.alicdn.com/t/font_856087_j4tjk5k9b5l.woff2') format('woff2'),
  url('//at.alicdn.com/t/font_856087_j4tjk5k9b5l.woff') format('woff'),
  url('//at.alicdn.com/t/font_856087_j4tjk5k9b5l.ttf') format('truetype'),
  url('//at.alicdn.com/t/font_856087_j4tjk5k9b5l.svg#iconfont') format('svg');
}
.iconfont{
    font-family:"iconfont" !important;
    font-size:16px;font-style:normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;
    }
/*女*/
.icon_female:after{
  content:"\e662";
  color: #ffffff;
  font-size: 10px;
  } 
/*男*/
.icon_male:after{
  content:"\e65e";
  color: #ffffff;
  font-size: 10px;
  }
/*身高*/
.icon_height:after{
  content:"\e60a";
  color: #ffffff;
  font-size: 12px;      
  } 
/*体重*/
.icon_weight:after{
  content:"\e6c3";
  color: #ffffff;
  font-size: 12px;    
  }    
  /*年龄*/
.icon_age:after{
  content:"\e6c2";
  color: #ffffff;
  font-size: 12px;    
  }  
/*删除*/
.icon_delete:after{
  content:"\e600";
  color: #dddddd;
  font-size: 20px;    
  }  
/*上箭头*/
.icon_arrow-up:after{
  content:"\e608";
  color: #00cc99;
  font-size: 14px;    
  }   
/*下箭头*/
.icon_arrow-down:after{
  content:"\e6c4";
  color: #ff4545;
  font-size: 14px;    
  }     
/*箭头-上*/
.icon_arrow-top:after{
  content:"\e6c7";
  color: #333333;
  font-size: 24px;    
  }   
/*箭头-下*/
.icon_arrow-bottom:after{
  content:"\e6c6";
  color: #333333;
  font-size: 24px;    
  }       
/*箭头-左*/
.icon_arrow-left:after{
  content:"\e6c5";
  color: #333333;
  font-size: 24px;    
  }   
/*箭头-右*/
.icon_arrow-right:after{
  content:"\e601";
  color: #333333;
  font-size: 24px;    
  }  
/*添加*/
.icon_add:after{
  content:"\e686";
  color: #333333;
  font-size: 22px;    
  }
/*定位*/
.icon_local:after{
  content:"\e602";
  font-size: 16px;
  color: #799af4; 
}
/*会员服务*/
.member-ico:after{
  content:"\e622";
  color: #fff;
  font-size: 12px;
  }
/*订单记录*/
.form-ico:after{
  content:"\e60d";
  color: #fff;
  font-size: 12px;
  }  
/*使用帮助*/
.help-ico:after{
  content:"\e60b";
  color: #fff;
  font-size: 12px;
  } 
/*打赏点赞*/
.reward-ico:after{
  content:"\e60e";
  color: #fff;
  font-size: 12px;
  }
/*联系客服*/
.tel-ico:after{
  content:"\e62a";
  color: #fff;
  font-size: 12px;
  }
/*技术支持*/
.skill-ico:after{
  content:"\e624";
  color: #fff;
  font-size: 12px;
  }  
/*清除缓存*/
.clear-ico:after{
  content:"\e629";
  color: #fff;
  font-size: 12px;
  }    
/*我的食物*/
.food-ico:after{
  content:"\e978";
  color: #fff;
  font-size: 14px;
  }   
/*宝宝运动*/
.sport-ico:after{
  content:"\e66a";
  color: #fff;
  font-size: 14px;
  }    
  /*vip*/
.icon_vip:after{
  content:"\e6e9";
  font-size: 13px;
}
/*vip2*/
.icon_vip2:after{
  content:"\e619";
  color: #ebc57d;
  font-size: 22px;    
  vertical-align: top;
  }  
 /*身高指示*/
.icon_instructions:after{
  content:"\e93d";
  font-size: 13px;
   color: #ff5342;
}
 /*体重指示*/
.icon_instructions_height:after{
  content:"\e93c";
  font-size: 13px;
   color: #ff5342;
   vertical-align: top;
   height:10px;
}
  /*播放*/
.icon_broadcast:after{
  content:"\e93b";
  font-size: 13px;
   color: #fff;
}
 /*星星*/
.icon_stars:after{
  font-size: 40rpx;
   content:"\e609";
   display:inline-block;
}
.icon_stars.yellow:after{ color:#ffc600 }
.icon_stars.grey:after{ color:#DDDDDD }
/*返回*/
.icon_return:after{
  content:"\e604";
  font-size: 23px;
   color: #fff;
}
/*提醒*/
.icon_remind:after{
  content:"\e615";
  color: #333333;
  font-size: 14px;    
  }      
/*用户*/
.icon_user:after{
  content:"\e603";
  color: #ffffff;
  font-size: 12px;    
  vertical-align: top;
  }     
/*时间*/
.icon_time:after{
  content:"\e655";
  color: #ffffff;
  font-size: 12px;    
  vertical-align: top;
  }   
/*全国*/
.icon_nation:after{
  content:"\e882";
  color: #fdc481;
  font-size: 21px;    
  vertical-align: top;
  }    
/*全省*/
.icon_province:after{
  content:"\e785";
  color: #f4a595;
  font-size: 20px;    
  vertical-align: top;
  }   
/*全市*/
.icon_city:after{
  content:"\e657";
  color: #64e3a7;
  font-size: 17px;    
  vertical-align: top;
  }          
/*问号*/
.icon_ask:after{
  content:"\e606";
  color: #f36718;
  font-size: 17px;    
  vertical-align: top;
  }       
/*警告*/
.icon_warn:after{
  content:"\e663";
  color: #ff9c00;
  font-size: 14px;    
  vertical-align: top;
  }      
/*切换*/
.icon_tab:after{
  content:"\e658";
  color: #ffffff;
  font-size: 14px;    
  vertical-align: top;
  }  
/*星星*/
.icon_star:after{
  content:"\e70b";
  color: #f4ea2a;
  font-size: 14px;    
  vertical-align: top;
  }  
.icon_unstar:after{
  content:"\e70b";
  color: #eeeeee;
  font-size: 14px;    
  vertical-align: top;
  }
/*提醒2*/
.icon_remind2:after{
  content:"\e60c";
  color: #ffffff;
  font-size: 20px;    
  vertical-align: top;
  }   
/*编辑*/
.icon_edit:after{
  content:"\e607";
  color: #2ea2f6;
  font-size: 12px;    
  vertical-align: top;
  }                        
  /*注意事项*/
.icon_careful:after{
  content:"\e61e";
  color: #ffffff;
  font-size: 10px;    
  vertical-align: top;
  }                        
/*参与人群*/
.icon_people:after{
  content:"\e6a6";
  color: #ffffff;
  font-size: 10px;    
  vertical-align: top;
  }                        
 /*适合年龄*/
.icon_ages:after{
  content:"\e849";
  color: #ffffff;
  font-size: 10px;    
  vertical-align: top;
  }   
/*新手指引*/
.icon_point:after{
  content:"\e61f";
  color: #f19d38;
  font-size: 16px;    
  vertical-align: top;
  }    
/*关闭*/
.icon_close:after{
  content:"\e610";
  color: #ffffff;
  font-size: 26px;    
  vertical-align: top;
  }   
/*关闭2*/
.icon_close2:after{
  content:"\e611";
  color: #4b4b4b;
  font-size: 15px;    
  vertical-align: top;
  }       
/*微信*/
.icon_weixin:after{
  content:"\e623";
  color: #ffffff;
  font-size: 26px;    
  vertical-align: top;
  }      
/*编辑2*/
.icon_edit2:after{
  content:"\e671";
  color: #00a2ff;
  font-size: 15px;    
  vertical-align: top;
  }      
/*分享*/
.icon_share:after{
  content:"\e618";
  color: #00a2ff;
  font-size: 20px;    
  vertical-align: top;
  }     
/*搜索*/
.icon_search:after{
  content:"\e954";
  color: #999999;
  font-size: 14px;    
  vertical-align: top;
  }        
/*对勾*/
.icon_select:after{
  content:"\e77e";
  color: #dddddd;
  font-size: 12px;    
  vertical-align: top;
  }       
/*对勾*/
.icon_select2:after{
  content:"\e6dd";
  color: #00cc99;
  font-size: 12px;    
  vertical-align: top;
  } 
/*通知*/
.icon_notify:after{
  content:"\e612";
  color: #ff9c00;
  font-size: 13px;    
  vertical-align: top;
  }     
/*添加*/
.icon_add2:after{
  content:"\e613";
  color: #ff9c00;
  font-size: 13px;    
  vertical-align: top;
  }            
/*评价*/
.icon_comment:after{
  content:"\e66b";
  color: #ff8d3a;
  font-size: 16px;    
  vertical-align: top;
  }       
/*视频*/
.icon_video:after{
  content:"\e614";
  color: #04c8ad;
  font-size: 12px;    
  vertical-align: top;
  }        
/*返回*/
.icon_refresh:after{
  content:"\e616";
  color: #48a7ff;
  font-size: 20px;    
  vertical-align: top;
  }     
         