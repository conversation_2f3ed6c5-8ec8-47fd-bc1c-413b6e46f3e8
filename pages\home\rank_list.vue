<template>
	<view>
		<view style="overflow-x: hidden; position: absolute;bottom: 0;width: 100%;height: 100%;">

			<image src="/static/image/bzn/chartsbg.png" style="width: 100%;height:369rpx; position: absolute;top: 0;">
			</image>
		</view>
		<view class="cen-bgview">
			<view class="weui-flex font12" style="justify-content: flex-end; margin: 10px 15px;">
				<view class="greentxt" style="margin-right: 10px;;">家长数：40</view>
				<view class="greentxt">学生数：40</view>
			</view>

			<view class="table-list">
				<view class="list-cell weui-flexcen" style="background: #FDF9E6;
border-radius: 5px; ">
					<view style="color: #FFB738;">
						<view style="width: 80rpx;">我</view>
					</view>
					<view class="weui-flexcen weui-flex__item ">
						<image src="/static/image/bzn/img_default.png"
							style="width:60rpx;height: 60rpx; margin-right: 20rpx; border-radius: 50%;">
						</image>王多鱼
					</view>
					<view style="color: #FFB738;">20岁</view>

				</view>

				<view class="list-cell weui-flexcen" style="color: #999999;">
					<view>
						<view style="width: 80rpx;">排名</view>
					</view>
					<view class="weui-flexcen weui-flex__item ">
						学生姓名
					</view>
					<view >呵护值</view>

				</view>
	<view class="list-cell weui-flexcen">
									<view>
										<view style="width: 80rpx;"><image src="/static/image/bzn/oneico.png"
											style="width:60rpx;height: 60rpx;"></view>
									</view>
									<view class="weui-flexcen weui-flex__item ">
										<image src="/static/image/bzn/img_default.png"
											style="width:60rpx;height: 60rpx; margin-right: 10rpx; border-radius: 50%;">
										</image>王多鱼
									</view>
									<view >20</view>
				
								</view>





<view class="list-cell weui-flexcen">
					<view>
						<view style="width: 80rpx;"><image src="/static/image/bzn/twoimg.png"
							style="width:60rpx;height: 60rpx;"></view>
					</view>
					<view class="weui-flexcen weui-flex__item ">
						<image src="/static/image/bzn/img_default.png"
							style="width:60rpx;height: 60rpx; margin-right: 10rpx; border-radius: 50%;">
						</image>王多鱼
					</view>
					<view >20</view>

				</view>
				<view class="list-cell weui-flexcen">
									<view>
										<view style="width: 80rpx;"><image src="/static/image/bzn/threeico.png"
											style="width:60rpx;height: 60rpx;"></view>
									</view>
									<view class="weui-flexcen weui-flex__item ">
										<image src="/static/image/bzn/img_default.png"
											style="width:60rpx;height: 60rpx; margin-right: 10rpx; border-radius: 50%;">
										</image>王多鱼
									</view>
									<view >20</view>
				
								</view>

	<view class="list-cell weui-flexcen">
									<view>
										<view style="width: 80rpx;text-align: center;font-weight: bold;">4</view>
									</view>
									<view class="weui-flexcen weui-flex__item ">
										<image src="/static/image/bzn/img_default.png"
											style="width:60rpx;height: 60rpx; margin-right: 10rpx; border-radius: 50%;">
										</image>王多鱼
									</view>
									<view >20</view>
				
								</view>



			</view>

		</view>
	</view>


</template>

<script>
</script>

<style>
	page {
		background: #FFF4D9;
	}

	.cen-bgview {
		background: #fff;
		left: 15px;
		right: 15px;
		border-radius: 5px;
		top: 130px;
		bottom: 20px;
		position: absolute;
	}

	.table-list {
		font-size: 28rpx;
		color: #303030;
		margin: 0 30rpx;
	}


	.table-list .list-cell {
		padding: 20rpx 30rpx;




	}

	.table-list .list-cell view {}

	.table-list .list-cell:first-child {
		background: #ffffff;
		border-top: none;
	}

	.table-list .list-cell .weui-flex__item {
		margin: 0 10rpx;
		align-items: center;
	}

	.weui-cell {
		font-size: 14px;
	}
</style>