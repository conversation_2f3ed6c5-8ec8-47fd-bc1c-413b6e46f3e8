function formatTime(time) {
	if (typeof time !== 'number' || time < 0) {
		return time
	}

	var hour = parseInt(time / 3600)
	time = time % 3600
	var minute = parseInt(time / 60)
	time = time % 60
	var second = time

	return ([hour, minute, second]).map(function(n) {
		n = n.toString()
		return n[1] ? n : '0' + n
	}).join(':')
}

function formatLocation(longitude, latitude) {
	if (typeof longitude === 'string' && typeof latitude === 'string') {
		longitude = parseFloat(longitude)
		latitude = parseFloat(latitude)
	}

	longitude = longitude.toFixed(2)
	latitude = latitude.toFixed(2)

	return {
		longitude: longitude.toString().split('.'),
		latitude: latitude.toString().split('.')
	}
}
var dateUtils = {
	UNITS: {
		'年': 31557600000,
		'月': 2629800000,
		'天': 86400000,
		'小时': 3600000,
		'分钟': 60000,
		'秒': 1000
	},
	humanize: function(milliseconds) {
		var humanize = '';
		for (var key in this.UNITS) {
			if (milliseconds >= this.UNITS[key]) {
				humanize = Math.floor(milliseconds / this.UNITS[key]) + key + '前';
				break;
			}
		}
		return humanize || '刚刚';
	},
	format: function(dateStr) {
		var date = this.parse(dateStr)
		var diff = Date.now() - date.getTime();
		if (diff < this.UNITS['天']) {
			return this.humanize(diff);
		}
		var _format = function(number) {
			return (number < 10 ? ('0' + number) : number);
		};
		return date.getFullYear() + '/' + _format(date.getMonth() + 1) + '/' + _format(date.getDate()) + '-' +
			_format(date.getHours()) + ':' + _format(date.getMinutes());
	},
	parse: function(str) { //将"yyyy-mm-dd HH:MM:ss"格式的字符串，转化为一个Date对象
		var a = str.split(/[^0-9]/);
		return new Date(a[0], a[1] - 1, a[2], a[3], a[4], a[5]);
	}
};

function getStrByDate(d) { //获取日期字符串 2015-01-01
	if (!d) d = new Date();
	return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) +
		"-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate());
}

function getStrBirthDes(strDate, birth, onlymonth) {
	var strage = GetAge(birth, strDate, 'text', onlymonth);
	return strage;
}

function GetAge(birday, nowDate, flag, onlymonth) {
	if (!birday) return "";
	if (typeof birday == "string") {
		birday = new Date(birday.split(" ")[0].replace(/-/g, "/"));
	}
	if (typeof nowDate == "string") {
		nowDate = new Date(nowDate.split(" ")[0].replace(/-/g, "/"));
	}
	if (birday > nowDate) {
		return "未出生";
	}
	var nowYear = nowDate.getFullYear();
	var birYear = birday.getFullYear();
	var nowMonth = nowDate.getMonth() + 1;
	var birMonth = birday.getMonth() + 1;
	var nowDay = nowDate.getDate();
	var birDate = birday.getDate();
	var ageMonth = 0;
	var ageYear = 0;
	var ageDay = 0;
	if (birMonth > nowMonth) {
		nowYear -= 1;
		ageMonth = 12 - birMonth + nowMonth;
	} else {
		ageMonth = nowMonth - birMonth;
	}
	ageYear = nowYear - birYear;
	if (nowDay < birDate) {
		ageMonth--;
		ageDay = parseInt((nowDate - new Date(nowDate.getFullYear(), nowDate.getMonth() - 1, birday.getDate())) / (24 *
			60 * 60 * 1000));
	} else {
		ageDay = nowDay - birDate;
	}
	if (ageMonth < 0) {
		ageYear--;
		ageMonth = 11;
	}
	if (!flag || flag == 'num') {
		if (parseInt(ageMonth) == 0)
			return ageYear + ".00";
		else {
			if (ageMonth < 10)
				ageMonth = "0" + ageMonth;
			return ageYear + "." + ageMonth;
		}
	} else {
		if (ageYear || ageMonth || ageDay) {
			if (onlymonth) {
				return (ageYear ? ageYear + "岁" : "") + (ageMonth ? ageMonth + "个月" : "");
			} else {
				return (ageYear ? ageYear + "岁" : "") + (ageMonth ? ageMonth + "个月" : "") + (ageDay ? ageDay + "天" :
					"");
			}
		} else {
			return "今天出生";
		}
	}
}

function formatDate(strDate) {
	var now = new Date();
	var day = strDate ? new Date(strDate) : new Date();
	var difftime = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() - new Date(day.getFullYear(), day.getMonth(), day.getDate()).getTime();
	if (difftime >= 0) {
		if (difftime == 0) {
			return "今天";
		} else if (difftime == 24 * 60 * 60 * 1000) {
			return "昨天";
		} else if (difftime == 2 * 24 * 60 * 60 * 1000) {
			return "前天";
		} else if (difftime / (24 * 60 * 60 * 1000) < 7) {
			return difftime / (24 * 60 * 60 * 1000) + "天前";
		}
	}
	return strDate.substring(0, 10);
}

function getDate(strdate) { //获取date
	if (strdate)
		return new Date(strdate.split(" ")[0].replace(/-/g, "/"));
	else
		return null;
}

function getMonthAge(age) { //通过年龄得到月龄
	var arr = age.split('.');
	return parseInt(arr[0]) * 12 + parseInt(arr[1]);
}
//获得num天后日期
function getnextday(date, num) {
	var currentDate = getDateByStr(date);
	var d = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() + num);
	return getStrByDate(d);
}
//获得num天钱前日期
function getpreday(date, num) {
	var currentDate = getDateByStr(date);
	var d = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - num);
	return getStrByDate(d);
}

function getDateByStr(dstr) {
	if (typeof(dstr) == "object") return dstr;
	var arr = getDateArray(dstr);
	if (arr.length > 3) {
		return new Date(arr[0], arr[1], arr[2], arr[3], arr[4], arr[5]);
	}
	return new Date(arr[0], arr[1], arr[2]);
}

function getDateArray(datestr) {
	var Result = [];
	var arr = datestr.split(' ')[0].split("-");
	arr[1] = arr[1].replace(/^0(\d)/, "$1");
	arr[2] = arr[2].replace(/^0(\d)/, "$1");
	Result.push(parseInt(arr[0]));
	Result.push(parseInt(arr[1]) - 1);
	Result.push(parseInt(arr[2]));
	if (datestr.split(' ')[1] && datestr.split(' ')[1].length > 1) {
		var arr1 = datestr.split(' ')[1].split(":");
		arr1[1] = arr1[1].replace(/^0(\d)/, "$1");
		arr1[2] = arr1[2].replace(/^0(\d)/, "$1");
		Result.push(parseInt(arr1[0]));
		Result.push(parseInt(arr1[1]));
		Result.push(parseInt(arr1[2]));
	}
	return Result;
}

//获取周
function get_week(date) {
	var values = ["日", "一", "二", "三", "四", "五", "六"];
	date = getDateByStr(date);
	return values[date.getDay()];
}
//获取周
function get_week_num(date) {
	date = getDateByStr(date);
	return date.getDay();
}
//得到月第一天
function getMonthFirstDay(d, dt) {
    return getStrByDate(new Date(d.getFullYear(), d.getMonth() + (dt || 0), '01'));
}
//得到月最后一天
function getMonthLastDay(d, dt) {
    return getStrByDate(new Date(d.getFullYear(), d.getMonth() + 1 + (dt || 0), '00'));
}

//获得本周周一日期
function getMondayDate(date) {
    var idendity = date.getDay();  //返回值0-6 ,分别表示这个礼拜的星期日到星期六
    var arr = [6, 0, 1, 2, 3, 4, 5];
    var d = new Date(date.getFullYear(), date.getMonth(), date.getDate() - arr[idendity]);
    return getStrByDate(d);
}

//获得本周周日日期
function getSundayDate(date) {
    var idendity = date.getDay();  //返回值0-6 ,分别表示这个礼拜的星期日到星期六
    var arr = [6, 0, 1, 2, 3, 4, 5];
    var d = new Date(date.getFullYear(), date.getMonth(), date.getDate() + (6 - arr[idendity]));
    return getStrByDate(d);
}
//得到与当前月相差指定月数的日期
function getMonthDate(dstr, dt) {
    var arr = getDateArray(dstr);
    var d = new Date(arr[0], arr[1] + dt, arr[2]);
    return getStrByDate(d);
}

function getLunarObj(date) {
    //农历年信息  
    var lunarInfo = new Array(
        0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2,
        0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977,
        0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970,
        0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950,
        0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557,
        0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5d0, 0x14573, 0x052d0, 0x0a9a8, 0x0e950, 0x06aa0,
        0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0,
        0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b5a0, 0x195a6,
        0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570,
        0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x055c0, 0x0ab60, 0x096d5, 0x092e0,
        0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5,
        0x0a950, 0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930,
        0x07954, 0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530,
        0x05aa0, 0x076a3, 0x096d0, 0x04bd7, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45,
        0x0b5a0, 0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0);
    var Animals = new Array("鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪");
    var Gan = new Array("甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸");
    var Zhi = new Array("子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥");

    //==== 传回农历 y年的总天数  
    function lYearDays(y) {
        var i, sum = 348
        for (i = 0x8000; i > 0x8; i >>= 1) sum += (lunarInfo[y - 1900] & i) ? 1 : 0
        return (sum + leapDays(y))
    }
    //==== 传回农历 y年闰月的天数  
    function leapDays(y) {
        if (leapMonth(y))
            return ((lunarInfo[y - 1900] & 0x10000) ? 30 : 29)
        else
            return (0)
    }
    //==== 传回农历 y年闰哪个月 1-12 , 没闰传回 0  
    function leapMonth(y) {
        return (lunarInfo[y - 1900] & 0xf);
    }
    //==== 传回农历 y年m月的总天数  
    function monthDays(y, m) {
        return ((lunarInfo[y - 1900] & (0x10000 >> m)) ? 30 : 29);
    }
    //==== 算出农历, 传入日期物件, 传回农历日期物件  
    //      该物件属性有 .year .month .day .isLeap .yearCyl .dayCyl .monCyl  
    function lunar(objDate) {
        var i, leap = 0, temp = 0
        var baseDate = new Date(1900, 0, 31)
        var offset = (objDate - baseDate) / 86400000

        this.dayCyl = offset + 40
        this.monCyl = 14

        for (i = 1900; i < 2050 && offset > 0; i++) {
            temp = lYearDays(i)
            offset -= temp
            this.monCyl += 12
        }
        if (offset < 0) {
            offset += temp;
            i--;
            this.monCyl -= 12
        }

        this.year = i
        this.yearCyl = i - 1864

        leap = leapMonth(i) //闰哪个月  
        this.isLeap = false

        for (i = 1; i < 13 && offset > 0; i++) {
            //闰月  
            if (leap > 0 && i == (leap + 1) && this.isLeap == false) { --i; this.isLeap = true; temp = leapDays(this.year); }
            else { temp = monthDays(this.year, i); }

            //解除闰月  
            if (this.isLeap == true && i == (leap + 1)) this.isLeap = false

            offset -= temp
            if (this.isLeap == false) this.monCyl++
        }

        if (offset == 0 && leap > 0 && i == leap + 1)
            if (this.isLeap) { this.isLeap = false; }
            else { this.isLeap = true; --i; --this.monCyl; }

        if (offset < 0) { offset += temp; --i; --this.monCyl; }

        this.month = i
        this.day = offset + 1
    }
    //获取农历（月）中文格式  
    function get_lunarmonth(month) {
        var fm = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "腊月"];
        return fm[month - 1];
    }
    //获取农历（日）中文格式  
    function get_lunarday(day) {
        var fd = ["十", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"];
        if (day <= 10) {
            return "初" + fd[day];
        }
        else if (day < 20) {
            return "十" + fd[day - 10];
        }
        else if (day == 20) {
            return "二十";
        }
        else if (day < 30) {
            return "廿" + fd[day - 20];
        }
        else {
            return "三" + fd[day - 30];
        }
    }

    //获取干支  
    function get_ganzhi(year) {
        var num = year - 1900 + 36;
        return (Gan[num % 10] + Zhi[num % 12]);
    }
    //获取生肖  
    function get_animal(year) {
        return Animals[(year - 4) % 12];
    }

    var viewdate = {};
    date = getDateByStr(date);
    //星期  
    viewdate.week = get_week(date);
    //农历信息  
    var lunar_obj = new lunar(date);
    //农历中文月  
    viewdate.lunarmonth = get_lunarmonth(lunar_obj.month);
    //农历中文日  
    var lunar_day = Math.floor(lunar_obj.day);
    viewdate.lunarday = get_lunarday(lunar_day);
    //农历年月日  
    viewdate.lunar = lunar_obj.year + "-" + lunar_obj.month + "-" + lunar_day;
    //干支  
    viewdate.ganzhi = get_ganzhi(lunar_obj.year);
    //生肖  
    viewdate.animal = get_animal(lunar_obj.year);

    return viewdate;
}


/***************************************************************************
 *                     解决JS浮点数(小数)计算加减乘除的BUG Start                *
 ***************************************************************************/
//给Number类型增加一个add加法方法，调用起来更加方便。
Number.prototype.add = function(arg) {
	return FloatAdd(this, arg);
};
// 给Number类型增加一个sub减法方法，调用起来更加方便。
Number.prototype.sub = function(arg) {
	return FloatSub(this, arg);
};
// 给Number类型增加一个mul乘法方法，调用起来更加方便。
Number.prototype.mul = function(arg) {
	return FloatMul(this, arg);
};
//给Number类型增加一个div除法方法，调用起来更加方便。
Number.prototype.div = function(arg) {
	return FloatDiv(this, arg);
};
//给Number类型增加一个parseFloatMoney方法，统一格式化金额。
Number.prototype.parseFloatMoney = function() {
	return parseFloatMoney(this);
};
//给Number类型增加一个parseFloatPrice方法，统一格式化金额。
Number.prototype.parseFloatPrice = function() {
	return parseFloatPrice(this);
};

/**
 ** 加法函数，用来得到精确的加法结果
 ** 说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。
 ** 调用：FloatAdd(arg1,arg2)
 ** 返回值：arg1加上arg2的精确结果
 **/
function FloatAdd(arg1, arg2) {
	var s1 = arg1.toString();
	var s2 = arg2.toString();
	var r1 = 0;
	var r2 = 0;
	try {
		if (s1.indexOf("E") != -1 || s1.indexOf("e") != -1) {
			s1 = arg1.scientificToNumber();
		}
		r1 = s1.split('.')[1].length;
	} catch (e) {}
	try {
		if (s2.indexOf("E") != -1 || s2.indexOf("e") != -1) {
			s2 = arg2.scientificToNumber();
		}
		r2 = s2.split('.')[1].length;
	} catch (e) {}

	var m = Math.pow(10, Math.max(r1, r2));
	return (Math.round(arg1 * m) + Math.round(arg2 * m)) / m;
}

/**
 ** 减法函数，用来得到精确的减法结果
 ** 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
 ** 调用：FloatSub(arg1,arg2)
 ** 返回值：arg1加上arg2的精确结果
 **/
function FloatSub(arg1, arg2) {
	return FloatAdd(arg1, -arg2);
}

/**
 ** 乘法函数，用来得到精确的乘法结果
 ** 说明：javascript的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。这个函数返回较为精确的乘法结果。
 ** 调用：FloatMul(arg1,arg2)
 ** 返回值：arg1乘以 arg2的精确结果
 **/
function FloatMul(arg1, arg2) {
	var s1 = arg1.toString();
	var s2 = arg2.toString();
	var m = 0;
	try {
		if (s1.indexOf("E") != -1 || s1.indexOf("e") != -1) {
			s1 = arg1.scientificToNumber();
		}
		m += s1.split('.')[1].length;
	} catch (e) {}
	try {
		if (s2.indexOf("E") != -1 || s2.indexOf("e") != -1) {
			s2 = arg2.scientificToNumber();
		}
		m += s2.split('.')[1].length;
	} catch (e) {}

	return (s1.replace('.', '') - 0) * (s2.replace('.', '') - 0) / Math.pow(10, m);
}

/**
 ** 除法函数，用来得到精确的除法结果
 ** 说明：javascript的除法结果会有误差，在两个浮点数相除的时候会比较明显。这个函数返回较为精确的除法结果。
 ** 调用：FloatDiv(arg1,arg2)
 ** 返回值：arg1除以arg2的精确结果
 **/
function FloatDiv(arg1, arg2) {
	var s1 = arg1.toString();
	var s2 = arg2.toString();
	var m = 0;
	try {
		if (s2.indexOf("E") != -1 || s2.indexOf("e") != -1) {
			s2 = arg2.scientificToNumber();
		}
		m = s2.split('.')[1].length;
	} catch (e) {}
	try {
		if (s1.indexOf("E") != -1 || s1.indexOf("e") != -1) {
			s1 = arg1.scientificToNumber();
		}
		m -= s1.split('.')[1].length;
	} catch (e) {}
	return FloatMul((s1.replace('.', '') - 0) / (s2.replace('.', '') - 0), Math.pow(10, m));
}

/**
 * 统一处理金钱位数 **
 * 调用：parseFloatMoney(arg1)
 ** 返回值：arg1除以arg2的精确结果
 */
function parseFloatMoney(arg1) {
	var dd = 2; //处理位数
	return parseFloat(arg1).toFixed(dd);
}

/**
 * 统一处理价格位数 **
 * 调用：parseFloatMoney(arg1)
 ** 返回值：arg1除以arg2的精确结果
 */
function parseFloatPrice(arg1) {
	return parseFloat(arg1);
}

export {
	formatTime,
	formatLocation,
	getStrBirthDes,
	GetAge,
	dateUtils,
	formatDate,
	getDate,
	getMonthAge,
	getStrByDate,
	getDateByStr,
	getnextday,
	getpreday,
	get_week,
	get_week_num,
	getLunarObj,
	getMondayDate,
	getSundayDate,
	getMonthFirstDay,
	getMonthLastDay,
	getMonthDate
}