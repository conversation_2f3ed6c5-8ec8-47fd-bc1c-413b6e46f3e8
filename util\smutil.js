/**
 * 消息框架工具类
 */
var smutil = {
	isspChar: 1, //是否替换
	SpecialChars: {
		"%": "%25",
		",": "%2C",
		"'": "%27",
		'"': "%22",
		"\\+": "%2B",
		"\\r": "%0D",
		"\\n": "%0A",
		"\\t": "%09",
		"<": "%3C",
		">": "%3E",
		"&": "%26"
	},
	//数组转码
	encodeArr: function(arr) {
		var _this = this;
		for (var j = 0; j < arr.length; j++) {
			if (typeof arr[j] == "string") {
				arr[j] = _this.EncodeSpChar(arr[j]);
			}
		}
		return arr;
	},
	EncodeSpChar: function(str, h) { //debugger;'&acute;"&quot;明确是\r \n的数据库存的是\\r\\n
		var a = this.SpecialChars;
		var t = "";
		for (var i in a) {
			if (typeof(a[i]) == "string") t += i + "|";
		}
		t = t.substr(0, t.length - 1);
		if (h) {
			var o = {
				"'": "&acute;",
				'"': "&quot;"
			};
			str = str.replace(/\'|\"/g, function(c) {
				return o[c];
			});
		}
		var p = new RegExp(t, "gim");
		return str.replace(p, function(c) {
			//debugger;
			var r1 = a["\\" + c] || a[c];
			if (!r1) {
				//	            if(c=="\n"||c=="\r"||c=="\t")return "";
				if (c == "\n") return "%0A";
				if (c == "\r") return "%0D";
				if (c == "\t") return "%09";
			} else
				return r1;
		});
	},
	getSmParam: function(arr, t, did, pobj, timeout, trans) {
		var strp = "";
		var ismul = 0;
		var _this = this;
		if (typeof arr[0] == "object") {
			ismul = 1;
			var arr2 = [];
			for (var i = 0; i < arr.length; i++) {
				arr2.push(_this.encodeArr(arr[i]).join('%15'));
			}
			strp = arr2.join('%18');
		} else {
			strp = _this.encodeArr(arr).join('%15');
		}
		var res = {
			"trans": (pobj && pobj.trans) || trans || "",
			"isspChar": _this.isspChar,
			"timestamp": new Date().getTime() + parseInt((Math.random() + 1) * Math.pow(10, 3 - 1)),
			"ismul": ismul,
			"arr": strp,
			"t": t || "",
			"did": did || "",
			"lan": 'zh'
		};
		if (pobj && pobj.rpc) {
			res.rpc = pobj.rpc;
		}
		if (pobj && pobj.rpcurl) {
			res.rpcurl = pobj.rpcurl;
		}
		return res;
	},
	msgwhere: function(obj1, obj2) {
		var obj = {};
		if (obj1) {
			obj = Object.assign(obj, obj1);
		}
		if (obj2) {
			obj = Object.assign(obj, obj2);
		}
		return JSON.stringify({
			msg_where: obj
		})
	},
	msgpJoin: function(arr) {
		var arrnew = [];
		for (var i = 0; i < arr.length; i++) {
			arrnew.push([arr[i]]);
		}
		return arrnew;
	}
};
//导出
module.exports = smutil;