<template>
	<view>

		<view class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
				<view class="weui-cell__bd">处理时间</view>
				<view class="weui-cell__ft weui-flex" style="align-items: center;">2024-06-26
				</view>
			</view>
	
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">重量（公斤）</view>
				<view class="weui-cell__ft">20</view>
			</view>
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">收运单位</view>
				<view class="weui-cell__ft">成都天投慰蓝生物有限公司</view>
			</view>
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">收运人</view>
				<view class="weui-cell__ft">冯良</view>
			</view>
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">交接人</view>
				<view class="weui-cell__ft">冯良</view>
			</view>
			<view class="weui-cell weui-cell_access">
				<view class="weui-cell__bd">消毒方式</view>
				<view class="weui-cell__ft">高温蒸煮</view>
			</view>
		
		</view>
		
		
		<view class="remarks-list">
		
			<view class="font14 martop">图片附件</view>
			<view class="weui-uploader__bd" style="margin: 10px 0 0 0;">
				<view class="weui-uploader__files" style="padding-top: 5px;">
					<view class="weui-uploader__file">
						<!-- <image src="../shop/static/image/icon_close.png" class="img-close"></image> -->
						<image src="static/image/img_def.png" class="img-upload"></image>
					</view>
						</view>
			</view>
			

		</view>
		

	</view>
</template>

<script>

</script>
<style>
	page {
		background: #F2F4F5;
	}

	.weui-cell {
		padding: 15px;
	}

	.weui-cell .weui-cell__bd {
		font-size: 14px;
		color: #999999;
	}

	.weui-cell .weui-cell__ft {
		font-size: 14px;
		color: #303030;
	}

	.weui-cell:before {
		right: 15px;
	}

	.weui-cells:after {
		right: 15px;
		left: 15px;
	}

	.inputbg {
		text-align: left;
		background: #F8F8F8;
		border-radius: 3px;
		padding: 5px 10px;
		font-size: 24rpx;
		color: #303030;
		line-height: 60rpx;
		height: 60rpx;
		margin-top: 10px;

	}

	.uni-input {
		text-align: left;
		font-size: 24rpx;
		color: #999999;
		line-height: 60rpx;
		height: 60rpx;
	}

	.remarks-list {
		background: #fff;
		margin-top: 10px;
		padding: 15px;
	}
	
	.weui-uploader__input-box {
		width: 216rpx;
		background: #F7F9FC !important;
		height: 216rpx;
	}
	
	.weui-uploader__file {
		position: relative;
		width: 216rpx;
		height: 216rpx;
	}
	
	.weui-uploader__input-box {
		box-sizing: border-box;
		border: 1px solid #eef1f2;
		background: #f9f9f9;

	}
	
	.img-upload {
		width: 216rpx;
		height: 216rpx;

		display: block;
	
	}
	
	.img-close {
		width: 22rpx;
		height: 22rpx;
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		z-index: 111;
	}
	
	.uni-combox {
		border: none;
		line-height: 60rpx;
		height: 60rpx;
		padding: 0 8px 0 0;
	}
	
	.weui-uploader__input-box:after,
	.weui-uploader__input-box:before {
		background-color: transparent;
	}
</style>