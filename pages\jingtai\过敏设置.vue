<template>
	<view>
		<view class="allergy-top">
			<view class="weui-cell">
				<view class="weui-cell__bd font15">苦荞挂面</view>
				<view class="weui-cell__ft weui-flex" style="align-items: center;">
					<view class="stat-tab">
						<view class="tab-txt curr-stat">轻度过敏</view>
						<view class="tab-txt">中度过敏</view>
						<view class="tab-txt">重度过敏</view>
					</view>


				</view>
			</view>

		</view>

		<!-- 确认按钮 -->
		<view class="button-bottom " style="height: 160rpx;box-shadow: none;border-top:none;">
			<button class="common-btn noafter">确认</button>
		</view>
	</view>

</template>

<script>
</script>

<style>
	page {
		background: #F2F4F5;
	}

	.allergy-top {
		background: #FFFFFF;
		box-shadow: inset 0px -1px 0px 0px #F1F1F1;
		border-radius: 0px 0px 0px 0px;
	}


	.stat-tab {
		border: 1px solid #C23C43;
		display: flex;
		display: -webkit-flex;
		align-items: center;
		width: 452rpx;
		border-radius: 2px;
	}

	.stat-tab view {
		font-size: 28rpx;
		color: #C23C43;
		display: flex;
		display: -webkit-flex;
		align-items: center;
		justify-content: center;
		height: 28px;
		line-height: initial;
		position: relative;
		flex: 1;
		-webkit-flex: 1;
		background: #FFECEF;
	}

	.stat-tab view:after {
		content: "";
		border-right: 1px solid #C23C43;
		height: 28px;
		position: absolute;
		right: 0;
		top: 0;
	}

	.stat-tab view:first-child {
		border-radius: 3px 0 0 3px;
	}

	.stat-tab view:last-child {
		border-radius: 0 2px 3px 0;
	}

	.stat-tab view:last-child:after {
		content: none;
	}

	.stat-tab view.curr-stat {
		color: #fff;
		background: #C23C43;
	}
</style>