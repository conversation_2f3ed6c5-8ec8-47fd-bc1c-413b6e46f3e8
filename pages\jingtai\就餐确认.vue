<template>

	<view>
		<view class="weui-flex topviewnebg" style="aviewgn-items: center;justify-content: center; background: #fff;">
			<view class="tab-common10 weui-flex" style="width: 100%;">
				<view class="current" style="flex: 1;">待确认</view>
				<view style="flex: 1;">已确认</view>
			</view>
		</view>

		<!--待确认 -->
		<view style="display: none;">
			<view class="dine-view">
				<view class="weui-cell" style="border-bottom: 1px soviewd #EBEBEB;">
					<view class="weui-cell__bd">
						<view class="sel-person-viewst green-left">
							<view class="weui-flex " style="aviewgn-items: center;">5月份就餐收费</view>
						</view>

					</view>
					<view class="weui-cell__ft graytxt">2024-06-26 14:32 </text>
					</view>
				</view>
				<view class="dining-viewst">
					<view class="weui-cell">
						<view class="weui-cell__bd graytxt">
							就餐确认月份
						</view>
						<view class="weui-cell__ft">2024年11月
						</view>
					</view>
					<view class="weui-cell">
						<view class="weui-cell__bd graytxt">
							缺勤未就餐天数
						</view>
						<view class="weui-cell__ft">2天
						</view>
					</view>
				
					<view class="dining-days">
						去确认
					</view>
				</view>
			</view>
	        <view class="dine-view">
				<view class="weui-cell" style="border-bottom: 1px soviewd #EBEBEB;">
					<view class="weui-cell__bd">
						<view class="sel-person-viewst green-left">
							<view class="weui-flex " style="aviewgn-items: center;">5月份就餐收费</view>
						</view>

					</view>
					<view class="weui-cell__ft graytxt">2024-06-26 14:32 </text>
					</view>
				</view>
				<view class="dining-viewst">
					<view class="weui-cell">
						<view class="weui-cell__bd graytxt">
							就餐确认月份
						</view>
						<view class="weui-cell__ft">2024年11月
						</view>
					</view>
					<view class="weui-cell">
						<view class="weui-cell__bd graytxt">
							缺勤未就餐天数
						</view>
						<view class="weui-cell__ft">2天
						</view>
					</view>
				
					<view class="dining-days">
						去确认
					</view>
				</view>
			</view>
			
			<!-- 没有数据 -->
			<view class="nomesssage" style="display: none;">
				<image src="static/image/no_metadataimg.png" style="width: 197rpx;height: 200rpx;">
				</image>
				<text class="nomesssage-txt">当前没有待确认就餐情况！</text>
			</view>
		</view>
		<!--已确认 -->
		
		<view >
			<view class="def-search topviewnebg">
				<view class="weui-search-bar weui-search-bar_focusing" id="searchBar">
					<view class="weui-flex_center redtit">筛选<text class="droptxt">3</text>
						<image src="static/imagedownimg.png"
							style="width: 40rpx;height: 40rpx; margin-right: 5px;">
						</image>
					</view>

					<view class="weui-search-bar__form">
						<view class="weui-search-bar__box weui-flex" style="aviewgn-items: center; padding: 0 30rpx">
							<image src="https://osstbfile.tb-n.com/xcxscale/static/image/icon_search.png"
								style="width: 30rpx; height: 30rpx; margin-right: 25rpx"></image>
							<input placeholder-class="phcolor" class="weui-input weui-flex__item"
								placeholder="请输入请假人..." style="font-size: 14px" />
							<image src="https://osstbfile.tb-n.com/xcxscale/static/image/icon_close.png"
								style="width: 30rpx; height: 30rpx"></image>
						</view>
					</view>
					<text style="margin-left: 5px; color:#C23C43; font-size: 14px;">搜索</text>
				</view>

				<view class="filter-results redtit">筛选结果：<view class="weui-flexcen">本月 <i
							class="iconfont icon_close2"></i>
					</view>
					<view class="weui-flexcen">核对通过 <i class="iconfont icon_close2"></i></view>

				</view>
			</view>
			<view class="dine-view">
				
					<view class="weui-cell" style="border-bottom: 1px soviewd #EBEBEB;">
						<view class="weui-cell__bd">
							<view class="sel-person-viewst green-left">
								<view class="weui-flex " style="aviewgn-items: center;">5月份就餐收费 </view>
							</view>

						</view>
						<view class="weui-cell__ft graytxt">2024-06-26 14:32 </text>
						</view>
					</view>
					<view class="dining-viewst">
						<view class="weui-cell">
							<view class="weui-cell__bd graytxt">
							就餐确认结果
							</view>
							<view class="weui-cell__ft"><text class="greentxt">确认无误</text>
							</view>
						</view>

						<view class="weui-cell">
							<view class="weui-cell__bd graytxt">
								就餐确认月份
							</view>
							<view class="weui-cell__ft">2024年11月
							</view>
						</view>
						<view class="weui-cell">
							<view class="weui-cell__bd graytxt">
								缺勤未就餐天数
							</view>
							<view class="weui-cell__ft">2天
							</view>
						</view>
						<view class="dining-days">
							查看详情
						</view>
					</view>
			</view>

		<view class="dine-view">
			
				<view class="weui-cell" style="border-bottom: 1px soviewd #EBEBEB;">
					<view class="weui-cell__bd">
						<view class="sel-person-viewst green-left">
							<view class="weui-flex " style="aviewgn-items: center;">5月份就餐收费 </view>
						</view>
		
					</view>
					<view class="weui-cell__ft graytxt">2024-06-26 14:32 </text>
					</view>
				</view>
				<view class="dining-viewst">
					<view class="weui-cell">
						<view class="weui-cell__bd graytxt">
						就餐确认结果
						</view>
						<view class="weui-cell__ft"><text class="yellowtxt">有疑问，已留言</text>
						</view>
					</view>
		
					<view class="weui-cell">
						<view class="weui-cell__bd graytxt">
							就餐确认月份
						</view>
						<view class="weui-cell__ft">2024年11月
						</view>
					</view>
					<view class="weui-cell">
						<view class="weui-cell__bd graytxt">
							缺勤未就餐天数
						</view>
						<view class="weui-cell__ft">2天
						</view>
					</view>
					<view class="dining-days">
						查看详情
					</view>
				</view>
		</view>
		
			<!-- 没有数据 -->
			<view class="nomesssage" style="display: none;">
				<image src="static/imageno_metadataimg.png" style="width: 197rpx;height: 200rpx;">
				</image>
				<text class="nomesssage-txt">暂无消毒记录</text>
			</view>
			
				<!-- 没有数据 -->
						<view class="nomesssage" style="display: none;">
							<image src="static/imageno_metadataimg.png" style="width: 197rpx;height: 200rpx;">
							</image>
							<text class="nomesssage-txt">您还没有已确认记录！</text>
						</view>
		</view>


<!--筛选弹窗-->
<view class="shadow-con drop-down" >
	<view class="shadow-box">
		<view class="mgbot70">
			
			
		    <view class="screen-selbot">
				<view  class="filter-lt">筛选日期 </view>
				<view class="screen-sellist">
					<view><text class="current">今天</text></view>
					<view><text>昨天</text></view>
					<view><text>本周</text></view>
					<view><text>上周</text></view>
					<view><text>本月</text></view>
					<view><text>上月</text></view>
					<view><text>最近7天</text></view>
					<view><text>3个月</text></view>
					<view><text>自定义</text></view>
				</view>
			</view>
			<view class="custom-time weui-flex_center" style="clear: both;margin: 20px 20px 0 15px;">
									    <view class="custom-list weui-flex_center"><image src="static/image/timeico.png"  style="width: 10px; height: 10px;"></image> <input type="text" class="time-input" placeholder="选择日期" > </view>
										<text>~</text>
							    <view class="custom-list weui-flex_center"><image src="static/image/timeico.png"  style="width: 10px; height: 10px;"></image> <input type="text" class="time-input" placeholder="选择日期" > </view>
			
			</view>
			<view class="screen-selbot">
				<view  class="filter-lt">审批状态 </view>
				<view class="screen-sellist">
					<view><text class="current">待审批</text></view>
					<view><text>审批通过</text></view>
					<view><text>审批不通过</text></view>
				
					
				</view>
			</view>
			
		
		</view>
	</view>
	<view style="padding: 15px 0;position: fixed;bottom: 0;width: 90%;z-index: 9; background: #fff; right: 0;"> 
		<!--保存发布按钮-->
		<view class="weui-flex" style="margin:0px 15px; border: 1px solid #C23C43;border-radius:40px; background: #C23C43;"> <a href="javascript:;" class="weui-btn weui-flex__item" style="background: #FFECEF;border-radius:40px 0 40px 40px;color: #C23C43;">重置</a> <a href="javascript:;" class="weui-btn" style="background: #C23C43;border-radius:0 40px 40px 0;margin: 0;width:49%;">确认 </a> </view>
	</view>
</view>

	</view>

</template>

<script>
</script>

<style>
	page {
		background: #F8F8F8;
	}

	.weui-cell {
		padding: 0px 15px;
	}

	.dine-view {
		background: #fff;
		margin-top: 8px;
		position: relative;
	}

	.approved-img {
		top: 60rpx;
		position: absolute;
		left: 300rpx;
	}

	.weui-cell__bd {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell__ft {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell:before {
		right: 0px;
		left: 0;
		border-top: none;
	}

	.weui-cell__ft.graytxt,
	.weui-cell__bd.graytxt {
		color: #999999;
	}

	.dining-viewst {
		padding: 10px 0;
		margin: 0 15px;
	}

	.dining-viewst .weui-cell {
		padding: 5px 0px;
	}


	.dining-days {
		font-size: 28rpx;
		color: #C23C43;
		text-aviewgn: right;
		text-decoration: underviewne;
		cursor: pointer;
	}

	.filter-results {
		background: #fff;
		padding: 0 15px 10px 15px;
		font-size: 24rpx;
	}

	.filter-results view {
		display: inviewne-block;
		background: #FFEDEF;
		border-radius: 10rpx;
		margin-right: 10px;
		color: #C23C43;
		padding: 5rpx 10rpx;
		font-size: 22rpx;
		font-weight: normal;
		cursor: pointer;
	}

	.icon_close2:after {
		color: #C23C43;
		font-size: 16rpx;
		vertical-aviewgn: middle;
		margin-left: 10px;
	}

	.droptxt {
		padding: 0 8rpx;
		height: 30rpx;
		viewne-height: 30rpx;
		background: #C23C43;
		border-radius: 12px;
		font-size: 20rpx;
		color: #FFFFFF;
		text-aviewgn: center;
		margin-left: 3px;
		font-weight: normal;
	}

	.costtxt {
		background: #FFECEF;
		border-radius: 10rpx;
		color: #C23C43;
		padding: 5rpx 10rpx;
		margin-left: 10px;
		font-size: 24rpx;

	}
</style>