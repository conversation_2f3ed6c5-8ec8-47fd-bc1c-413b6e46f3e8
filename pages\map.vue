<template>
	<view>
		<!--搜索-->
		<view class="weui-search-bar weui-flex">
			<view class="weui-flex" style="align-items: center;font-size: 28rpx;color: #333333;margin: 0 24rpx 0 0;">
				<image src="../static/image/icon_local.png" style="width: 20rpx;height: 24rpx;margin-right: 10rpx;">
				</image>
				<picker @change="checkcity" :range="arrcity" range-key="strarea">
					<view class="uni-input">{{objcity[objlocal.adcode]}}</view>
				</picker>
				<image src="../static/image/arrow_down.png" style="width: 16rpx;height: 9rpx;margin-left: 10rpx;">
				</image>
			</view>
			<view class="weui-search-bar__form weui-flex__item">
				<view class="weui-search-bar__box weui-flex" style="padding: 14rpx 28rpx;">
					<image src="../static/image/icon_search.png" style="width: 22rpx;height: 22rpx;margin-right: 12rpx;"></image>
					<input type="text" class="weui-search-bar__input weui-flex__item" placeholder="请输入关键字搜索" style="font-size: 11px;" @confirm="searchbyname" @input="insqname" :avalue="strsqname" />
				</view>
			</view>
			<view class="weui-flex" style="align-items: center;font-size: 24rpx;color: #6f9eff;" @click="againlocation">
				<image src="../static/image/icon_local2.png" style="width: 28rpx;height: 28rpx;margin: 0 10rpx 0 30rpx;"></image>
				<view>重新定位</view>
			</view>
		</view>
		<view style="top: 50px;">
			<map :style="'width: 100%; height:'+ mapheight+'px;'" :latitude="latitude" :longitude="longitude" :markers="covers" scale="13" @markertap="tapindex">
			</map>
		</view>

		<cover-view style="position: fixed;bottom: 400rpx;right: 0rpx;width: 50px;height: 50px;background-color: #fff;border-radius: 25px;" @click="gettoadd" v-if="objmarker.id">
			<cover-image src="/static/image/icon_local7.png" style="width: 30px;height: 30px;margin: 10px;">
			</cover-image>
		</cover-view>

		<!--底部定位-->
		<view class="localbtm weui-flex">
			<view class="weui-flex__item">
				<view style="font-size: 36rpx;color: #333333;margin-bottom: 10rpx;">{{objmarker.title || ''}}</view>
				<view class="font12gray">{{objmarker.address || ''}}</view>
				<view class="font12gray">距离{{objmarker.distance || 0}}公里</view>
			</view>
			<block v-if="formpage=='addbaby'||formpage=='index'">
				<button v-if="objmarker.id" class="fullbtn weui-flex_center noafter" style="width: 180rpx;font-size: 26rpx;" @click="changesq(objmarker.id,objmarker.title,objmarker.areacode)">
					<image src="../static/image/icon_local3.png" style="width: 26rpx;height: 32rpx;margin-right: 15rpx;">
					</image>
					<text>选中</text>
				</button>
			</block>
			<block v-else>
				<button class="fullbtn weui-flex_center noafter" style="width: 180rpx;font-size: 26rpx;display: none;">
					<image src="../static/image/icon_local3.png" style="width: 26rpx;height: 32rpx;margin-right: 15rpx;">
					</image>
					<text>到这里</text>
				</button>
				<button class="fullbtn weui-flex_center noafter" style="width: 180rpx;font-size: 26rpx;" @click="tocommunity(objmarker.id,objmarker.title,objmarker.areacode)" v-if="objmarker.id">
					<!-- 	<image src="../static/image/icon_local3.png" style="width: 26rpx;height: 32rpx;margin-right: 15rpx;">
					</image> -->
					<text>进入社区</text>
				</button>
			</block>
		</view>

	</view>
</template>

<script>
	// var transform = require('wgs2mars');
	var app = getApp();
	var _this;
	export default {
		data() {
			return {
				title: 'Hello',
				showCoverView: true,
				latitude: 0,
				longitude: 0,
				covers: [],
				objmarker: {},
				formpage: "",
				strsqname: "",
				objcity: {},
				objlocal: {},
				arrcity: [],
				mapheight: 620
			}
		},
		onLoad(options) {
			_this = this;
			uni.getSystemInfo({
				success: function(res) {
					_this.mapheight = res.windowHeight;
					//#ifdef APP
					_this.mapheight = res.windowHeight - 150;
					// #endif 
				}
			});
			_this.formpage = options.formpage || "";
			_this.getnow(_this.getCommunity);
			_this.getCity();
		},
		methods: {

			//todo 1.定位 2.查询附近所有的站点 3.点击某个站点进入首页

			tapindex: function(e) {
				var markerId = e.detail.markerId;
				var arrmarker = _this.$data.covers;
				var objmarker = {};
				for (var i = 0; i < arrmarker.length; i++) {
					if (arrmarker[i].id == markerId) {
						objmarker = arrmarker[i];
						break;
					}
				}
				var distance = _this.getDistance(_this.$data.latitude, _this.$data.longitude, objmarker.latitude, objmarker.longitude);
				objmarker['distance'] = distance;
				_this.$data.objmarker = objmarker;
				// uni.switchTab({
				// 	url: "/pages/tabBar/lewan/index"
				// })
			},
			// 获取定位
			getnow(cb) {
				uni.getLocation({
					type: 'gcj02', //返回可以用于uni.openLocation的经纬度
					success: (res) => {
						let that = this
						const latitude = res.latitude;
						const longitude = res.longitude;
						_this.$data.latitude = res.latitude;
						_this.$data.longitude = res.longitude;
						uni.request({
							header: {
								"Content-Type": "application/text"
							},
							//注意:这里的key值需要高德地图的 web服务生成的key  只有web服务才有逆地理编码
							url: 'https://apis.map.qq.com/ws/geocoder/v1/?location=' + res.latitude +
								',' + res.longitude + '&key=' + app.globalData.mapkey,
							success(re) {
								var data = re.data;
								if (re.statusCode === 200) {
									var objlocal = {};
									var objadd = data.result;
									objlocal['address'] = objadd.address;
									objlocal['adcode'] = objadd.ad_info.adcode;
									if (!_this.$data.objcity[objadd.ad_info.adcode]) {
										var strarea = objadd.address_component.province + ' ' + objadd
											.address_component.city + ' ' + objadd.address_component
											.district;
										_this.$data.objcity[objadd.ad_info.adcode] = objadd
											.address_component.district;
										_this.$data.arrcity.push({
											areacode: objadd.ad_info.adcode,
											province: objadd.address_component.province,
											city: objadd.address_component.city,
											district: objadd.address_component.district,
											strarea: strarea
										})
									}
									_this.$data.objlocal = objlocal;
									cb && cb();
								} else {
									uni.msg("获取信息失败，请重试！")
								}
							}
						});
					},
					fail(e, v, b) {
						// GPS已授权 微信定位未授权
						cb && cb();
						if (e.errMsg.indexOf("缺少定位权限") >= 0) {
							uni.showModal({
								title: '未打开定位',
								content: '找不到您的位置，请开启定位',
								confirmText: '确定',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										uni.openSetting(); // 打开地图权限设置
									}
								}
							});
						}
					}
				});
			},
			getCity() {
				uni.sm(function(re, err) {
					if (!err) {
						var arrdata = re || [];
						var arrcity = [];
						var objcity = {};
						for (var i = 0; i < arrdata.length; i++) {
							var obj = arrdata[i];
							if (!objcity[obj.areacode]) {
								objcity[obj.areacode] = obj.district;
							}
							obj['strarea'] = obj.province + ' ' + obj.city + ' ' + obj.district;
							arrcity.push(obj);
						}
						_this.$data.objcity = objcity;
						_this.$data.arrcity = arrcity;
					}
				}, ['appcode.getRegisterCityInfo']);
			},
			checkcity(e) {
				var index = e.detail.value;
				var objarea = _this.$data.arrcity[index];
				_this.$data.objlocal.adcode = objarea.areacode;
				_this.$data.latitude = objarea.lat;
				_this.$data.longitude = objarea.lng;
				_this.getCommunity();
			},
			getCommunity() {
				var swhere = "";
				if (_this.$data.strsqname) {
					swhere = " and communityname like '%" + _this.$data.strsqname + "%'";
				}
				uni.sm(function(re, err) {
					if (!err) {
						var arrdata = [{
							id: 0,
							title: '您的位置',
							latitude: _this.$data.latitude,
							longitude: _this.$data.longitude,
							iconPath: '../static/image/icon_local2.png',
							address: _this.$data.objlocal.address
						}];
						for (var i = 0; i < re.length; i++) {
							var obj = re[i];
							arrdata.push({
								id: obj.id,
								title: obj.communityname,
								address: obj.address,
								phone: obj.phone,
								areacode: obj.areacode,
								latitude: obj.qqlat,
								longitude: obj.qqlng,
								//#ifndef APP 
								iconPath: '../static/image/icon_local6.png',
								// #endif 
								//#ifdef APP 
								iconPath: '../static/image/icon_local66.png',
								// #endif 
								remark: obj.remark
							});
						}
						_this.$data.covers = arrdata;
					}
				}, ['appcode.getCommunityByAdCode', _this.$data.objlocal.adcode, swhere])
			},
			getDistance(lat1, lng1, lat2, lng2) {
				var radLat1 = lat1 * Math.PI / 180.0;
				var radLat2 = lat2 * Math.PI / 180.0;
				var a = radLat1 - radLat2;
				var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
				var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
					Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
				s = s * 6378.137; // EARTH_RADIUS;
				s = Math.round(s * 10000) / 10000;
				return s;
			},
			tocommunity(sqid, communityname, areacode) {
				app.globalData.communityname = communityname;
				app.globalData.areacode = areacode;
				app.globalData.sqid = sqid;
				uni.reLaunch({
					url: "/pages/tabBar/lewan/index?sqid=" + sqid
				});
			},
			againlocation() {
				_this.getnow(_this.getCommunity);
			},
			insqname(e) {
				_this.$data.strsqname = e.detail.value;
			},
			searchbyname(e) {
				_this.getCommunity();
			},
			// #ifndef APP-PLUS
			gettoadd() {
				var objmarker = _this.$data.objmarker;
				var latitude = Number(objmarker.latitude),
					longitude = Number(objmarker.longitude),
					name = objmarker.address;
				// 打开地图并导航
				uni.openLocation({
					latitude,
					longitude,
					name,
					fail: () => {
						uni.showModal({
							content: '打开地图失败,请重试'
						})
					}
				})
			},
			// #endif
			// #ifdef APP-PLUS
			gettoadd(item) {
				var url = '';
				var objmarker = _this.$data.objmarker;
				const address = objmarker.address;
				const latitude = objmarker.latitude;
				const longitude = objmarker.longitude;
				if (plus.os.name == 'Android') {
					var hasBaiduMap = plus.runtime.isApplicationExist({
						pname: 'com.baidu.BaiduMap',
						action: 'baidumap://'
					});
					var hasAmap = plus.runtime.isApplicationExist({
						pname: 'com.autonavi.minimap',
						action: 'androidamap://'
					});
					var urlBaiduMap = 'baidumap://map/marker?location=' + latitude + ',' + longitude + '&title=' +
						encodeURIComponent(address) + '&src=com.bailb.hbb';
					var urlAmap = 'androidamap://viewMap?sourceApplication=com.bailb.hbb&poiname=' + encodeURIComponent(
						address) + '&lat=' + latitude + '&lon=' + longitude + '&dev=0';
					if (hasAmap && hasBaiduMap) {
						plus.nativeUI.actionSheet({
							title: '选择地图应用',
							cancel: '取消',
							buttons: [{
								title: '百度地图'
							}, {
								title: '高德地图'
							}]
						}, function(e) {
							switch (e.index) {
								case 1:
									plus.runtime.openURL(urlBaiduMap);
									break;
								case 2:
									plus.runtime.openURL(urlAmap);
									break;
							}
						});
					} else if (hasAmap) {
						if (uni.getSystemInfoSync().platform == 'android') {
							this.$showModal({
									title: '公告',
									content: '是否打开“高德地图”进行导航？',
									delCancel: false
								})
								.then(res => {
									plus.runtime.openURL(urlAmap);
								})
								.catch(res => {});
						} else {
							uni.showModal({
								title: '公告',
								content: '是否打开“高德地图”进行导航？',
								success: res => {
									if (res.confirm) {
										plus.runtime.openURL(urlAmap);
									}
								}
							});
						}
					} else if (hasBaiduMap) {
						if (uni.getSystemInfoSync().platform == 'android') {
							this.$showModal({
									title: '公告',
									content: '是否打开“百度地图”进行导航？',
									delCancel: false
								})
								.then(res => {
									plus.runtime.openURL(urlBaiduMap);
								})
								.catch(res => {});
						} else {
							uni.showModal({
								title: '公告',
								content: '是否打开“百度地图”进行导航？',
								success: res => {
									if (res.confirm) {
										plus.runtime.openURL(urlBaiduMaps);
									}
								}
							});
						}
					} else {
						//如果是国外应用，应该优先使用这个，会启动google地图。这个接口不能统一坐标系，进入百度地图时会有偏差
						url = 'geo:' + latitude + ',' + longitude + '?q=' + encodeURIComponent(address);
						if (uni.getSystemInfoSync().platform == 'android') {
							this.$showModal({
									title: '公告',
									content: '是否打开“GoogleMap”进行导航？',
									delCancel: false
								})
								.then(res => {
									plus.runtime.openURL(url);
								})
								.catch(res => {});
						} else {
							uni.showModal({
								title: '公告',
								content: '是否打开“GoogleMap”进行导航？',
								success: res => {
									if (res.confirm) {
										plus.runtime.openURL(url);
									}
								}
							});
						}
					}
				} else {
					// iOS上获取本机是否安装了百度高德地图，需要在manifest里配置，在manifest.json文件app-plus->distribute->apple->urlschemewhitelist节点下添加（如urlschemewhitelist:["iosamap","baidumap"]）
					plus.nativeUI.actionSheet({
						title: '选择地图应用',
						cancel: '取消',
						buttons: [{
							title: 'Apple地图'
						}, {
							title: '百度地图'
						}, {
							title: '高德地图'
						}]
					}, function(e) {
						switch (e.index) {
							case 1:
								url = 'http://maps.apple.com/?q=' + encodeURIComponent(address) + '&ll=' +
									latitude +
									',' + longitude + '&spn=0.008766,0.019441';
								break;
							case 2:
								url = 'baidumap://map/marker?location=' + latitude + ',' + longitude + '&title=' +
									encodeURIComponent(address) + '&src=com.bailb.hbb';
								break;
							case 3:
								url = 'iosamap://viewMap?sourceApplication=com.bailb.hbb&poiname=' +
									encodeURIComponent(address) + '&lat=' + latitude + '&lon=' + longitude +
									'&dev=1';
								break;
							default:
								break;
						}
						if (url != '') {
							plus.runtime.openURL(url, e => {
								plus.nativeUI.alert('本机未安装指定的地图应用');
							});
						}
					});
				}
			},
			// #endif
			changesq(sqid, communityname, areacode) {
				app.globalData.communityname = communityname;
				app.globalData.areacode = areacode;
				app.globalData.sqid = sqid;
				var PrePage = uni.getPrePage();
				PrePage.checkedSq(sqid, communityname);
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.weui-search-bar {
		/* #ifndef APP */
		z-index: 999;
		margin: 20rpx 30rpx;
		border-radius: 10rpx;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		/* #endif */
		/* #ifdef APP */
		margin-bottom: 0;
		/* #endif */
	}

	.localbtm {
		align-items: center;
		background: #ffffff;
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		/* #ifndef APP */
		margin: 30rpx;
		/* #endif */
		/* #ifdef APP */
		height: 60px;
		/* #endif */
		border-radius: 10rpx;
		padding: 30rpx 30rpx;
	}
</style>
