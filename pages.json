{
	"pages": [{
		"path": "pages/start",
		"style": {
			"navigationBarTitleText": "启动页",
			"enablePullDownRefresh": false
		}
	}, {
		"path": "pages/tabBar/home/<USER>",
		"style": {
			"navigationBarTitleText": "首页",
			"enablePullDownRefresh": false
		}
	}, {
		"path": "pages/tabBar/message/index",
		"style": {
			"navigationBarTitleText": "消息",
			"enablePullDownRefresh": false
		}
	}, {
		"path": "pages/tabBar/mine/index",
		"style": {
			"navigationBarTitleText": "我的",
			"enablePullDownRefresh": false
		}
	}, {
		"path": "pages/agree",
		"style": {
			"navigationBarTitleText": "服务协议",
			"enablePullDownRefresh": false
		}
	}],
	"subPackages": [{
			"root": "pages/login",
			"pages": [{
				"path": "login",
				"style": {
					"navigationBarTitleText": "登录",
					"enablePullDownRefresh": false
				}
			}]
		},
		{
			"root": "pages/home",
			"pages": [{
				"path": "notice_detail",
				"style": {
					"navigationBarTitleText": "通知公告",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "rank_list",
				"style": {
					"navigationBarTitleText": "排行榜",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "peic_list",
				"style": {
					"navigationBarTitleText": "陪餐",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "yey_search",
				"style": {
					"navigationBarTitleText": "搜索学校",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "peic_detail",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "suplier_list",
				"style": {
					"navigationBarTitleText": "供应商资质",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "suplier_detail",
				"style": {
					"navigationBarTitleText": "资质详情",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "askleave_add",
				"style": {
					"navigationBarTitleText": "学生请假",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "askleave_addwebview",
				"style": {
					"navigationBarTitleText": "学生请假",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "recipe_list",
				"style": {
					"navigationBarTitleText": "一日食谱",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "recipe_listwebview",
				"style": {
					"navigationBarTitleText": "一日食谱",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "chooseImage",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "meals_fee_list",
				"style": {
					"navigationBarTitleText": "就餐缴费",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "meals_fee",
				"style": {
					"navigationBarTitleText": "缴费",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "meals_confirm_list",
				"style": {
					"navigationBarTitleText": "就餐确认",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "meals_confirm",
				"style": {
					"navigationBarTitleText": "就餐确认",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "meals_view",
				"style": {
					"navigationBarTitleText": "查看缴费",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "meals_feedback",
				"style": {
					"navigationBarTitleText": "就餐疑问",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "meals_uploadimg",
				"style": {
					"navigationBarTitleText": "上传凭证",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "meals_detail",
				"style": {
					"navigationBarTitleText": "费用详情",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "certpub",
				"style": {
					"navigationBarTitleText": "证照公示",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "clean_list",
				"style": {
					"navigationBarTitleText": "清洗消毒",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "clean_detail",
				"style": {
					"navigationBarTitleText": "清洗消毒详情",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "kitchen_monitor",
				"style": {
					"navigationBarTitleText": "后厨监控",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "train_list",
				"style": {
					"navigationBarTitleText": "培训记录",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "train_detail",
				"style": {
					"navigationBarTitleText": "培训记录详情",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "food_rubbish_list",
				"style": {
					"navigationBarTitleText": "餐厨垃圾",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "food_rubbish_detail",
				"style": {
					"navigationBarTitleText": "餐厨垃圾详情",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "foodalleadmin_webview",
				"style": {
					"navigationBarTitleText": "过敏填报",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "videomonitor_webview",
				"style": {
					"navigationBarTitleText": "后厨监控",
					"enablePullDownRefresh": false
				}

			}]
		},
		{
			"root": "pages/message",
			"pages": [{
				"path": "消息",
				"style": {
					"navigationBarTitleText": "消息",
					"enablePullDownRefresh": false
				}
			}]
		},
		{
			"root": "pages/mine",
			"pages": [{
				"path": "help_center",
				"style": {
					"navigationBarTitleText": "帮助中心",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "feedback",
				"style": {
					"navigationBarTitleText": "意见反馈",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "mybabys",
				"style": {
					"navigationBarTitleText": "我的孩子",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "myinfo",
				"style": {
					"navigationBarTitleText": "个人信息",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "student_add",
				"style": {
					"navigationBarTitleText": "添加孩子",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "safe_verify",
				"style": {
					"navigationBarTitleText": "安全验证",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "student_share_add",
				"style": {
					"navigationBarTitleText": "添加孩子",
					"enablePullDownRefresh": false
				}
			}]
		}
	],
	"preloadRule": { //分包预载配置
		"pages/tabBar/home/<USER>": {
			"network": "all",
			"packages": ["__APP__"]
		},
		"pages/tabBar/message/index": {
			"network": "all",
			"packages": ["pages/message"]
		}
	},
	"easycom": {
		"autoscan": true,
		"custom": {
			"fui-(.*)": "@/components/firstui/fui-$1/fui-$1.vue"
		}
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "中小学家长端",
		"navigationBarBackgroundColor": "#FFF",
		"backgroundColor": "#F8F8F8",
		"app-plus": {
			"background": "#efeff4"
		}
	},
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "#C23C43",
		"borderStyle": "black",
		"backgroundColor": "#F8F8F8",
		"list": [{
				"pagePath": "pages/tabBar/home/<USER>",
				"iconPath": "static/tabbar/index.png",
				"selectedIconPath": "static/tabbar/index_HL.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/tabBar/message/index",
				"iconPath": "static/tabbar/message.png",
				"selectedIconPath": "static/tabbar/message_HL.png",
				"text": "消息"
			},
			{
				"pagePath": "pages/tabBar/mine/index",
				"iconPath": "static/tabbar/mine.png",
				"selectedIconPath": "static/tabbar/mine_HL.png",
				"text": "我的"
			}
		]
	},

	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "start", //模式名称
			"path": "pages/start", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	},
	"h5": {
		"devServer": {
			"disableHostCheck": true,
			"proxy": {
				"/map-api": {
					"target": "https://map.qq.com",
					"changeOrigin": true,
					"secure": false
				}
			}
		}
	}
}
