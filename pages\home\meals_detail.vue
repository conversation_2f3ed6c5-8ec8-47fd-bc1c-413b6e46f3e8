<template>
	<view class="marbot10">
		<view class="yllist">
			<view class="weui-cell" style="background: #FFEDEF;">
				<view class="weui-cell__bd">
					<view class="sel-person-list green-left">
						<view class="weui-flex " style="align-items: center;">12月</view>
					</view>
				</view>
			</view>
			<view>
				<!-- <uni-calendar 
					:insert="true"
					:lunar="true"
					@change="change"
					 /> -->
				<wu-calendar 
					:date="beginday"
					:useToday="false"
					:insert="true" 
					:lunar="true" 
					:monthShowCurrentMonth="true"
					slideSwitchMode="none"
					:selected="selected"
					:itemHeight="60"></wu-calendar>
			</view>
		</view>
		<view v-for="(item, i) in arrleave" :key="i" class="yllist">
			<view class="weui-cell" style="background: #FFEDEF;">
				<view class="weui-cell__bd">
					<view class="sel-person-list green-left">
						<view class="weui-flex " style="align-items: center;">{{item.monthday}}</view>
						<view class="graytxt">{{item.lunarday}}</view>
					</view>
				</view>
			</view>
			<view class="weui-cell" style="padding: 10px 15px;">
				<view class="weui-cell__bd boldtxt">
					请假未就餐
				</view>
				<view class="weui-cell__ft" style="color: #C23C43;">¥ {{item.fee}} <text style="margin-left: 10px;">未收取</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js");
	var app = getApp();
	var pagesize = 10;

	export default {
		data() {
			return {
				myinfo: app.globalData.objuserinfo,
				objrecord: {},
				beginday: '',
				selected: [],
				arrleave: []
			}
		},
		onLoad: function(params) {
			var recordid = params.recordid;
			this.initData(recordid);
		},
		onShow() {

		},
		methods: {
			initData(recordid) {
				var _this = this;
				uni.sm(function(re, err) {
					if (err) {
						return uni.msg(err.msg | '查询出错');
					}
					console.log('re:', re);
					//m.id,m.name,m.arrdate,m.startdate,m.enddate,r.id as recordid,m.arrdate,m.startdate,m.enddate,r.leavedays,parentstatus,charge_no,pay_img,mealsdetail
					_this.objrecord = re;
					var selected = [];
					var arrleave = [];
					var arrdate = re.arrdate;
					var mealsdetail = re.mealsdetail ? JSON.parse(re.mealsdetail) : {};
					if(arrdate){
						arrdate = arrdate.split(",");
						_this.beginday = arrdate[0];
						for (var i = 0; i < arrdate.length; i++) {
							var date = arrdate[i];
							var day = parseInt(date.substring(8,10));
							var text = re['d' + day] == 0 ? '请假' : '';
							var color = re['d' + day] == 0 ? '#ff2a3f' : '#6ac695';
							var strday = '' + day;
							if(day < 10){
								strday = "0" + strday;
							}
							var fee = mealsdetail['d' + strday] ? mealsdetail['d' + strday].cost : 0;
							selected.push({
								date: date,
								info: '￥' + fee,
								infoColor: color,
								topInfo: text,
								topInfoColor: color
							})
							if(!re['d' + day]){//请假
								var monthday = date.substring(5).replace("-", "月") + "日";
								var objLunar = util.getLunarObj(date);
								var lunarday = objLunar.lunarmonth + objLunar.lunarday;
								arrleave.push({
									monthday: monthday,
									lunarday: lunarday,
									fee: fee
								})
							}
						}
						_this.selected = selected;
						_this.arrleave = arrleave;
					}
					
				}, ["meals.selectbyrecorddetailid", recordid], {
					route: uni.svs.zxx
				})
			}
			

		}
	}
</script>



<style>
	page {
		background: #fff;
	}

	.weui-cell {
		padding: 0px 15px;
	}


	.weui-cell__bd {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell__ft {
		font-size: 28rpx;
		color: #303030;

	}

	.weui-cell:before {
		right: 0px;
		left: 0;
	}
</style>
