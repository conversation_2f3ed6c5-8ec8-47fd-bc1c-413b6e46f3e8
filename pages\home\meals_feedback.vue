<template>
	<view>
		<view class="remarks-list">
			<view class="weui-cell" style="padding:0px;">
				<view class="weui-cell__bd">家长留言</view>

			</view>
			<view class="inputbg">
				<textarea class="uni-input" @input="gettext" style="width: 100%;" placeholder-style="color:#a7a8a9;"
					placeholder="请输入留言内容" /> </textarea>
			</view>
		</view>
		<!-- 提交按钮 -->
		 <view class="button-bottom " style="height: 160rpx;box-shadow: none;border-top:none;">
		     <button class="common-btn noafter" @click="btnConfirm">提交</button>
		</view>
	</view>
</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js");
	var app = getApp();
	var pagesize = 10;

	export default {
		data() {
			return {
				myinfo: app.globalData.objuserinfo,
				recordid: 0,
				text: ""
			}
		},
		onLoad: function(params) {
			this.recordid = params.recordid;
		},
		onShow() {
		},
		methods: {
			gettext(e){
				this.text = e.detail.value;
			},
			btnConfirm(){//无疑问，确认
				if(this.text.length > 200){
					return uni.msg("文字过多，请精简");
				}
				uni.sm(function(re,err){
					if (err) {
						return uni.msg(err);
					}
					var prepage = uni.getPrePage(-2);
					prepage.refresh(2);
					uni.navigateBack({
						delta: 2
					});
				}, ["meals.feedbackmeal", this.text, this.recordid], {route: uni.svs.zxx})
			}

		}
	}
</script>

<style>
	page {
		background: #fff;
	}

	.weui-cell {
		padding: 15px;
	}

	.weui-cell .weui-cell__bd {
		font-size: 14px;
		color: #999999;
	}

	.weui-cell .weui-cell__ft {
		font-size: 14px;
		color: #303030;
	}

	.weui-cell:before {
		right: 15px;
	}

	.weui-cells:after {
		right: 15px;
		left: 15px;
	}

	.inputbg {
		text-align: left;
		background: #F8F8F8;
		border-radius: 3px;
		padding: 5px 10px;
		font-size: 24rpx;
		color: #303030;
		line-height: 60rpx;
		height: 200rpx;
		margin-top: 10px;

	}

	.uni-input {
		text-align: left;
		font-size: 24rpx;
		color: #999999;
		line-height:40rpx;
		height: 200rpx;
	}

	.remarks-list {
		background: #fff;
		padding: 15px;
	}
</style>