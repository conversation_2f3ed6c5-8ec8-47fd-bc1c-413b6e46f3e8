<template>
	<view>

		<view class="weui-cells weui-cells_after-title">

			<view class="weui-cell">
				<view class="weui-cell__bd">日期</view>
				<view class="weui-cell__ft weui-flex weui-cell__ft_in-access" style="align-items: center;">
					{{peicObj.rdate}}</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd">餐别</view>
				<view class="weui-cell__ft weui-cell__ft_in-access">{{canbieObj[peicObj.canbie]}}</view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd">
					<view style="width:70px;">陪餐人员</view>
				</view>

				<view class="weui-cell__ft ">
					<block v-if="peicObj.pc_userinfo" v-for="(pcuser, index2) in toJSONArr(peicObj.pc_userinfo)"
						:key="index2">
						<view style="text-align: right; =">{{pcuser.departname}}-{{pcuser.zwname}}-{{ pcuser.empname }}
						</view>
					</block>
				</view>
			</view>




		</view>


		<view class="remarks-list">

			<view class="font14 martop" style="color: #999999;">陪餐图片</view>
			<view class="weui-uploader__bd" style="margin: 10px 0 0 0;">
				<view class="weui-uploader__files" style="padding-top: 5px;">
					<view v-for="(oneimg, index) in splitToArr(peicObj.pc_imgs)" :key="index"
						class="weui-uploader__file">
						<image :src="ossPrefix+oneimg" class="img-upload"></image>
					</view>
				</view>
			</view>
			<!-- <view class="weui-flex_center" style="font-size: 24rpx;color: #999999;">暂无图片！</view> -->
		</view>

		<view v-if="peicObj.hashome" class="remarks-list">
			<view class="weui-cell" style="padding:0px;">
				<view class="weui-cell__bd">家长代表陪餐</view>
				<view class="weui-cell__ft weui-flex weui-cell__ft_in-access" style="align-items: center;">
					{{peicObj.hashomedetail||'无'}}</view>
			</view>

			<view class="inputbg" style="height:100rpx; ">
				<view>{{ peicObj.beizhu }}</view>
			</view>
		</view>
		<view class="remarks-list">
			<view class="font14 martop" style="color: #999999;">陪餐记录表</view>


			<view class="total-tab">
				<uni-table border>
					<!-- 表头行 -->
					<uni-tr>
						<uni-th align="center" style="width: 90rpx;">序号</uni-th>
						<uni-th align="center" style="width: 120rpx;">检查项</uni-th>
						<uni-th align="center">检查内容</uni-th>
						<uni-th align="center">
							<view style="width: 100rpx;">检查情况</view>
						</uni-th>

					</uni-tr>
					<!-- 表格数据行 -->
					<uni-tr>
						<uni-td align="center">1</uni-td>
						<uni-td align="center">望</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t1.name}}</uni-td>
						<uni-td align="center">{{ peicObj.pcrd_t1?'是':'否' }}</uni-td>

					</uni-tr>
					<uni-tr>
						<uni-td align="center">2</uni-td>
						<uni-td align="center">望</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t2.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t2?'是':'否'}}</uni-td>

					</uni-tr>
					<uni-tr>
						<uni-td align="center">3</uni-td>
						<uni-td align="center">望</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t3.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t3?'是':'否'}}</uni-td>

					</uni-tr>
					<uni-tr>
						<uni-td align="center">4</uni-td>
						<uni-td align="center">望</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t4.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t4?'是':'否'}}</uni-td>

					</uni-tr>
					<uni-tr>
						<uni-td align="center">5</uni-td>
						<uni-td align="center">望</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t5.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t5?'是':'否'}}</uni-td>

					</uni-tr>
					<uni-tr>
						<uni-td align="center">6</uni-td>
						<uni-td align="center">望</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t6.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t6?'是':'否'}}</uni-td>

					</uni-tr>
					<uni-tr>
						<uni-td align="center">7</uni-td>
						<uni-td align="center">望</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t7.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t7?'是':'否'}}</uni-td>

					</uni-tr>
					<uni-tr>
						<uni-td align="center">8</uni-td>
						<uni-td align="center">望</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t8.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t8?'是':'否'}}</uni-td>

					</uni-tr>
					<uni-tr>
						<uni-td align="center">9</uni-td>
						<uni-td align="center">望</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t9.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t9?'是':'否'}}</uni-td>

					</uni-tr>
					<uni-tr>
						<uni-td align="center">10</uni-td>

						<uni-td align="center">闻</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t10.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t10?'是':'否'}}</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td align="center">11</uni-td>

						<uni-td align="center">问</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t13.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t13}}</uni-td>
					</uni-tr>
					<uni-tr>
						<uni-td align="center">12</uni-td>

						<uni-td align="center">尝</uni-td>
						<uni-td align="left">{{wentiObj.pcrd_t11.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t11?'是':'否'}}</uni-td>
					</uni-tr>

					<uni-tr>
						<uni-td align="center">13</uni-td>
						<uni-td align="center">尝</uni-td>

						<uni-td align="left">{{wentiObj.pcrd_t12.name}}</uni-td>
						<uni-td align="center">{{peicObj.pcrd_t12?'是':'否'}}</uni-td>
					</uni-tr>


					<uni-tr>
						<uni-td align="center">14</uni-td>

						<uni-td align="left">{{wentiObj.pcrd_t14.name}}</uni-td>
						<uni-td align="left" colspan="2">{{ peicObj.pcrd_t14 }}</uni-td>

					</uni-tr>


				</uni-table>

			</view>

		</view>
	</view>
</template>


<script>
	var app = getApp();

	export default {
		data() {
			return {
				ossPrefix: app.globalData.ossPrefix,
				peicObj: {},
				toJSONArr: function(jsonstr) {
					return !jsonstr ? [] : JSON.parse(jsonstr);
				},
				splitToArr: function(str) {
					return !str ? [] : str.split(",");
				},
				canbieObj: {
					"1": "早餐",
					"2": "加餐",
					"3": "午餐",
					"4": "午点",
					"5": "晚餐",
					"6": "晚点"
				},
				wentiObj: {
					"pcrd_t1": {
						name: "学生餐与食谱是否一致，品种齐全"
					},
					"pcrd_t2": {
						name: "分餐或送餐人员穿戴是否整齐并干净整洁"
					},
					"pcrd_t3": {
						name: "用餐环境及餐具是否符合卫生标准"
					},
					"pcrd_t4": {
						name: "餐具是否经过消毒，表面是否干净无污染，无破损"
					},
					"pcrd_t5": {
						name: "饭菜中是否无异物"
					},
					"pcrd_t6": {
						name: "学生是否团结互助、有序打餐"
					},
					"pcrd_t7": {
						name: "学生是否爱粮节粮、勤俭节约"
					},
					"pcrd_t8": {
						name: "学生是否不挑食、不偏食，荤素搭配，合理膳食"
					},
					"pcrd_t9": {
						name: "学生是否做好垃圾分类"
					},
					"pcrd_t10": {
						name: "饭菜是否无异味"
					},
					"pcrd_t11": {
						name: "饭菜味道是否新鲜"
					},
					"pcrd_t12": {
						name: "饭菜温度是否适宜"
					},
					"pcrd_t13": {
						name: "学生对营养餐的评价和要求"
					},
					"pcrd_t14": {
						name: "学生反馈的其他意见"
					},
				}



			}
		},
		onLoad: function(params) {
			debugger
			this.c_index = params.c_index;
			this.initData();
		},
		methods: {
			initData() {
				var _this = this;
				var prePage = uni.getPrePage();
				var peicObj = prePage.peicrecordArr[_this.c_index];
				_this.peicObj = peicObj;
				console.log(_this.peicObj);
			},




		}
	}
</script>
<style>
	page {
		background: #F2F4F5;
	}

	.red {
		color: #F45357;
	}

	.weui-cell {
		padding: 15px;
	}

	.weui-cell .weui-cell__bd {
		font-size: 14px;
		color: #999999;
	}

	.weui-cell .weui-cell__ft {
		font-size: 14px;
		color: #303030;
	}

	.weui-cell:before {
		right: 15px;
	}

	.weui-cells:after {
		right: 15px;
		left: 15px;
	}

	.inputbg {
		text-align: left;
		background: #F8F8F8;
		border-radius: 3px;
		padding: 5px 10px;
		font-size: 24rpx;
		color: #303030;
		line-height: 60rpx;
		height: 60rpx;
		margin-top: 10px;

	}

	.uni-input {
		text-align: left;
		font-size: 24rpx;
		color: #999999;
		line-height: 60rpx;
		height: 60rpx;
	}

	.remarks-list {
		background: #fff;
		margin-top: 10px;
		padding: 15px;
	}

	.weui-uploader__input-box {
		width: 165rpx;
		background: #F7F9FC !important;
		height: 165rpx;
	}

	.weui-uploader__file {
		position: relative;
		width: 165rpx;
		height: 165rpx;
	}

	.weui-uploader__input-box {
		box-sizing: border-box;
		border: 1px solid #eef1f2;
		background: #f9f9f9;

	}

	.img-upload {
		width: 165rpx;
		height: 165rpx;

		display: block;

	}

	.img-close {
		width: 22rpx;
		height: 22rpx;
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		z-index: 111;
	}

	.uni-combox {
		border: none;
		line-height: 60rpx;
		height: 60rpx;
		padding: 0 8px 0 0;
	}

	.weui-uploader__input-box:after,
	.weui-uploader__input-box:before {
		background-color: transparent;
	}

	.total-tab {
		background: #fff;
		margin-top: 10px;

	}


	/deep/ .uni-table {
		min-width: auto !important;
	}

	.uni-table-td {
		line-height: 16px;
		font-size: 20rpx;
		color: #34495E;
		vertical-align: middle;
	}

	.uni-table-th {
		color: #34495E;
		font-weight: normal;
		font-size: 20rpx;
	}
</style>