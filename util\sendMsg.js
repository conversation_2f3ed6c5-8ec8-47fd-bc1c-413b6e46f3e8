// 模板注册器
var templateRegistry = (function () {
    var templates = {};

    return {
        register: function (xcxType, tplType, template) {
            if (!templates[xcxType]) {
                templates[xcxType] = {};
            }
            templates[xcxType][tplType] = template;
        },
        get: function (xcxType, tplType) {
            return templates[xcxType] && templates[xcxType][tplType];
        },
        getAll: function() {
            return templates;
        }
    };
})();

// 注册默认模板
templateRegistry.register("xcxschool", 1, {
    "touser": [],
    "template_id": "PQo5eYKIYEqIxIEKWCzd3LWEOXvGEnFaOdOxsvm4Y94",
    "data": {
        "time2": {
            "value": "00:00"
        },
        "thing3": {
            "value": "这是个消息内容"
        },
        "thing4": {
            "value": "这是一个备注哦。。"
        }
    }
});
templateRegistry.register("xcxhome", 1, {
    "touser": [],
    "template_id": "SxWEPUY0eaZxFKGt-uyy31-WiCQRVJ6YYczIcfr5XS8",
    "data": {
        "time2": {
            "value": "00:00"
        },
        "thing3": {
            "value": "这是个消息内容"
        },
        "thing4": {
            "value": "这是一个备注哦。。"
        }
    }
});

/**
 * 消息工具类
 */
const MessageUtil = {
    /**
     * 注册消息模板
     * @param {String} xcxType - 小程序类型
     * @param {Number} tplType - 模板类型
     * @param {Object} template - 模板内容
     */
    registerTemplate: function(xcxType, tplType, template) {
        templateRegistry.register(xcxType, tplType, template);
    },
    
    /**
     * 获取消息模板
     * @param {String} xcxType - 小程序类型
     * @param {Number} tplType - 模板类型
     * @returns {Object} 模板内容
     */
    getTemplate: function(xcxType, tplType) {
        return templateRegistry.get(xcxType, tplType);
    }, 
    //获取模板id
    getTemplateId: function(xcxType, tplType) {
        var template = templateRegistry.get(xcxType, tplType);
        if (!template) {
            return null;
        }
        return template.template_id;
    },
    
    /**
     * 发送消息
     * @param {String} xcxtype - 小程序类型，例如：xcxschool, xcxhome
     * @param {Number} tplType - 模板类型
     * @param {Array} openids - 接收者的openid数组
     * @param {Object} data - 消息数据，会与模板中的data合并
     * @param {String} link - 跳转链接
     * @param {String} tip - 自定义提示信息，默认为"发送成功"
     * @param {Function} callback - 回调函数，参数为(result, error)
     */
    sendMsg: function(xcxtype, tplType, openids, data, link, tip) {
        var template = templateRegistry.get(xcxtype, tplType);
        if (!template) {
            uni.msg("模板不存在");
            return;
        }

        var pushmsg = Object.assign({}, template); // 深拷贝模板
        pushmsg.touser = openids;
        pushmsg.type = xcxtype;
        pushmsg.data = Object.assign({}, template.data, data);
        if (link) {
            pushmsg.page = link;
        }
        
        // 根据环境判断是否发送测试版消息
        if (uni.globalData && uni.globalData.env === 'dev' || uni.globalData && uni.globalData.env === 'test') {
            pushmsg.mpstate = 1;  // miniprogram_state 小程序版本类型  正式版  测试版
        }

        uni.smaction(function (re, err) {
            if (err) {
                uni.msg("发送失败");
                return;
            }

            if (tip) {
                uni.msg(tip);
            } else {
                uni.msg("发送成功");
            }
        }, pushmsg, { 
            datastring: true, 
            route: uni.svs.zxx,
            action: "message/sendmsg/" + (xcxtype == 'xcxschool' ? 'school' : 'home'),
            method: "POST"
        });
    }, 
     	
};

// 同时支持 CommonJS 和 ES6 模块导出
// CommonJS 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MessageUtil;
}

// ES6 模块导出
export default MessageUtil
