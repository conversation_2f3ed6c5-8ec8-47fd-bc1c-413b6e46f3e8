<template>
	<view style="overflow-x: hidden; position: absolute;bottom: 0;width: 100%;height: 100%;">
		<image src="static/image/shareico.jpg" style="width: 100%;height: 100%; position: absolute;bottom: 0;">
		</image>
		<view class="login-container">
			<view class="weui-flexcen" style="justify-content: center;">
				<image :src="objStu.photopath ? ossPrefix + objStu.photopath : 'static/image/img_def.png'"
					style="position: absolute;top:-50rpx;border: 4rpx solid #33B596; width: 100rpx; height: 100rpx; border-radius: 50%;">
				</image>
			</view>
			<view style="text-align: center; padding-top: 76rpx;font-size: 32rpx;color: #303030;">{{objStu.stuname}}</view>
			<view style="text-align: center; font-size: 24rpx;color: #999999;">{{objStu.birthday | getAge}}</view>
			<view style="background: #F2F4F5;border-radius: 10rpx ;font-size: 28rpx;margin: 60rpx 0;">
				<view class="weui-cell">
					<view class="weui-cell__bd">
						<view style="width: 200rpx;">与宝宝的关系</view>
					</view>
					<view class="weui-cell__ft weui-cell__ft_in-access"> 
						<picker mode="selector" :range="arrptype" range-key="pname" :value="ptypeindex" @change="getptype" style="width:100%">
							{{arrptype[ptypeindex].pname || '请选择'}}
						</picker>
					</view>
				</view>
			</view>
			<button @click="btnBind" style="background: #33B596;border-radius: 6rpx;font-size: 24rpx;color: #FFFFFF; margin: 30rpx auto;">关联宝宝</button>
		</view>
	</view>

</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js")
	var app = getApp();
	
	export default {
		data() {
			return {
				ossPrefix: app.globalData.ossPrefix,
				myinfo: {},
				stuid: 0,
				inviterid: 0,
				objStu: {},
				arrptype: [
					{ptype: 1, pname: '爸爸'},
					{ptype: 2, pname: '妈妈'},
					{ptype: 3, pname: '爷爷'},
					{ptype: 4, pname: '奶奶'},
					{ptype: 5, pname: '姥爷'},
					{ptype: 6, pname: '姥姥'}
				],
				ptypeindex: -1,
				ptype: 1,
				isbind: false,
				objyeyarrstu: {}
			}
		},
		onLoad(params) {
			this.stuid = params.stuid;
			this.inviterid = params.inviterid;
			if (app.globalData.waitlogin) {
				// return uni.navigateTo({
				// 	url: "/pages/login/login?qrcodeuserid=" + (params.qrcodeuserid || '') + "&qrcodegiftcode=" + (params.qrcodegiftcode || '')
				// })
				console.log("onLoad:waitlogin", params);
				app.globalData.userInfoReadyCallback = this.initData;
			} else {
				console.log("onLoad:", params);
				this.initData();
			}
		},
		onShow() {
		
		},
		computed: {
		},
		filters: {
			getAge(birthday) {
				if(!birthday){
					return '';
				}
				return util.GetAge(birthday, new Date(), 'text', true);
			}
		},
		methods: {
			initData(){
				var _this = this;
				this.myinfo = app.globalData.objuserinfo;
				uni.sm(function(re, err){
					if(err){
						return uni.msg(err);
					}
					_this.objStu = re;
					_this.getBind(re.yeyid, re.stuno);
				}, ["stu.selectstubyid", this.stuid], {route: uni.svs.zxx_home_biz})
			},
			//查询当前学生与当前用户是否已存在关联关系
			getBind(yeyid, stuno){
				var _this = this;
				uni.sm(function(re, err){
					if(err){
						return uni.msg(err);
					}
					if(re.num > 0){
						_this.isbind = true;
					}
				}, ["userstu.select", yeyid, stuno, this.myinfo.id], {route: uni.svs.zxx_home_biz})
			},
			getptype(e){
				console.log(e);
				this.ptypeindex = e.detail.value;
				this.ptype = this.arrptype[e.detail.value].ptype;
			},
			btnBind(){
				if(this.isbind){
					return uni.msg("您已经关联了当前学生，请去我的孩子中查看");
				}
				var param = {
					yeyid: this.objStu.yeyid,
					stuno: this.objStu.stuno,
					xcxuserid: this.myinfo.id,
					ptype: this.ptype,
					method: 3,
					inviterid: this.inviterid
				}
				uni.sm(function(re, err){
					if(err){
						return uni.msg(err);
					}
					uni.msg('关联成功，请去我的孩子中查看');
					uni.reLaunch({
						url: '/pages/tabBar/home/<USER>'
					})
				}, ["userstu.saveuserstudent", JSON.stringify(param)], {route: uni.svs.zxx_home_biz})
			}
		}
	}
</script>


<style>
	.login-container {
		padding: 0px 20px;
		width: 600rpx;
		height: 504rpx;
		background: #FFFFFF;
		border-radius: 10rpx;

		margin: 200rpx auto;
		position: relative;

	}
</style>