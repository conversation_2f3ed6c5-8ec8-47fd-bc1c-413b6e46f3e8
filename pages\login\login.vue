<template>
	<view>
		<view style="margin: 100rpx 60rpx; text-align: center;" class="logoimg">
		<!-- 	<view style="font-size: 60rpx;color: #323334;font-weight: bold;">登录</view>
			<view style="font-size: 30rpx;color: #999999;margin-top: 30rpx;">为了你的账号安全，请绑定手机号</view> -->
			
			<image src="../login/static/image/logo.png" width="200rpx" height="200rpx"></image>
			
		</view>
		<view style="margin: 120rpx 60rpx 0 60rpx;">
			<view class="login-inp weui-flex" v-if="env_version!='release'">
				<input type="number" placeholder="请输入手机号" placeholder-style="color: #cccccc;" v-model="phone" @input="inputmobile" />
			</view>
			<view class="login-inp weui-flex" style="margin-top: 20rpx;" v-if="env_version!='release'">
				<input type="text" v-model="yanzhengmavalue" placeholder="请输入验证码" placeholder-style="color: #cccccc;" class="weui-flex__item" />
				<Verify @success='success' :mode="'pop'" :captchaType="'clickWord'" ref="verify" :imgSize="{width:'310px',height:'155px'}"></Verify>
				<!-- <Verify @success='success' :mode="'pop'" :captchaType="'clickWord'" ref="verify" :imgSize="{width:'310px',height:'155px'}"></Verify> -->
				<button v-if="yanzhengmabtndisabled" class="borderbtn noafter">{{yanzhengmabtntext}}</button>
				<button v-else class="borderbtn borblue noafter" @click="showVerify">获取验证码</button>
			</view>
			<view style="margin: 30rpx 0 0 5rpx;">
				<view class="weui-flex">
					<checkbox-group @change="checkboxChange" class="agreement_radio">
						<label>
							<checkbox :checked="checked" style="transform:scale(0.8)" />已阅读并同意
						</label>
						<text style="color: #C23C43;" @click="goagree()">《服务协议》</text>
					</checkbox-group>
				</view>
			</view>
			<button class="fullbtn noafter" style="margin-top: 70rpx;" @click="bindAndlogin()" v-if="env_version!='release'">立即登录</button>
			<!-- #ifdef MP-WEIXIN -->
			<button class="fullbtn noafter" style="margin-top: 70rpx;" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">手机号快捷登录</button>
			<!-- #endif -->
		</view>
	</view>
</template>

<script>
	import Verify from "@/components/verify/verify.vue"
	const app = getApp();

	export default {
		data() {
			return {
				env_version: app.globalData.env_version,
				t: null,
				phone: '',
				yanzhengmavalue: "",
				yanzhengmabtndisabled: true,
				yanzhengmabtntext: "获取验证码",
				objsendtime: 300,
				checked: false
			}
		},
		onLoad: async function(params) {
			this.qrcodeuserid = params.qrcodeuserid;
			this.qrcodegiftcode = params.qrcodegiftcode;
			// #ifdef MP-WEIXIN
			uni.checkSession({
				success: function(res) {
					if (res.errMsg != 'login:ok') {
						uni.login(); //重新登录　
					}
				},
				fail: function(res) {
					console.log(JSON.stringify(res));
					uni.login(); //重新登录　
				}
			});
			// await app.globalData.getOpenid();
			// #endif		
		},
		components: {
			Verify
		},
		methods: {
			showVerify() {
				this.$refs.verify.show();
			},
			success(data) {
				var _this = this;
				setTimeout(function() {
					_this.$refs.verify.hide();
				}, 1000);
				this.tapgetyanzhengma(data.captchaVerification);
			},
			inputmobile(e) {
				var txtmobile = e.detail.value;
				if (txtmobile && txtmobile.charAt(0) == 1 && txtmobile.length == 11) {
					this.yanzhengmabtndisabled = false
				} else {
					this.yanzhengmabtndisabled = true;
				}
			},
			getPhoneNumber: function(e) {
				if (!this.checked) {
					return uni.msg("请先勾选协议");
				}
				if (e.detail.errMsg == 'getPhoneNumber:ok') {
					uni.login({
						success: async (res) => {
							console.log(JSON.stringify(res));
							// e.detail.code = res.code;
							var param = {
								zhtype: app.globalData.xcxtype, 
								logintype: "loginByMobile",
								mobilecode: e.detail.code,
								logincode: res.code
							}
							app.globalData.applogin(param, function() {
								//todo 登录成功
								_logincallback();
							});
						}
					});
				} else {
					uni.msg("取消获取");
				}
			},
			//获取验证码
			tapgetyanzhengma: function(captchaVerification) {
				var _this = this;
				var txtmobile = this.phone;
				if (txtmobile && txtmobile.charAt(0) == 1 && txtmobile.length == 11) {
					uni.smaction(function(re, err) {
						if(err){
							_this.yanzhengmabtndisabled = false;
							console.log('系统错误', err)
							return;
						}
						_this.objphone = txtmobile;
						uni.msg("发送成功，");
						console.log("re:", re);
						if (_this.t) {
							clearInterval(_this.t);
						}
						_this.t = setInterval(function() {
							_this.objsendtime--;
							_this.yanzhengmabtndisabled = true;
							_this.yanzhengmabtntext = _this.objsendtime + ' 秒';
							if (_this.objsendtime == 0) {
								_this.yanzhengmabtndisabled = false;
								_this.yanzhengmabtntext = "获取验证码";
								clearInterval(_this.t);
								_this.objsendtime = 300;
							}
						}, 1000);
						// if (re.code == 200) {
							
						// } else if (re.code == 500) {
						// 	_this.yanzhengmabtndisabled = false
						// 	console.log(err)
						// } else {
						// 	_this.yanzhengmabtndisabled = false
						// 	console.log('系统错误')
						// }
					}, {mobile: txtmobile, captchaVerification: captchaVerification}, {route:  uni.svs.zxx_home_auth, action: 'loginsendvcode'});
				} else {
					return uni.msg("请输入正确的手机号");
				}
			},
			mobileCheck(mobile) {
			    var myreg = /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/;
			    if(mobile.length == 11){
			        if (!myreg.test(mobile)) {
			            return false;
			        } else {
			            return true;
			        }
			    }else{
			        return false;
			    }
			},
			//绑定并登陆
			bindAndlogin: function(yzm = true, logintype) {
				if (!this.checked) {
					return uni.msg("请先勾选协议");
				}
				if (!this.phone) {
					return uni.msg("请输入正确的手机号");
				}
				if(!this.mobileCheck(this.phone)){
					return uni.msg("请输入正确的手机号");
				}
				if (!this.yanzhengmavalue && yzm) {
					return uni.msg("请输入正确的验证码");
				}
				uni.showLoading({
					title: '登录中...',
				})
				var _this = this;
				var param = {
					zhtype: app.globalData.xcxtype, 
					logintype: "loginByVcode",
					mobile: this.phone,
					vcode: this.yanzhengmavalue
				}
				app.globalData.applogin(param, function() {
					//todo 登录成功
					uni.hideLoading();
					_logincallback();
				})
			},
			goagree: function() {
				uni.navigateTo({
					url: "../agree"
				});
			},
			checkboxChange(e) {
				if (!this.checked) {
					this.checked = true;
				} else {
					this.checked = false;
				}
			}
		}
	}

	function _logincallback() {
		uni.reLaunch({
			url: "/pages/tabBar/home/<USER>"
		});
		return;
		var PrePage = uni.getPrePage();
		if (PrePage) {
			PrePage.logincallback && PrePage.logincallback();
			uni.navigateBack();
		} else {
			uni.reLaunch({
				url: "/pages/tabBar/home/<USER>"
			});
		}
	}
</script>

<style>
	page {
		background: #ffffff;
	}

	#app,
	uni-app,
	uni-page,
	uni-page-wrapper {
		height: inherit;
	}

	.login-inp {
		align-items: center;
		border-bottom: 1px solid #f8f8f8;
		height: 70rpx;
		line-height: 70rpx;
		padding: 15rpx 0;
	}


	.tab-menu {
		display: flex;
	}

	.tab-item {
		width: 50%;
		box-sizing: border-box;
		height: 50rpx;
		line-height: 50rpx;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.tab-item:nth-child(1) {
		border-right: 1rpx solid #ccc;
	}

	.active {
		color: #007AFF;
	}

	.content {
		width: 700rpx;
		margin: 100rpx auto;
		/* border: 1rpx solid #007AFF; */
		padding: 10rpx;
	}

	.title {
		font-size: 35rpx;
		color: #808080;
	}

	.form {
		margin-top: 15rpx;
		padding: 10rpx;
	}

	.form-item {
		padding: 10rpx 10rpx 10rpx 0;
		margin-top: 10rpx;
	}

	.form-item input {
		width: 90%;
		border: 1rpx solid #ccc;
		height: 50rpx;
		line-height: 50rpx;
		padding: 10rpx;
		color: #C8C7CC;
	}

	.verify-btn {
		margin: 10rpx 0;
		width: 90%;
		padding-left: 10rpx;
	}

	.space {
		height: 5rpx;
		background-color: #ccc;
		margin: 30rpx 0;
	}

	uni-checkbox .uni-checkbox-input {
		width: 16px;
		height: 16px;
	}
	.borderbtn {
	    border: 1px solid #C23C43;
	    color: #C23C43;
	    background: #ffffff;
	   
	}
	.logoimg image{ width: 200rpx; height: 200rpx;}
</style>