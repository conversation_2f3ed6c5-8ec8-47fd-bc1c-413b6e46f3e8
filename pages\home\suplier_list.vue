<template>
	<view style="height:100%;overflow:hidden;">
		<z-paging-item ref="swiperItem" :currentIndex="current" @query="initData" @updateList="updateList">
			<view v-for="(item, index) in supplierArr" :key="item.id" @click="toPeicDetail(index,item)">
				<view class="weui-cells weui-cells_after-title">
					<view class="weui-cell">
						<view class="weui-cell__bd newstxt">供应商名称</view>
						<view class="weui-cell__ft weui-flex newsrttxt" style="align-items: center;">
							{{ item.sname }}
						</view>
					</view>
				</view>
				<view style="background: #fff;">
					<view style="font-size: 28rpx;
	 padding: 10px 0px 0 15px;" class="graytxt">供应商资质</view>
					<view class="photoimg weui-flex">
						<view v-if="item.licensepic">
							<image :src="ossPrefix + '/' + item.licensepic" style="width:100%; height: 82rpx;"></image>
						</view>
						<view v-if="item.licensepic2">
							<image :src="ossPrefix + '/' + item.licensepic2" style="width:100%; height: 82rpx;"></image>
						</view>
						<view v-if="item.licensepic3">
							<image :src="ossPrefix + '/' + item.licensepic3" style="width:100%; height: 82rpx;"></image>
						</view>
						<view v-if="item.licensepic4">
							<image :src="ossPrefix + '/' + item.licensepic4" style="width:100%; height: 82rpx;"></image>
						</view>
					</view>
				</view>
			</view>
		</z-paging-item>
	</view>
</template>

<script>
var moment = require("../../common/moment.min.js");
var util = require("../../common/util.js");
var app = getApp();
var pagesize = 10;

export default {
	data() {
		return {
			ossPrefix: app.globalData.ossPrefix,
			myinfo: app.globalData.objuserinfo,
			current: 0,
			supplierArr: []
		}
	},
	onLoad() {
		// this.initData();
	},
	methods: {
		initData(pageNo, pageSize, e) {
			var _this = this;
			var where = {};
			var offset = (pageNo - 1) * pageSize;
			uni.sm(function(re, err) {
				if (err) {
					return uni.msg(err);
				}
				console.log('re:', re);
				var supplierArr = _this.supplierArr;
				if(pageNo == 1){
					supplierArr = [];
				}
				supplierArr = supplierArr.concat(re);
				_this.supplierArr = supplierArr;
				_this.$refs.swiperItem.complete(re);
			}, ["supplier.getdetail", pageSize, offset], {route: uni.svs.zxx})
		},
		toPeicDetail(index, peiObj) {
			uni.navigateTo({
				url: "/pages/home/<USER>" + index
			})
		},
		updateList(data) {
			console.log("updateList");
			console.log(data);
			// 更新当前对应tab的数据，注意这里请用$set而非this.dataList[this.current]=data，因为需要触发列表渲染
			// this.$set(this.dataList, this.current, data);
		}



	}
}
</script>
<style>
	page {
		background: #F2F4F7;
	}



	.newstxt {
		font-size: 28rpx;
		color: #999999;
	}

	.newsrttxt {
		font-size: 28rpx;
		color: #303030;
	}

	.photoimg {
		background-color: #fff;
		padding: 20rpx 30rpx;
	}

	.photoimg view {
		width: 25%;
		margin-right: 10px;
	}

	.weui-cells:after,
	.weui-cells:before {
		left: 15px;
		right: 15px;
	}

	.weui-cells:before {
		border-top: none;
	}
</style>