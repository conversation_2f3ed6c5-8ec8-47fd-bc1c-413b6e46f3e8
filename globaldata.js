var dev = {
	ossPrefix: 'https://alot-zxx.oss-cn-beijing.aliyuncs.com/', 
	tbfile: 'https://tbfile.oss-cn-beijing.aliyuncs.com/',
	ossParam: '?x-oss-process=image/resize,h_150',
	ossParam2: '?x-oss-process=image/resize,m_fill,h_300,w_300',
	ossParam3: '?x-oss-process=image/resize,m_fill,h_40,w_33',
	requrl: 'http://192.168.1.8:8100', //2015  9013
	zxxurl: 'http://192.168.1.8:8118',//zxx pc端
	updir: 'zxxmini',
	xcxtype: 'xcxhome', //小程序类型 
	env_version: "develop", // 正式版为 release，体验版为 trial，开发版为 develop
	selfsevice: {
		"/zxx-home-auth": {
			"target": "http://192.168.1.8:8101",
		},
		"/zxx-home-biz": {
			"target": "http://192.168.1.8:8102",
		},
		"/zxx": {
			"target": "http://192.168.1.8:8018",
		}
	}
}

var test = {
	ossPrefix: 'https://alot-zxx.oss-cn-beijing.aliyuncs.com/', 
	tbfile: 'https://tbfile.oss-cn-beijing.aliyuncs.com/',
	ossParam: '?x-oss-process=image/resize,h_150',
	ossParam2: '?x-oss-process=image/resize,m_fill,h_300,w_300',
	ossParam3: '?x-oss-process=image/resize,m_fill,h_40,w_33',
	requrl: 'https://test-zxxhome.bt-z.com:553',
	zxxurl:'https://testzxx.bt-z.com',
	updir: 'zxxmini',
	xcxtype: 'xcxhome', //小程序类型
	env_version: "trial" // 正式版为 release，体验版为 trial，开发版为 develop
}

var prod = {
	ossPrefix: 'https://alot-zxx.oss-cn-beijing.aliyuncs.com/', 
	tbfile: 'https://tbfile.oss-cn-beijing.aliyuncs.com/',
	ossParam: '?x-oss-process=image/resize,h_150',
	ossParam2: '?x-oss-process=image/resize,m_fill,h_300,w_300',
	ossParam3: '?x-oss-process=image/resize,m_fill,h_40,w_33',
	requrl: 'https://zxxhome.bt-z.com',
	zxxurl:'https://zxx.bt-z.com',
	updir: 'zxxmini',
	xcxtype: 'xcxhome', //小程序类型
	env_version: "release" // 正式版为 release，体验版为 trial，开发版为 develop
}

global = process.env.ENV_TYPE === 'test' ? test : (process.env.NODE_ENV === 'development' ? dev : prod);
module.exports = global;