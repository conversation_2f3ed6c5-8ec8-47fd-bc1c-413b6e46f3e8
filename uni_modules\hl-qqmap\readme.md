
<h1>使用方法</h1>

![](https://mp-679c1e1a-9c66-4787-b8be-aed112e62bf3.cdn.bspapp.com/pics/WechatIMG01.jpg)

![](https://mp-679c1e1a-9c66-4787-b8be-aed112e62bf3.cdn.bspapp.com/pics/55.jpg)

![](https://mp-679c1e1a-9c66-4787-b8be-aed112e62bf3.cdn.bspapp.com/pics/44.jpg)

<h3>注意：在h5前端的话要跨域否则获取不了定位的信息，微信小程序无需跨域。另外还需腾讯地图那申请mapkey(demo上是我自己申请的，如果该插件用在项目上，建议用户重新申请mapkey)</h3>
<h3>配置easycom规则后，自动按需引入，无需import组件，直接引用即可，目前只是做了个比较简单demo,后续会继续扩展；locPath这个是中心图片最好设置下，不设置图片路径视觉较差</h3>
<h3>近期老是有人问我怎么做一版小程序较好的位置定位功能。所以为了照顾下做微信小程序的同学，所以选择用腾讯地图做了一版地图位置定位,滑动地图中心点获取位置信息，这个版本加入城市选择器，关键字搜索等功能，希望大家支持该插件；</h3>
<h3>如果想接入高端地图的话可以在评论区@我，如果需求量大的话我会做一版高德，帮大家解决问题；</h3>
<h3>另外路过同学使用了该组件，有钱的给个赏，没钱的点个赞，我会继续更新后面常规项目需求的一些组件；</h3>