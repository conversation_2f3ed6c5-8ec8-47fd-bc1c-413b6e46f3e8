<template>
	<view style="width: 100%;position: relative;">
		<fixed-nav-bar v-if="scrollChange" :navArr=dataArray :tabCurrentIndex="currentIndex" 
		@navbarTap="navbarTapHandler" :scrollChangeIndex="currentI"></fixed-nav-bar>
	</view>
</template>

<script>
	import fixedNavBar from './hl-navbar-fixed.vue'
	
	export default {
		props:{
			currentI : {
				type: Number,
				default:0
			},
			scrollChange : {
				type: Boolean,
				default:false
			},
			dataArray : {
				type: Array,
				default () {
					return [
						{title: '待接单',type: 'await'},
						{title: '制作中',type: 'making'},
					];
				}
			},
		},

		components: {
			fixedNavBar
		},
		data () {
			return {
				currentIndex: 0,
				isFixed: false,
				topHeight: 0
			}
		},
		onLoad (options) {
			uni.setNavigationBarTitle({
				title: options.title
			});
			this.calculateTopSectionHeight();
		},
		methods: {
			navbarTapHandler (index) {
				this.currentIndex = index;
				this.$emit('currentIndex',index)
			},
			scrollChnage (e) {
				let top = e.detail.scrollTop;
				if (top >= this.topHeight) {
					this.isFixed = true;
				} else {
					this.isFixed = false;
				}
			},
			/**
			 * 计算头部视图的高度
			 */
			calculateTopSectionHeight () {
				var that = this;
				let topView = uni.createSelectorQuery().select(".top-section");
				topView.fields({
					size: true
				}, data => {
					that.topHeight = data.height;
				}).exec();
			}
		},
		watch:{
			currentI:function(newVal){
				this.navbarTapHandler(newVal)
			}
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	.scroll ::-webkit-scrollbar {
	  display: none;
	  width: 0;
	  height: 0;
	  -webkit-appearance: none;
	  background: transparent;
	  color: transparent;
	}

	
	
	.scroll {
		display: flex;
		position: relative;
		width: 100%;
		height: 100%;
		.navbar-fixed-section {
			display: flex;
			width: 100%;
			height: 100%;
		}
		
		.top-section {
			height: 350upx;
			background-color: green;
		}
		
		.bottom-section {
			height: 1500upx;
			background-color: yellow;
		}
		
		.footer-section {
			height: 1500upx;
			background-color: blue;
		}
	}
</style>
