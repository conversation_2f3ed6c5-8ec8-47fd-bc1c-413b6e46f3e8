<template>
	
	<view>
		  <view class="info-mess">
		        <view class="weui-flexcen"><image src="static/image/prompt.png" style="width:31rpx;height: 30rpx;"></image>输入正确的孩子信息，才能关联成功哦~</view>
		    </view>
		
		<view class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
				<view class="weui-cell__bd weui-flexcen"><image src="static/image/addbobyico1.png" style="width:31rpx;height: 30rpx;"></image>{{yeyname}}</view>
				
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd weui-flexcen">
					<image src="static/image/addbobyico2.png" style="width:31rpx;height: 30rpx;"></image>
					<input @input="getstuname" placeholder-class="phcolor" class="weui-input weui-flex__item" placeholder="请输入孩子姓名"
						style="font-size: 14px" />
				</view>
				
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd weui-flexcen">
					<image src="static/image/addbobyico3.png" style="width:31rpx;height: 30rpx;"></image>
					<picker mode="date" @change="getbirthday" style="width:100%">
						{{birthday || '请选择孩子生日'}}
					</picker>
				</view>
				<view class="weui-cell__ft"><image src="static/image/dateimg.png" style="width:31rpx;height: 30rpx;"></image></view>
			</view>
			<view class="weui-cell">
				<view class="weui-cell__bd weui-flexcen">
					<image src="static/image/addbobyico4.png" style="width:31rpx;height: 30rpx;"></image>
					<picker mode="selector" :range="arrptype" range-key="pname" :value="ptypeindex" @change="getptype" style="width:100%">
						{{arrptype[ptypeindex].pname || '请选择身份'}}
					</picker>
				</view>
				<view class="weui-cell__ft weui-cell__ft_in-access"></view>
			</view>
		</view>
		    <!--  添加性别-->
		    <view class="mine-top">
		        <view class="weui-flex func-list whitebg" id="selSex">
		            <view class="weui-flex__item" @click="changeSex('男')">
		                <view class="placeholder">
							<image v-if="sex == '男'" src="static/image/boysexHL.png" style="width:49rpx;height: 48rpx;"></image>
							<image v-else src="static/image/boysex.png" style="width:49rpx;height: 48rpx;"></image>
		                    <text>男孩</text>
		                </view>
		            </view>
		            <view class="weui-flex__item" @click="changeSex('女')">
		                <view class="placeholder">
							<image v-if="sex == '女'" src="static/image/girlsexHL.png" style="width:49rpx;height: 48rpx;"></image>
							<image v-else src="static/image/girlsex.png" style="width:49rpx;height: 48rpx;"></image>
		                    <text>女孩</text>
		                </view>
		            </view>
		        </view>
		    </view>	
			
			<!-- 提交按钮 -->
			<view class="button-bottom " style="height: 160rpx;box-shadow: none;border-top:none;">
				<button class="common-btn noafter" @click="addStudent">关联学生</button>
			</view>
			
	</view>
			
	
</template>

<script>
	var moment = require("../../common/moment.min.js");
	var util = require("../../common/util.js")
	var app = getApp();
	var pagesize = 10;
	
	export default {
		data() {
			return {
				yeyid: 0,
				yeyname: '',
				guid: '',
				stuname: '',
				birthday: '',
				ptype: 1,
				ptypeindex: -1,
				arrptype: [
					{ptype: 1, pname: '爸爸'},
					{ptype: 2, pname: '妈妈'},
					{ptype: 3, pname: '爷爷'},
					{ptype: 4, pname: '奶奶'},
					{ptype: 5, pname: '姥爷'},
					{ptype: 6, pname: '姥姥'}
				],
				sex: '男'
			}
		},
		onLoad(params) {
			console.log(params);
			this.yeyid = params.yeyid;
			this.yeyname = params.yeyname;
			this.guid = params.guid;
		},
		onShow() {
		
		},
		methods: {
			getstuname(e){
				console.log(e);
				this.stuname = e.detail.value;
			},
			getbirthday(e){
				console.log(e);
				this.birthday = e.detail.value;
			},
			getptype(e){
				console.log(e);
				this.ptypeindex = e.detail.value;
				this.ptype = this.arrptype[e.detail.value].ptype;
			},
			changeSex(sex){
				this.sex = sex;
			},
			addStudent(){
				var _this = this;
				if(!this.stuname){
					return uni.msg('请输入孩子姓名');
				}
				if(!this.birthday){
					return uni.msg('请选择孩子生日');
				}
				if(!this.sex){
					return uni.msg('请选择孩子性别');
				}
				var curusermobile = app.globalData.objuserinfo.mobile;//当前用户的手机号
				//1、根据条件查询学生信息
				uni.sm(function(re, err){
					if(err){
						return uni.msg(err);
					}
					//classno,claname,stuno,stuname,sex,photopath,birthday,intime,filenumber,faphone,mophone
					if(re.length == 0){//
						return uni.msg('没有查到孩子信息，请确认您输入的信息是否有误');
					}
					_this.objstu = re;
					//2、判断当前登录用户手机号是否与学生信息中的父亲或母亲手机号匹配
					if(re.faphone == curusermobile || re.mophone == curusermobile){//匹配，直接绑定
						_this.startBind(1, function(){
							uni.reLaunch({
								url: "/pages/tabBar/home/<USER>"
							})
						});
					} else {//不匹配，跳转到验证页面，验证成功后再返回此页面绑定
						uni.navigateTo({
							url: "/pages/mine/safe_verify?faphone=" + re.faphone + "&mophone=" + re.mophone
						})
					}
				}, ["stu.selectstu", this.stuname, this.birthday, this.sex, this.guid], {route:uni.svs.zxx})
			},
			startBind(method, cb){
				uni.showLoading();
				var _this = this;
				//1、查询家长端数据库中是否已经有学生信息
				uni.sm(function(re, err){
					if(err){
						uni.hideLoading();
						return uni.msg(err);
					}
					// var objstu = {
					// 	yeyid: _this.yeyid,
					// 	yeyname: _this.yeyname,
					// 	yguid: _this.guid,
					// 	classno: _this.objstu.classno,
					// 	claname: _this.objstu.claname,
					// 	stuno: _this.objstu.stuno,
					// 	stuname: _this.objstu.stuname,
					// 	sex: _this.objstu.sex,
					// 	photopath: _this.objstu.photopath,
					// 	birthday: _this.objstu.birthday,
					// 	intime: _this.objstu.intime,
					// 	filenumber: _this.objstu.filenumber
					// }
					var objstu = _this.objstu;
					objstu.yeyid = _this.yeyid;
					objstu.yeyname = _this.yeyname;
					objstu.yguid = _this.guid;
					if(re.id){//学生信息已经存在
						//2、更新学生信息
						uni.sm(function(re2,err2){
							if(err2){
								uni.hideLoading();
								return uni.msg(err2);
							}
							_this.addBindInfo(method, re.id, cb);
						}, ["stu.updatestudent", JSON.stringify(objstu), uni.msgwhere({id: [re.id]})], {route:uni.svs.zxx_home_biz})
					} else {//学生信息还未存在
						//2、保存学生信息
						uni.sm(function(re2,err2){
							if(err2){
								uni.hideLoading();
								return uni.msg(err2);
							}
							_this.addBindInfo(method, re2, cb);
						}, ["stu.savestudent", JSON.stringify(objstu)], {route:uni.svs.zxx_home_biz})
					}
				}, ["stu.selectstuid", _this.yeyid, _this.objstu.stuno], {route:uni.svs.zxx_home_biz})
			},
			addBindInfo(method, stuid, cb){//添加当前用户与学生的关联关系
				var _this = this;
				//查询关联关系是否已经存在
				uni.sm(function(re, err){
					if(err){
						uni.hideLoading();
						return uni.msg(err);
					}
					if(!re.id){//关联关系不存在，则保存关联关系
						var obj = {
							yeyid: _this.yeyid,
							stuno: _this.objstu.stuno,
							xcxuserid: app.globalData.objuserinfo.id,
							ptype: _this.ptype,
							method: method
						}
						uni.sm(function(re2, err2){
							if(err2){
								uni.hideLoading();
								return uni.msg(err2);
							}
							_this.changeCurBindStu(stuid, cb);
						}, ["userstu.saveuserstudent", JSON.stringify(obj)], {route:uni.svs.zxx_home_biz})
					} else {//关联关系已存在，则直接切换当前用户关联学生
						_this.changeCurBindStu(stuid);
					}
				}, ["userstu.selectid", _this.yeyid, _this.objstu.stuno, app.globalData.objuserinfo.id], {route:uni.svs.zxx_home_biz})
			},
			changeCurBindStu(stuid, cb){//切换当前关联学生
				var _this  = this;
				uni.smaction(function(re, err){
					uni.hideLoading();
					if(err){
						return uni.msg(err);
					}
					app.globalData.objuserinfo.stu_id = stuid;
					app.globalData.objuserinfo.yeyid = _this.yeyid;
					app.globalData.objuserinfo.yeyname = _this.yeyname;
					app.globalData.objuserinfo.guid = _this.guid;
					app.globalData.objuserinfo.classno = _this.objstu.classno;
					app.globalData.objuserinfo.claname = _this.objstu.claname;
					app.globalData.objuserinfo.stuno = _this.objstu.stuno;
					app.globalData.objuserinfo.stuname = _this.objstu.stuname;
					app.globalData.objuserinfo.stusex = _this.objstu.sex;
					app.globalData.objuserinfo.photopath = _this.objstu.photopath;
					app.globalData.objuserinfo.birthday = _this.objstu.birthday;
					app.globalData.objuserinfo.intime = _this.objstu.intime;
					app.globalData.objuserinfo.filenumber = _this.objstu.filenumber;
					cb && cb();
				}, {stuid: stuid}, {route:uni.svs.zxx_home_biz, action: 'mine/changestu'})
			}
		}
	}
</script>

<style>
	page{background: #F2F4F5;}
	
	.mine-top{background: #fff; margin-top: 10rpx;}


	.func-list .weui-flex__item .placeholder{text-align: center;}
	.func-list .weui-flex__item .placeholder img{height: 17.5px;}
	.func-list .weui-flex__item .placeholder text{font-size: 12px; display: block;}
	.mine-top .func-list .weui-flex__item:nth-child(1) .placeholder:after{content: "";position: absolute;right:0;top:50%;margin-top:-15px;height: 30px;border-right: 1px solid #E8E8E8;}
	.mine-top .func-list{padding: 15px 0; cursor: pointer}
	.mine-top .func-list .weui-flex__item{position: relative;}
	
	.weui-cell {
		padding: 15px;
	}
	
	.weui-cell .weui-cell__bd {
		font-size: 14px;
		color: #303030;
	}
	
	.weui-cell .weui-cell__ft {
		font-size: 14px;
		color: #999999;
	}
	
	.weui-cell:before {
		right: 15px;
	}
	
	.weui-cells:after {
		right: 15px;
		left: 15px;
	}
	.info-mess{font-size:26rpx;background:#F4F5F7;height: 80rpx;line-height: 80rpx;padding: 0 26rpx;white-space: nowrap;overflow: hidden; color: #666666}
	.info-mess image,.weui-cell__bd image{margin-right:10rpx;vertical-align:middle;}
	
</style>