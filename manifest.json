{
    "name" : "食安童健",
    "appid" : "__UNI__58A11E2",
    "description" : "",
    "versionName" : "1.0.2",
    "versionCode" : "100",
    "transformPx" : false,
    "app-plus" : {
        /* 5+App特有相关 */
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {},
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios" : {},
            /* ios打包配置 */
            "sdkConfigs" : {}
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        "appid" : "wx8a5b6177c45ed794",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "requiredPrivateInfos" : [
            "getLocation",
            "chooseLocation",
            "onLocationChange",
            "startLocationUpdateBackground"
        ],
        "permission" : {},
        "optimization" : {
            "subPackages" : true
        }
    },
    "h5" : {
        "router" : {
            "base" : ""
        },
        "devServer" : {
            "disableHostCheck" : false, //禁止访问本地host文件
            "port" : 8085, //修改项目端口
            "proxy" : {
                "/zxx-home-auth" : {
                    "target" : "http://***********:8101",
                    "changeOrigin" : true,
                    /**重写路径**/
                    "pathRewrite" : {
                        "^/zxx-home-auth" : "/"
                    }
                },
                "/zxx-home-biz" : {
                    "target" : "http://***********:8102",
                    "changeOrigin" : true,
                    /**重写路径**/
                    "pathRewrite" : {
                        "^/zxx-home-biz" : "/"
                    }
                },
                "/zxx" : {
                    "target" : "http://***********:8018",
                    "changeOrigin" : true,
                    /**重写路径**/
                    "pathRewrite" : {
                        "^/zxx" : "/"
                    }
                }
            }
        },
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "HUSBZ-552W6-PBHST-EDDJQ-YWOZJ-43FAI"
                }
            }
        }
    },
    "vueVersion" : "2"
}
