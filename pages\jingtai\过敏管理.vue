<template>
	<view>
			<view class="area-con">
						<view class="area-left food-txt">
							<view class="sel-pro"><image src="static/image/re_icon1.png" style="width:28rpx;height: 28rpx;"></image><text>谷类及制品</text></view>
							<view><image src="static/image/re_icon10.png" style="width:28rpx;height: 28rpx;"></image><text>薯类、淀粉及制品</text></view>
							<view><image src="static/image/re_icon2.png" style="width:28rpx;height: 28rpx;"><text>干豆类及制品</text></view>
							<view><image src="static/image/re_icon7.png" style="width:28rpx;height: 28rpx;"></image><text>蔬菜类及制品</text></view>
							<view><image src="static/image/re_icon11.png" style="width:28rpx;height: 28rpx;"></image><text>菌藻类</text></view>
							<view><image src="static/image/re_icon9.png" style="width:28rpx;height: 28rpx;"></image><text>水果类及制品</text></view>
							<view><image src="static/image/re_icon5.png" style="width:28rpx;height: 28rpx;"></image><text>坚果、种子类</text></view>
							<view><image src="static/image/re_icon18.png" style="width:28rpx;height: 28rpx;"></image><text>畜肉类及制品</text></view>
							<view><image src="static/image/re_icon6.png" style="width:28rpx;height: 28rpx;"></image><text>禽肉类及制品</text></view>
							<view><image src="static/image/re_icon3.png" style="width:28rpx;height: 28rpx;"></image><text>乳类及制品</text></view>
							<view><image src="static/image/re_icon4.png" style="width:28rpx;height: 28rpx;"></image><text>蛋类及制品</text></view>
						</view>
						<view class="area-right food-txt">
							<view class="weui-flexcen"><text>全选</text><label class="weui-flex checkbox_style checkbox_blue" style="align-items: center;margin-left: 44rpx;width: auto;"><checkbox style="width: 30rpx;height: 30rpx;left: -44rpx;top: -30rpx;"></checkbox>
														</label>
													
														</view>
							<view class="weui-flexcen"><text>小麦粉（标准粉）小麦粉（标准粉）</text><label class="weui-flex checkbox_style checkbox_blue" style="align-items: center;margin-left: 44rpx;width: auto;"><checkbox style="width: 30rpx;height: 30rpx;left: -44rpx;top: -30rpx;"></checkbox>
							</label>
						
							</view>
								<view class="weui-flexcen"><text>挂面</text><label class="weui-flex checkbox_style checkbox_blue" style="align-items: center;margin-left: 44rpx;width: auto;"><checkbox style="width: 30rpx;height: 30rpx;left: -44rpx;top: -30rpx;"></checkbox>
														</label>
													
														</view>
							<view><label><text>坚果、种子类</text><input type="checkbox"></label></view>
							<view><label><text>小麦</text><input type="checkbox"></label></view>
							<view><label><text>小麦粉（标准粉）</text><input type="checkbox"></label></view>
							<view><label><text>挂面</text><input type="checkbox"></label></view>
							<view><label><text>坚果、种子类</text><input type="checkbox"></label></view>
							<view><label><text>小麦</text><input type="checkbox"></label></view>
							<view><label><text>小麦粉（标准粉）</text><input type="checkbox"></label></view>
							<view><label><text>挂面</text><input type="checkbox"></label></view>
							<view><label><text>坚果、种子类</text><input type="checkbox"></label></view>
						</view>
					</view>
					
			<view class="bot-sel weui-flexcen" style="justify-content: space-between"><view>已选：<text class="redtxtgl">1</text>种食物（已有<text class="redtxtgl">1</text>种)</view><view class="btnqd">确定</view></view>
		
		</view>
</template>

<script>
</script>

<style>

.area-con {border-top:1px solid #eeeeee;text-align:left;font-size: 0;position: absolute;bottom: 98rpx;left:0;right:0;background: #fff; top: 0px;}

.area-left,.area-right{display: inline-block;width: 50%;font-size: 30rpx;vertical-align: top;}
.area-right{background: #F8F8F8;}
.area-left view,.area-right view{height: 87rpx;line-height: 87rpx;padding-left: 30rpx; position: relative;}

.area-left, .area-right {
  overflow-y: auto;
  overflow-x: hidden;    
  height: 100%;
  border: 0;
}
.area-con ul li.unpro{color: #999999;text-indent: 0.7rem;}
.area-left view.sel-pro{background: #F8F8F8; color: #C23C43; font-weight: bold;}
.area-left view.sel-pro:before{content: "";position: absolute;left:0;border-left: 4px solid #C23C43;height: 37rpx; top:24rpx;}	
.area-con .area-left{background: #FFFFFF;}
.area-con .area-left view{/*border-bottom: 1px solid #dddddd;*/cursor: pointer;}
.area-con .area-left view:last-child{border-bottom: none;}
.area-left uni-image{ margin-right: 10rpx; vertical-align: top;margin-top:32rpx;}

.area-left text {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: inline-block;
    width: 75%;
}

.area-right text {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: inline-block;
    width: 70%;
    padding-left: 5px;
}


 .bot-sel{background: #ffffff;height: 98rpx;line-height:98rpx;position: absolute;
    bottom: 0;
    width: 100%;box-shadow: 0px 0px 16px 0px rgba(194,60,67,0.12);}
.bot-sel view{margin-left: 20rpx;font-size:30rpx;}
 .bot-sel .redtxtgl{color: #F95E28;}
 .bot-sel .btnqd{margin-right: 15px; border-radius: 10rpx; height: 55rpx;line-height:55rpx; display: inline-block;background: #C23C43;width:130rpx;text-align: center;color: #fff;font-size: 28rpx;}

</style>