<template>
	<view class="container">
		<uni-file-picker 
			v-model="imageValue"
			fileMediatype="image"
			mode="grid"
			:limit="9"
			@select="select"
		></uni-file-picker>
		<button type="primary" @click="btnok">确定</button>
	</view>
</template>

<script>
	import uploadUtil from '@/common/uploadUtil.js';
	var app = getApp();
	
	export default {
		data() {
			return {
				imageValue: [],
				imgsArr:[]
			}
		},
		methods: {
			// 选择文件后触发
			async select(e){
				var _this = this;
				try { 
					// 调用上传接口
					uploadUtil.uploadServer({
						arrfile: e.tempFiles,
						// #ifdef H5
						serverurl: "/" +uni.svs.zxx+ '/ueditorController?Authorization=Bearer ' + uni.getStorageSync('token') + '&action=uploadfile&upRePath=unihome/users/',// 文件接收服务端

						// #endif
						// #ifndef H5
						serverurl: app.globalData.geturl(uni.svs.zxx, "ueditorController") + '?Authorization=Bearer ' + uni.getStorageSync('token') + '&action=uploadfile&upRePath=unihome/users/',// 文件接收服务端
						// #endif
						complete: function (arrpath) {
							console.log('arrpath', arrpath);
							var idcardphoto = arrpath[0].key;
							_this.imgsArr.push(idcardphoto);
							uni.msg("上传成功");
							
							 

						}
					})	

				} catch (error) {
					console.error('上传失败：', error)
					uni.showToast({
						title: '上传失败',
						icon: 'error'
					})
				}
			},
			// 获取上传进度
			progress(e){
				console.log('上传进度：',e)
			},
			
			// 上传成功
			success(e){
				console.log('上传成功')
			},
			
			// 上传失败
			fail(e){
				console.log('上传失败：',e)
			},
			btnok() {
				if (this.imgsArr.length == 0) {
					return uni.msg("请选择图片");
				}
				//todo 再次跳转到webview页面 带上图片路径参数
				uni.redirectTo({
					url:'/pages/home/<USER>' + encodeURIComponent(this.imgsArr.join(','))
				})
			}
		}
	}
</script>

<style lang="scss">
	.container {
		padding: 20px;
		background-color: #fff;
	}
</style>
