<template>
	<view class="main-view">
		<HLNavbar class="top-navbar" @currentIndex="setIndex" :currentI="thisindex" :dataArray="dataArray"
			:scrollChange="true"></HLNavbar>
		<swiper class="content-swiper" :duration="500" :current="thisindex" :data-index='thisindex' @change="toggle">
			<swiper-item class="swiper-item">
				<scroll-view class="list-view" scroll-y>
					<view class="provice-item" v-for="(item,index) in provinceList" :key="index"
						:class="{'active' : provinceModel != null && item.value == provinceModel.value}"
						@click="didSelectorToProvince(index)">
						<text>{{item.province}}</text>
					</view>
				</scroll-view>
			</swiper-item>
			<swiper-item class="swiper-item">
				<scroll-view class="list-view" scroll-y>
					<view class="city-item" v-for="(item,index) in cityList" :key="index"
						:class="{'active' : cityModel != null && item.value == cityModel.value}"
						@click="didSelectorToCity(index)">
						<text>{{item.city}}</text>
					</view>
				</scroll-view>

			</swiper-item>
			<swiper-item class="swiper-item">
				<scroll-view class="list-view" scroll-y>
					<view class="area-item" v-for="(item,index) in areaList" :key="index"
						:class="{'active' : areaModel != null && item.value == areaModel.value}"
						@click="didSelectorToDistrict(index)">
						<text>{{item.area}}</text>
					</view>
				</scroll-view>
			</swiper-item>
		</swiper>
		<view class="bottom-view">
			<button class="btn cancel" @click="cancel">取消</button>
			<button class="btn certain" @click="certain">确定</button>
		</view>
	</view>
</template>

<script>
	import {
		provinceList
	} from './config/map-service.js'
	import HLNavbar from './hl-navbar.vue'
	export default {
		components: {
			HLNavbar
		},

		data() {
			return {
				target: 0,
				thisindex: 0,

				dataArray: [{
						title: '省',
						id: 1
					},
					{
						title: '市',
						id: 2
					},
					{
						title: '县/区',
						id: 3
					},
				],
				provinceList: [],
				provinceModel: null,

				cityList: [],
				cityModel: null,

				areaList: [],
				areaModel: null

			}
		},

		mounted() {
			this.provinceList = provinceList
			let i = 0;
		},

		watch: {
			target(val) {
				this.setIndex(val)
			},
		},

		methods: {
			toggle(e) {
				let index = e.detail.current
				this.target = index
			},
			//点击nav控制下面的展示
			setIndex(e) {
				this.thisindex = e
			},

			didSelectorToProvince: function(index) {
				this.provinceModel = this.provinceList[index]
				this.cityList = this.provinceModel.data
				this.setIndex(1)
			},

			didSelectorToCity: function(index) {
				this.cityModel = this.cityList[index]
				this.areaList = this.cityModel.dataList
				this.setIndex(2)
			},

			didSelectorToDistrict: function(index) {
				this.areaModel = this.areaList[index]
			},
			
			cancel : function(){
				this.$emit('citySelector',{result : false})
			},
			
			certain : function(){
				let city = null
				if(this.areaModel != null){
					city = this.areaModel.area
				}else if(this.cityModel != null){
					city = this.cityModel.city
				}else if(this.provinceModel != null){
					city = this.provinceModel.province
				}
				if(city == null){
					uni.showToast({
						icon:'none',
						title: '选择不能为空'
					})
				}else{
					this.$emit('citySelector',{result : true, city : city})
				}
			}
		}

	}
</script>

<style lang="scss" scoped>
	$uni-color-theme : #007aff !default 
	
	button::after{ border: none;} 
	
	.main-view {
		display: flex;
		flex-direction: column;
		background-color: #FFF;
		height: 420px;

		.top-navbar {
			height: 48px;
		}

		.content-swiper {
			flex: 1;
			position: relative;
		}


		.content-view {
			display: flex;
			flex-direction: column;
		}

		.list-view {
			flex: 1;
			height: 100%;

			.provice-item,
			.city-item,
			.area-item {
				padding: 10px 20px;

				&.active {
					color: $uni-color-theme;
				}
			}
		}
		
		.bottom-view{
			display: flex;
			flex-direction: row;
			height: 56px;
			align-items: center;
			justify-content: center;
			font-size: 16px;
			padding: 10px;
			.btn{
				flex: 1;
				background-color: transparent;
				&.cancel{
					border: 1px solid $uni-color-theme;
					color: $uni-color-theme;
				}
				&.certain{
					background-color: $uni-color-theme;
					color: #FFF;
					margin-left: 10px;
				}
			}
		}
	}

	/deep/ ::-webkit-scrollbar {
		display: block;
		width: 4px !important;
		height: 1px !important;
		overflow: auto !important;
		background: transparent !important;
		-webkit-appearance: auto !important;
	}
</style>